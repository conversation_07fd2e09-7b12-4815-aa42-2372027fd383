import 'package:note_x/base/base.dart';
import 'base_data_mapper.dart';

  class FlashcardSetDataMapper
    extends BaseDataMapper<FlashcardSetDataDto, FlashcardSetDataModel> {
  @override
  FlashcardSetDataModel mapToEntity(FlashcardSetDataDto data) {
    return FlashcardSetDataModel(
      setId: data.setId,
      name: data.name,
      difficulty: data.difficulty,
      cardsCount: data.cardsCount,
    );
  }

  @override
  FlashcardSetDataDto mapToDto(FlashcardSetDataModel entity) {
    return FlashcardSetDataDto(
      setId: entity.setId,
      name: entity.name,
      difficulty: entity.difficulty,
      cardsCount: entity.cardsCount,
    );
  }
}
