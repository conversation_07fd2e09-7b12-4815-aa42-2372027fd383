// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mindmap_cache_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MindMapCacheModelAdapter extends TypeAdapter<MindMapCacheModel> {
  @override
  final int typeId = 10;

  @override
  MindMapCacheModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MindMapCacheModel(
      noteId: fields[0] == null ? '' : fields[0] as String,
      formatUrls:
          fields[1] == null ? {} : (fields[1] as Map).cast<String, String>(),
    );
  }

  @override
  void write(BinaryWriter writer, MindMapCacheModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.noteId)
      ..writeByte(1)
      ..write(obj.formatUrls);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MindMapCacheModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
