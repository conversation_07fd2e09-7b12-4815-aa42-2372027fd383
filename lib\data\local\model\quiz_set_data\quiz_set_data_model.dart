import 'package:hive/hive.dart';

import '../../../../services/hive/hive_type_id.dart';

part 'quiz_set_data_model.g.dart';

@HiveType(typeId: HiveTypeId.quizSetDataId)
class QuizSetDataModel extends HiveObject {
  @HiveField(0, defaultValue: '')
  String setId;

  @HiveField(1, defaultValue: '')
  String name;

  @HiveField(2, defaultValue: 0)
  int questionsCount;

  @HiveField(3, defaultValue: '')
  String difficulty;

  QuizSetDataModel({
    required this.setId,
    required this.name,
    required this.difficulty,
    this.questionsCount = 0,
  });

  QuizSetDataModel copyWith({
    String? setId,
    String? name,
    String? difficulty,
    int? questionsCount,
  }) {
    return QuizSetDataModel(
      setId: setId ?? this.setId,
      name: name ?? this.name,
      difficulty: difficulty ?? this.difficulty,
      questionsCount: questionsCount ?? this.questionsCount,
    );
  }
}
