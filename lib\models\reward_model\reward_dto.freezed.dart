// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reward_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

RewardDto _$RewardDtoFromJson(Map<String, dynamic> json) {
  return _RewardDto.fromJson(json);
}

/// @nodoc
mixin _$RewardDto {
  @JsonKey(name: 'referrer_reward')
  int get referrerReward => throw _privateConstructorUsedError;
  @JsonKey(name: 'referred_reward')
  int get referredReward => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardDtoCopyWith<RewardDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardDtoCopyWith<$Res> {
  factory $RewardDtoCopyWith(RewardDto value, $Res Function(RewardDto) then) =
      _$RewardDtoCopyWithImpl<$Res, RewardDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'referrer_reward') int referrerReward,
      @JsonKey(name: 'referred_reward') int referredReward});
}

/// @nodoc
class _$RewardDtoCopyWithImpl<$Res, $Val extends RewardDto>
    implements $RewardDtoCopyWith<$Res> {
  _$RewardDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referrerReward = null,
    Object? referredReward = null,
  }) {
    return _then(_value.copyWith(
      referrerReward: null == referrerReward
          ? _value.referrerReward
          : referrerReward // ignore: cast_nullable_to_non_nullable
              as int,
      referredReward: null == referredReward
          ? _value.referredReward
          : referredReward // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RewardDtoImplCopyWith<$Res>
    implements $RewardDtoCopyWith<$Res> {
  factory _$$RewardDtoImplCopyWith(
          _$RewardDtoImpl value, $Res Function(_$RewardDtoImpl) then) =
      __$$RewardDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'referrer_reward') int referrerReward,
      @JsonKey(name: 'referred_reward') int referredReward});
}

/// @nodoc
class __$$RewardDtoImplCopyWithImpl<$Res>
    extends _$RewardDtoCopyWithImpl<$Res, _$RewardDtoImpl>
    implements _$$RewardDtoImplCopyWith<$Res> {
  __$$RewardDtoImplCopyWithImpl(
      _$RewardDtoImpl _value, $Res Function(_$RewardDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referrerReward = null,
    Object? referredReward = null,
  }) {
    return _then(_$RewardDtoImpl(
      referrerReward: null == referrerReward
          ? _value.referrerReward
          : referrerReward // ignore: cast_nullable_to_non_nullable
              as int,
      referredReward: null == referredReward
          ? _value.referredReward
          : referredReward // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RewardDtoImpl implements _RewardDto {
  const _$RewardDtoImpl(
      {@JsonKey(name: 'referrer_reward') this.referrerReward = 0,
      @JsonKey(name: 'referred_reward') this.referredReward = 0});

  factory _$RewardDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$RewardDtoImplFromJson(json);

  @override
  @JsonKey(name: 'referrer_reward')
  final int referrerReward;
  @override
  @JsonKey(name: 'referred_reward')
  final int referredReward;

  @override
  String toString() {
    return 'RewardDto(referrerReward: $referrerReward, referredReward: $referredReward)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RewardDtoImpl &&
            (identical(other.referrerReward, referrerReward) ||
                other.referrerReward == referrerReward) &&
            (identical(other.referredReward, referredReward) ||
                other.referredReward == referredReward));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, referrerReward, referredReward);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RewardDtoImplCopyWith<_$RewardDtoImpl> get copyWith =>
      __$$RewardDtoImplCopyWithImpl<_$RewardDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RewardDtoImplToJson(
      this,
    );
  }
}

abstract class _RewardDto implements RewardDto {
  const factory _RewardDto(
          {@JsonKey(name: 'referrer_reward') final int referrerReward,
          @JsonKey(name: 'referred_reward') final int referredReward}) =
      _$RewardDtoImpl;

  factory _RewardDto.fromJson(Map<String, dynamic> json) =
      _$RewardDtoImpl.fromJson;

  @override
  @JsonKey(name: 'referrer_reward')
  int get referrerReward;
  @override
  @JsonKey(name: 'referred_reward')
  int get referredReward;
  @override
  @JsonKey(ignore: true)
  _$$RewardDtoImplCopyWith<_$RewardDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
