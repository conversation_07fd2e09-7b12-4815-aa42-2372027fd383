// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transcript_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TranscriptDto _$TranscriptDtoFromJson(Map<String, dynamic> json) {
  return _TranscriptDto.fromJson(json);
}

/// @nodoc
mixin _$TranscriptDto {
  String get text => throw _privateConstructorUsedError;
  double get start => throw _privateConstructorUsedError;
  double get duration => throw _privateConstructorUsedError;
  String get speaker => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TranscriptDtoCopyWith<TranscriptDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TranscriptDtoCopyWith<$Res> {
  factory $TranscriptDtoCopyWith(
          TranscriptDto value, $Res Function(TranscriptDto) then) =
      _$TranscriptDtoCopyWithImpl<$Res, TranscriptDto>;
  @useResult
  $Res call({String text, double start, double duration, String speaker});
}

/// @nodoc
class _$TranscriptDtoCopyWithImpl<$Res, $Val extends TranscriptDto>
    implements $TranscriptDtoCopyWith<$Res> {
  _$TranscriptDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? start = null,
    Object? duration = null,
    Object? speaker = null,
  }) {
    return _then(_value.copyWith(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      start: null == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as double,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as double,
      speaker: null == speaker
          ? _value.speaker
          : speaker // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TranscriptDtoImplCopyWith<$Res>
    implements $TranscriptDtoCopyWith<$Res> {
  factory _$$TranscriptDtoImplCopyWith(
          _$TranscriptDtoImpl value, $Res Function(_$TranscriptDtoImpl) then) =
      __$$TranscriptDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String text, double start, double duration, String speaker});
}

/// @nodoc
class __$$TranscriptDtoImplCopyWithImpl<$Res>
    extends _$TranscriptDtoCopyWithImpl<$Res, _$TranscriptDtoImpl>
    implements _$$TranscriptDtoImplCopyWith<$Res> {
  __$$TranscriptDtoImplCopyWithImpl(
      _$TranscriptDtoImpl _value, $Res Function(_$TranscriptDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? start = null,
    Object? duration = null,
    Object? speaker = null,
  }) {
    return _then(_$TranscriptDtoImpl(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      start: null == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as double,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as double,
      speaker: null == speaker
          ? _value.speaker
          : speaker // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TranscriptDtoImpl implements _TranscriptDto {
  const _$TranscriptDtoImpl(
      {this.text = '',
      this.start = 0.0,
      this.duration = 0.0,
      this.speaker = ''});

  factory _$TranscriptDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$TranscriptDtoImplFromJson(json);

  @override
  @JsonKey()
  final String text;
  @override
  @JsonKey()
  final double start;
  @override
  @JsonKey()
  final double duration;
  @override
  @JsonKey()
  final String speaker;

  @override
  String toString() {
    return 'TranscriptDto(text: $text, start: $start, duration: $duration, speaker: $speaker)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TranscriptDtoImpl &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.start, start) || other.start == start) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.speaker, speaker) || other.speaker == speaker));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, text, start, duration, speaker);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TranscriptDtoImplCopyWith<_$TranscriptDtoImpl> get copyWith =>
      __$$TranscriptDtoImplCopyWithImpl<_$TranscriptDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TranscriptDtoImplToJson(
      this,
    );
  }
}

abstract class _TranscriptDto implements TranscriptDto {
  const factory _TranscriptDto(
      {final String text,
      final double start,
      final double duration,
      final String speaker}) = _$TranscriptDtoImpl;

  factory _TranscriptDto.fromJson(Map<String, dynamic> json) =
      _$TranscriptDtoImpl.fromJson;

  @override
  String get text;
  @override
  double get start;
  @override
  double get duration;
  @override
  String get speaker;
  @override
  @JsonKey(ignore: true)
  _$$TranscriptDtoImplCopyWith<_$TranscriptDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
