import 'package:hive/hive.dart';

import '../../../../services/hive/hive_type_id.dart';

part 'flashcard_set_data_model.g.dart';

@HiveType(typeId: HiveTypeId.flashcardSetDataId)
class FlashcardSetDataModel extends HiveObject {
  @HiveField(0, defaultValue: '')
  String setId;

  @HiveField(1, defaultValue: '')
  String name;

  @HiveField(2, defaultValue: 0)
  int cardsCount;

  @HiveField(3, defaultValue: '')
  String difficulty;

  FlashcardSetDataModel({
    required this.setId,
    required this.name,
    required this.difficulty,
    this.cardsCount = 0,
  });

  FlashcardSetDataModel copyWith({
    String? setId,
    String? name,
    String? difficulty,
    int? cardsCount,
  }) {
    return FlashcardSetDataModel(
      setId: setId ?? this.setId,
      name: name ?? this.name,
      difficulty: difficulty ?? this.difficulty,
      cardsCount: cardsCount ?? this.cardsCount,
    );
  }
}
