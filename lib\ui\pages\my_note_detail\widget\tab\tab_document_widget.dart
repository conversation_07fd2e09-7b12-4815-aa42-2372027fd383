import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:note_x/lib.dart';

class TabDocumentWidget extends StatelessWidget {
  const TabDocumentWidget({
    super.key,
    required this.note,
  });

  final NoteModel note;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().noteBox.listenable(),
      builder: (context, box, child) {
        final currentNote = box.get(note.id) ?? note;
        return currentNote.documentUrl.isNotEmpty
            ? DocumentPage(
                note: currentNote,
                isPdfFile: MyUtils.isFilePDF(currentNote.documentUrl),
              )
            : EmptyPage(
                isShowButton: false,
                image: Assets.images.imgMascotDocument,
                title: S.current.document_available,
                noteModel: currentNote,
                isLoading: currentNote.noteStatus == NoteStatus.loading,
                contentButton: CommonText(
                  S.current.create,
                  style: TextStyle(
                    fontSize: context.isTablet ? 16 : 14.sp,
                    fontWeight:
                        context.isTablet ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
                onTap: () {},
              );
      },
    );
  }
}
