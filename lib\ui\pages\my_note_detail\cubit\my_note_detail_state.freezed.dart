// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'my_note_detail_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$MyNoteDetailState {
  MyNoteDetailOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  QuizEvent get quizEvent => throw _privateConstructorUsedError;
  FlashCardEvent get flashCardEvent => throw _privateConstructorUsedError;
  DeleteNoteEvent get deleteNoteEvent => throw _privateConstructorUsedError;
  CreateFolderEvent get createFolderEvent => throw _privateConstructorUsedError;
  MindMapEvent get mindMapEvent => throw _privateConstructorUsedError;
  SharePdfEvent get sharePdfEvent => throw _privateConstructorUsedError;
  ExportMindMapEvent get exportMindMapEvent =>
      throw _privateConstructorUsedError;
  TranslateNoteEvent get translateNoteEvent =>
      throw _privateConstructorUsedError;
  FlashcardOneShotEvent get flashcardEvent =>
      throw _privateConstructorUsedError;
  QuizOneShotEvent get quizOneShotEvent => throw _privateConstructorUsedError;
  ChooseFlashcardOneShotEvent get chooseFlashcard =>
      throw _privateConstructorUsedError;
  ChooseQuizOneShotEvent get chooseQuiz => throw _privateConstructorUsedError;
  bool get onTopContentReady => throw _privateConstructorUsedError;
  bool get isGeneratingQuiz => throw _privateConstructorUsedError;
  bool get isGeneratingFlashCard => throw _privateConstructorUsedError;
  bool get isGeneratingMindMap => throw _privateConstructorUsedError;
  bool get isFetchingNoteDataFromServer => throw _privateConstructorUsedError;
  bool get isScrolled => throw _privateConstructorUsedError;
  bool get isVisibleKeyboard => throw _privateConstructorUsedError;
  bool get hasEdits => throw _privateConstructorUsedError;
  bool get isRefreshingSummary => throw _privateConstructorUsedError;
  bool get isRefreshingTranscript => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MyNoteDetailStateCopyWith<MyNoteDetailState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MyNoteDetailStateCopyWith<$Res> {
  factory $MyNoteDetailStateCopyWith(
          MyNoteDetailState value, $Res Function(MyNoteDetailState) then) =
      _$MyNoteDetailStateCopyWithImpl<$Res, MyNoteDetailState>;
  @useResult
  $Res call(
      {MyNoteDetailOneShotEvent oneShotEvent,
      QuizEvent quizEvent,
      FlashCardEvent flashCardEvent,
      DeleteNoteEvent deleteNoteEvent,
      CreateFolderEvent createFolderEvent,
      MindMapEvent mindMapEvent,
      SharePdfEvent sharePdfEvent,
      ExportMindMapEvent exportMindMapEvent,
      TranslateNoteEvent translateNoteEvent,
      FlashcardOneShotEvent flashcardEvent,
      QuizOneShotEvent quizOneShotEvent,
      ChooseFlashcardOneShotEvent chooseFlashcard,
      ChooseQuizOneShotEvent chooseQuiz,
      bool onTopContentReady,
      bool isGeneratingQuiz,
      bool isGeneratingFlashCard,
      bool isGeneratingMindMap,
      bool isFetchingNoteDataFromServer,
      bool isScrolled,
      bool isVisibleKeyboard,
      bool hasEdits,
      bool isRefreshingSummary,
      bool isRefreshingTranscript});
}

/// @nodoc
class _$MyNoteDetailStateCopyWithImpl<$Res, $Val extends MyNoteDetailState>
    implements $MyNoteDetailStateCopyWith<$Res> {
  _$MyNoteDetailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? quizEvent = null,
    Object? flashCardEvent = null,
    Object? deleteNoteEvent = null,
    Object? createFolderEvent = null,
    Object? mindMapEvent = null,
    Object? sharePdfEvent = null,
    Object? exportMindMapEvent = null,
    Object? translateNoteEvent = null,
    Object? flashcardEvent = null,
    Object? quizOneShotEvent = null,
    Object? chooseFlashcard = null,
    Object? chooseQuiz = null,
    Object? onTopContentReady = null,
    Object? isGeneratingQuiz = null,
    Object? isGeneratingFlashCard = null,
    Object? isGeneratingMindMap = null,
    Object? isFetchingNoteDataFromServer = null,
    Object? isScrolled = null,
    Object? isVisibleKeyboard = null,
    Object? hasEdits = null,
    Object? isRefreshingSummary = null,
    Object? isRefreshingTranscript = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as MyNoteDetailOneShotEvent,
      quizEvent: null == quizEvent
          ? _value.quizEvent
          : quizEvent // ignore: cast_nullable_to_non_nullable
              as QuizEvent,
      flashCardEvent: null == flashCardEvent
          ? _value.flashCardEvent
          : flashCardEvent // ignore: cast_nullable_to_non_nullable
              as FlashCardEvent,
      deleteNoteEvent: null == deleteNoteEvent
          ? _value.deleteNoteEvent
          : deleteNoteEvent // ignore: cast_nullable_to_non_nullable
              as DeleteNoteEvent,
      createFolderEvent: null == createFolderEvent
          ? _value.createFolderEvent
          : createFolderEvent // ignore: cast_nullable_to_non_nullable
              as CreateFolderEvent,
      mindMapEvent: null == mindMapEvent
          ? _value.mindMapEvent
          : mindMapEvent // ignore: cast_nullable_to_non_nullable
              as MindMapEvent,
      sharePdfEvent: null == sharePdfEvent
          ? _value.sharePdfEvent
          : sharePdfEvent // ignore: cast_nullable_to_non_nullable
              as SharePdfEvent,
      exportMindMapEvent: null == exportMindMapEvent
          ? _value.exportMindMapEvent
          : exportMindMapEvent // ignore: cast_nullable_to_non_nullable
              as ExportMindMapEvent,
      translateNoteEvent: null == translateNoteEvent
          ? _value.translateNoteEvent
          : translateNoteEvent // ignore: cast_nullable_to_non_nullable
              as TranslateNoteEvent,
      flashcardEvent: null == flashcardEvent
          ? _value.flashcardEvent
          : flashcardEvent // ignore: cast_nullable_to_non_nullable
              as FlashcardOneShotEvent,
      quizOneShotEvent: null == quizOneShotEvent
          ? _value.quizOneShotEvent
          : quizOneShotEvent // ignore: cast_nullable_to_non_nullable
              as QuizOneShotEvent,
      chooseFlashcard: null == chooseFlashcard
          ? _value.chooseFlashcard
          : chooseFlashcard // ignore: cast_nullable_to_non_nullable
              as ChooseFlashcardOneShotEvent,
      chooseQuiz: null == chooseQuiz
          ? _value.chooseQuiz
          : chooseQuiz // ignore: cast_nullable_to_non_nullable
              as ChooseQuizOneShotEvent,
      onTopContentReady: null == onTopContentReady
          ? _value.onTopContentReady
          : onTopContentReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isGeneratingQuiz: null == isGeneratingQuiz
          ? _value.isGeneratingQuiz
          : isGeneratingQuiz // ignore: cast_nullable_to_non_nullable
              as bool,
      isGeneratingFlashCard: null == isGeneratingFlashCard
          ? _value.isGeneratingFlashCard
          : isGeneratingFlashCard // ignore: cast_nullable_to_non_nullable
              as bool,
      isGeneratingMindMap: null == isGeneratingMindMap
          ? _value.isGeneratingMindMap
          : isGeneratingMindMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingNoteDataFromServer: null == isFetchingNoteDataFromServer
          ? _value.isFetchingNoteDataFromServer
          : isFetchingNoteDataFromServer // ignore: cast_nullable_to_non_nullable
              as bool,
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      isVisibleKeyboard: null == isVisibleKeyboard
          ? _value.isVisibleKeyboard
          : isVisibleKeyboard // ignore: cast_nullable_to_non_nullable
              as bool,
      hasEdits: null == hasEdits
          ? _value.hasEdits
          : hasEdits // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshingSummary: null == isRefreshingSummary
          ? _value.isRefreshingSummary
          : isRefreshingSummary // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshingTranscript: null == isRefreshingTranscript
          ? _value.isRefreshingTranscript
          : isRefreshingTranscript // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MyNoteDetailStateImplCopyWith<$Res>
    implements $MyNoteDetailStateCopyWith<$Res> {
  factory _$$MyNoteDetailStateImplCopyWith(_$MyNoteDetailStateImpl value,
          $Res Function(_$MyNoteDetailStateImpl) then) =
      __$$MyNoteDetailStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MyNoteDetailOneShotEvent oneShotEvent,
      QuizEvent quizEvent,
      FlashCardEvent flashCardEvent,
      DeleteNoteEvent deleteNoteEvent,
      CreateFolderEvent createFolderEvent,
      MindMapEvent mindMapEvent,
      SharePdfEvent sharePdfEvent,
      ExportMindMapEvent exportMindMapEvent,
      TranslateNoteEvent translateNoteEvent,
      FlashcardOneShotEvent flashcardEvent,
      QuizOneShotEvent quizOneShotEvent,
      ChooseFlashcardOneShotEvent chooseFlashcard,
      ChooseQuizOneShotEvent chooseQuiz,
      bool onTopContentReady,
      bool isGeneratingQuiz,
      bool isGeneratingFlashCard,
      bool isGeneratingMindMap,
      bool isFetchingNoteDataFromServer,
      bool isScrolled,
      bool isVisibleKeyboard,
      bool hasEdits,
      bool isRefreshingSummary,
      bool isRefreshingTranscript});
}

/// @nodoc
class __$$MyNoteDetailStateImplCopyWithImpl<$Res>
    extends _$MyNoteDetailStateCopyWithImpl<$Res, _$MyNoteDetailStateImpl>
    implements _$$MyNoteDetailStateImplCopyWith<$Res> {
  __$$MyNoteDetailStateImplCopyWithImpl(_$MyNoteDetailStateImpl _value,
      $Res Function(_$MyNoteDetailStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? quizEvent = null,
    Object? flashCardEvent = null,
    Object? deleteNoteEvent = null,
    Object? createFolderEvent = null,
    Object? mindMapEvent = null,
    Object? sharePdfEvent = null,
    Object? exportMindMapEvent = null,
    Object? translateNoteEvent = null,
    Object? flashcardEvent = null,
    Object? quizOneShotEvent = null,
    Object? chooseFlashcard = null,
    Object? chooseQuiz = null,
    Object? onTopContentReady = null,
    Object? isGeneratingQuiz = null,
    Object? isGeneratingFlashCard = null,
    Object? isGeneratingMindMap = null,
    Object? isFetchingNoteDataFromServer = null,
    Object? isScrolled = null,
    Object? isVisibleKeyboard = null,
    Object? hasEdits = null,
    Object? isRefreshingSummary = null,
    Object? isRefreshingTranscript = null,
  }) {
    return _then(_$MyNoteDetailStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as MyNoteDetailOneShotEvent,
      quizEvent: null == quizEvent
          ? _value.quizEvent
          : quizEvent // ignore: cast_nullable_to_non_nullable
              as QuizEvent,
      flashCardEvent: null == flashCardEvent
          ? _value.flashCardEvent
          : flashCardEvent // ignore: cast_nullable_to_non_nullable
              as FlashCardEvent,
      deleteNoteEvent: null == deleteNoteEvent
          ? _value.deleteNoteEvent
          : deleteNoteEvent // ignore: cast_nullable_to_non_nullable
              as DeleteNoteEvent,
      createFolderEvent: null == createFolderEvent
          ? _value.createFolderEvent
          : createFolderEvent // ignore: cast_nullable_to_non_nullable
              as CreateFolderEvent,
      mindMapEvent: null == mindMapEvent
          ? _value.mindMapEvent
          : mindMapEvent // ignore: cast_nullable_to_non_nullable
              as MindMapEvent,
      sharePdfEvent: null == sharePdfEvent
          ? _value.sharePdfEvent
          : sharePdfEvent // ignore: cast_nullable_to_non_nullable
              as SharePdfEvent,
      exportMindMapEvent: null == exportMindMapEvent
          ? _value.exportMindMapEvent
          : exportMindMapEvent // ignore: cast_nullable_to_non_nullable
              as ExportMindMapEvent,
      translateNoteEvent: null == translateNoteEvent
          ? _value.translateNoteEvent
          : translateNoteEvent // ignore: cast_nullable_to_non_nullable
              as TranslateNoteEvent,
      flashcardEvent: null == flashcardEvent
          ? _value.flashcardEvent
          : flashcardEvent // ignore: cast_nullable_to_non_nullable
              as FlashcardOneShotEvent,
      quizOneShotEvent: null == quizOneShotEvent
          ? _value.quizOneShotEvent
          : quizOneShotEvent // ignore: cast_nullable_to_non_nullable
              as QuizOneShotEvent,
      chooseFlashcard: null == chooseFlashcard
          ? _value.chooseFlashcard
          : chooseFlashcard // ignore: cast_nullable_to_non_nullable
              as ChooseFlashcardOneShotEvent,
      chooseQuiz: null == chooseQuiz
          ? _value.chooseQuiz
          : chooseQuiz // ignore: cast_nullable_to_non_nullable
              as ChooseQuizOneShotEvent,
      onTopContentReady: null == onTopContentReady
          ? _value.onTopContentReady
          : onTopContentReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isGeneratingQuiz: null == isGeneratingQuiz
          ? _value.isGeneratingQuiz
          : isGeneratingQuiz // ignore: cast_nullable_to_non_nullable
              as bool,
      isGeneratingFlashCard: null == isGeneratingFlashCard
          ? _value.isGeneratingFlashCard
          : isGeneratingFlashCard // ignore: cast_nullable_to_non_nullable
              as bool,
      isGeneratingMindMap: null == isGeneratingMindMap
          ? _value.isGeneratingMindMap
          : isGeneratingMindMap // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingNoteDataFromServer: null == isFetchingNoteDataFromServer
          ? _value.isFetchingNoteDataFromServer
          : isFetchingNoteDataFromServer // ignore: cast_nullable_to_non_nullable
              as bool,
      isScrolled: null == isScrolled
          ? _value.isScrolled
          : isScrolled // ignore: cast_nullable_to_non_nullable
              as bool,
      isVisibleKeyboard: null == isVisibleKeyboard
          ? _value.isVisibleKeyboard
          : isVisibleKeyboard // ignore: cast_nullable_to_non_nullable
              as bool,
      hasEdits: null == hasEdits
          ? _value.hasEdits
          : hasEdits // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshingSummary: null == isRefreshingSummary
          ? _value.isRefreshingSummary
          : isRefreshingSummary // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshingTranscript: null == isRefreshingTranscript
          ? _value.isRefreshingTranscript
          : isRefreshingTranscript // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$MyNoteDetailStateImpl implements _MyNoteDetailState {
  const _$MyNoteDetailStateImpl(
      {this.oneShotEvent = MyNoteDetailOneShotEvent.none,
      this.quizEvent = QuizEvent.initial,
      this.flashCardEvent = FlashCardEvent.initial,
      this.deleteNoteEvent = DeleteNoteEvent.initial,
      this.createFolderEvent = CreateFolderEvent.initial,
      this.mindMapEvent = MindMapEvent.initial,
      this.sharePdfEvent = SharePdfEvent.initial,
      this.exportMindMapEvent = ExportMindMapEvent.initial,
      this.translateNoteEvent = TranslateNoteEvent.none,
      this.flashcardEvent = FlashcardOneShotEvent.initial,
      this.quizOneShotEvent = QuizOneShotEvent.initial,
      this.chooseFlashcard = ChooseFlashcardOneShotEvent.initial,
      this.chooseQuiz = ChooseQuizOneShotEvent.initial,
      this.onTopContentReady = false,
      this.isGeneratingQuiz = false,
      this.isGeneratingFlashCard = false,
      this.isGeneratingMindMap = false,
      this.isFetchingNoteDataFromServer = false,
      this.isScrolled = false,
      this.isVisibleKeyboard = false,
      this.hasEdits = false,
      this.isRefreshingSummary = false,
      this.isRefreshingTranscript = false});

  @override
  @JsonKey()
  final MyNoteDetailOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final QuizEvent quizEvent;
  @override
  @JsonKey()
  final FlashCardEvent flashCardEvent;
  @override
  @JsonKey()
  final DeleteNoteEvent deleteNoteEvent;
  @override
  @JsonKey()
  final CreateFolderEvent createFolderEvent;
  @override
  @JsonKey()
  final MindMapEvent mindMapEvent;
  @override
  @JsonKey()
  final SharePdfEvent sharePdfEvent;
  @override
  @JsonKey()
  final ExportMindMapEvent exportMindMapEvent;
  @override
  @JsonKey()
  final TranslateNoteEvent translateNoteEvent;
  @override
  @JsonKey()
  final FlashcardOneShotEvent flashcardEvent;
  @override
  @JsonKey()
  final QuizOneShotEvent quizOneShotEvent;
  @override
  @JsonKey()
  final ChooseFlashcardOneShotEvent chooseFlashcard;
  @override
  @JsonKey()
  final ChooseQuizOneShotEvent chooseQuiz;
  @override
  @JsonKey()
  final bool onTopContentReady;
  @override
  @JsonKey()
  final bool isGeneratingQuiz;
  @override
  @JsonKey()
  final bool isGeneratingFlashCard;
  @override
  @JsonKey()
  final bool isGeneratingMindMap;
  @override
  @JsonKey()
  final bool isFetchingNoteDataFromServer;
  @override
  @JsonKey()
  final bool isScrolled;
  @override
  @JsonKey()
  final bool isVisibleKeyboard;
  @override
  @JsonKey()
  final bool hasEdits;
  @override
  @JsonKey()
  final bool isRefreshingSummary;
  @override
  @JsonKey()
  final bool isRefreshingTranscript;

  @override
  String toString() {
    return 'MyNoteDetailState(oneShotEvent: $oneShotEvent, quizEvent: $quizEvent, flashCardEvent: $flashCardEvent, deleteNoteEvent: $deleteNoteEvent, createFolderEvent: $createFolderEvent, mindMapEvent: $mindMapEvent, sharePdfEvent: $sharePdfEvent, exportMindMapEvent: $exportMindMapEvent, translateNoteEvent: $translateNoteEvent, flashcardEvent: $flashcardEvent, quizOneShotEvent: $quizOneShotEvent, chooseFlashcard: $chooseFlashcard, chooseQuiz: $chooseQuiz, onTopContentReady: $onTopContentReady, isGeneratingQuiz: $isGeneratingQuiz, isGeneratingFlashCard: $isGeneratingFlashCard, isGeneratingMindMap: $isGeneratingMindMap, isFetchingNoteDataFromServer: $isFetchingNoteDataFromServer, isScrolled: $isScrolled, isVisibleKeyboard: $isVisibleKeyboard, hasEdits: $hasEdits, isRefreshingSummary: $isRefreshingSummary, isRefreshingTranscript: $isRefreshingTranscript)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MyNoteDetailStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.quizEvent, quizEvent) ||
                other.quizEvent == quizEvent) &&
            (identical(other.flashCardEvent, flashCardEvent) ||
                other.flashCardEvent == flashCardEvent) &&
            (identical(other.deleteNoteEvent, deleteNoteEvent) ||
                other.deleteNoteEvent == deleteNoteEvent) &&
            (identical(other.createFolderEvent, createFolderEvent) ||
                other.createFolderEvent == createFolderEvent) &&
            (identical(other.mindMapEvent, mindMapEvent) ||
                other.mindMapEvent == mindMapEvent) &&
            (identical(other.sharePdfEvent, sharePdfEvent) ||
                other.sharePdfEvent == sharePdfEvent) &&
            (identical(other.exportMindMapEvent, exportMindMapEvent) ||
                other.exportMindMapEvent == exportMindMapEvent) &&
            (identical(other.translateNoteEvent, translateNoteEvent) ||
                other.translateNoteEvent == translateNoteEvent) &&
            (identical(other.flashcardEvent, flashcardEvent) ||
                other.flashcardEvent == flashcardEvent) &&
            (identical(other.quizOneShotEvent, quizOneShotEvent) ||
                other.quizOneShotEvent == quizOneShotEvent) &&
            (identical(other.chooseFlashcard, chooseFlashcard) ||
                other.chooseFlashcard == chooseFlashcard) &&
            (identical(other.chooseQuiz, chooseQuiz) ||
                other.chooseQuiz == chooseQuiz) &&
            (identical(other.onTopContentReady, onTopContentReady) ||
                other.onTopContentReady == onTopContentReady) &&
            (identical(other.isGeneratingQuiz, isGeneratingQuiz) ||
                other.isGeneratingQuiz == isGeneratingQuiz) &&
            (identical(other.isGeneratingFlashCard, isGeneratingFlashCard) ||
                other.isGeneratingFlashCard == isGeneratingFlashCard) &&
            (identical(other.isGeneratingMindMap, isGeneratingMindMap) ||
                other.isGeneratingMindMap == isGeneratingMindMap) &&
            (identical(other.isFetchingNoteDataFromServer,
                    isFetchingNoteDataFromServer) ||
                other.isFetchingNoteDataFromServer ==
                    isFetchingNoteDataFromServer) &&
            (identical(other.isScrolled, isScrolled) ||
                other.isScrolled == isScrolled) &&
            (identical(other.isVisibleKeyboard, isVisibleKeyboard) ||
                other.isVisibleKeyboard == isVisibleKeyboard) &&
            (identical(other.hasEdits, hasEdits) ||
                other.hasEdits == hasEdits) &&
            (identical(other.isRefreshingSummary, isRefreshingSummary) ||
                other.isRefreshingSummary == isRefreshingSummary) &&
            (identical(other.isRefreshingTranscript, isRefreshingTranscript) ||
                other.isRefreshingTranscript == isRefreshingTranscript));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        oneShotEvent,
        quizEvent,
        flashCardEvent,
        deleteNoteEvent,
        createFolderEvent,
        mindMapEvent,
        sharePdfEvent,
        exportMindMapEvent,
        translateNoteEvent,
        flashcardEvent,
        quizOneShotEvent,
        chooseFlashcard,
        chooseQuiz,
        onTopContentReady,
        isGeneratingQuiz,
        isGeneratingFlashCard,
        isGeneratingMindMap,
        isFetchingNoteDataFromServer,
        isScrolled,
        isVisibleKeyboard,
        hasEdits,
        isRefreshingSummary,
        isRefreshingTranscript
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MyNoteDetailStateImplCopyWith<_$MyNoteDetailStateImpl> get copyWith =>
      __$$MyNoteDetailStateImplCopyWithImpl<_$MyNoteDetailStateImpl>(
          this, _$identity);
}

abstract class _MyNoteDetailState implements MyNoteDetailState {
  const factory _MyNoteDetailState(
      {final MyNoteDetailOneShotEvent oneShotEvent,
      final QuizEvent quizEvent,
      final FlashCardEvent flashCardEvent,
      final DeleteNoteEvent deleteNoteEvent,
      final CreateFolderEvent createFolderEvent,
      final MindMapEvent mindMapEvent,
      final SharePdfEvent sharePdfEvent,
      final ExportMindMapEvent exportMindMapEvent,
      final TranslateNoteEvent translateNoteEvent,
      final FlashcardOneShotEvent flashcardEvent,
      final QuizOneShotEvent quizOneShotEvent,
      final ChooseFlashcardOneShotEvent chooseFlashcard,
      final ChooseQuizOneShotEvent chooseQuiz,
      final bool onTopContentReady,
      final bool isGeneratingQuiz,
      final bool isGeneratingFlashCard,
      final bool isGeneratingMindMap,
      final bool isFetchingNoteDataFromServer,
      final bool isScrolled,
      final bool isVisibleKeyboard,
      final bool hasEdits,
      final bool isRefreshingSummary,
      final bool isRefreshingTranscript}) = _$MyNoteDetailStateImpl;

  @override
  MyNoteDetailOneShotEvent get oneShotEvent;
  @override
  QuizEvent get quizEvent;
  @override
  FlashCardEvent get flashCardEvent;
  @override
  DeleteNoteEvent get deleteNoteEvent;
  @override
  CreateFolderEvent get createFolderEvent;
  @override
  MindMapEvent get mindMapEvent;
  @override
  SharePdfEvent get sharePdfEvent;
  @override
  ExportMindMapEvent get exportMindMapEvent;
  @override
  TranslateNoteEvent get translateNoteEvent;
  @override
  FlashcardOneShotEvent get flashcardEvent;
  @override
  QuizOneShotEvent get quizOneShotEvent;
  @override
  ChooseFlashcardOneShotEvent get chooseFlashcard;
  @override
  ChooseQuizOneShotEvent get chooseQuiz;
  @override
  bool get onTopContentReady;
  @override
  bool get isGeneratingQuiz;
  @override
  bool get isGeneratingFlashCard;
  @override
  bool get isGeneratingMindMap;
  @override
  bool get isFetchingNoteDataFromServer;
  @override
  bool get isScrolled;
  @override
  bool get isVisibleKeyboard;
  @override
  bool get hasEdits;
  @override
  bool get isRefreshingSummary;
  @override
  bool get isRefreshingTranscript;
  @override
  @JsonKey(ignore: true)
  _$$MyNoteDetailStateImplCopyWith<_$MyNoteDetailStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
