import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';

class AudioController extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  File? _currentPlayingFile;
  bool _isPlaying = false;
  final ValueNotifier<Duration> _position = ValueNotifier(Duration.zero);
  final ValueNotifier<Duration> _duration = ValueNotifier(Duration.zero);
  final ValueNotifier<double> playbackSpeed = ValueNotifier(1.0);
  final List<double> playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
  int currentSpeedIndex = 2;
  bool isDragging = false;
  bool _isDisposed = false;
  StreamSubscription? _playerStateSubscription;
  StreamSubscription? _positionSubscription;
  StreamSubscription? _durationSubscription;

  AudioPlayer get audioPlayer => _audioPlayer;
  File? get currentPlayingFile => _currentPlayingFile;
  bool get isPlaying => _isPlaying;
  ValueNotifier<Duration> get position => _position;
  ValueNotifier<Duration> get duration => _duration;

  AudioController() {
    _setupAudioPlayer();
  }

  void _setupAudioPlayer() {
    _playerStateSubscription =
        _audioPlayer.playerStateStream.listen((state) async {
      if (!_isDisposed) {
        _isPlaying = state.playing;

        // Auto stop when playback completes
        if (state.processingState == ProcessingState.completed) {
          await stop();
        }

        notifyListeners();
      }
    });

    _positionSubscription = _audioPlayer.positionStream.listen((position) {
      if (!_isDisposed) {
        _position.value = position;
      }
    });

    _durationSubscription = _audioPlayer.durationStream.listen((duration) {
      if (!_isDisposed && duration != null) {
        _duration.value = duration;
      }
    });
  }

  Future<void> handleAudioTap(File file) async {
    if (_isDisposed) return;

    try {
      // Same file - toggle play/pause
      if (_currentPlayingFile?.path == file.path) {
        if (_audioPlayer.playing) {
          await _audioPlayer.pause();
          _isPlaying = false;
        } else {
          await _audioPlayer.play();
          _isPlaying = true;
        }
      }
      // Different file - load and play new file
      else {
        // Stop current if playing
        if (_audioPlayer.playing) {
          await _audioPlayer.stop();
        }

        // Set new file and reset position
        _currentPlayingFile = file;
        _position.value = Duration.zero;

        // Load and play
        await _audioPlayer.setFilePath(file.path);
        await _audioPlayer.play();
        _isPlaying = true;
      }
    } catch (e) {
      _isPlaying = false;
      _currentPlayingFile = null;
      _position.value = Duration.zero;
    }

    notifyListeners();
  }

  Future<void> seekTo(Duration position) async {
    if (_isDisposed) return;
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> stop() async {
    if (_isDisposed) return;
    try {
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }
      await _audioPlayer.seek(Duration.zero);
      _position.value = Duration.zero;
      _currentPlayingFile = null;
      _isPlaying = false;
      notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _audioPlayer.dispose();
    _position.dispose();
    _duration.dispose();
    super.dispose();
  }
}
