// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_chat_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

HistoryChatModel _$HistoryChatModelFromJson(Map<String, dynamic> json) {
  return _HistoryChatModel.fromJson(json);
}

/// @nodoc
mixin _$HistoryChatModel {
  String get userId => throw _privateConstructorUsedError;
  List<NumberOfMessageNoteModel> get numberOfMessageNoteModels =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryChatModelCopyWith<HistoryChatModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryChatModelCopyWith<$Res> {
  factory $HistoryChatModelCopyWith(
          HistoryChatModel value, $Res Function(HistoryChatModel) then) =
      _$HistoryChatModelCopyWithImpl<$Res, HistoryChatModel>;
  @useResult
  $Res call(
      {String userId,
      List<NumberOfMessageNoteModel> numberOfMessageNoteModels});
}

/// @nodoc
class _$HistoryChatModelCopyWithImpl<$Res, $Val extends HistoryChatModel>
    implements $HistoryChatModelCopyWith<$Res> {
  _$HistoryChatModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? numberOfMessageNoteModels = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfMessageNoteModels: null == numberOfMessageNoteModels
          ? _value.numberOfMessageNoteModels
          : numberOfMessageNoteModels // ignore: cast_nullable_to_non_nullable
              as List<NumberOfMessageNoteModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistoryChatModelImplCopyWith<$Res>
    implements $HistoryChatModelCopyWith<$Res> {
  factory _$$HistoryChatModelImplCopyWith(_$HistoryChatModelImpl value,
          $Res Function(_$HistoryChatModelImpl) then) =
      __$$HistoryChatModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      List<NumberOfMessageNoteModel> numberOfMessageNoteModels});
}

/// @nodoc
class __$$HistoryChatModelImplCopyWithImpl<$Res>
    extends _$HistoryChatModelCopyWithImpl<$Res, _$HistoryChatModelImpl>
    implements _$$HistoryChatModelImplCopyWith<$Res> {
  __$$HistoryChatModelImplCopyWithImpl(_$HistoryChatModelImpl _value,
      $Res Function(_$HistoryChatModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? numberOfMessageNoteModels = null,
  }) {
    return _then(_$HistoryChatModelImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfMessageNoteModels: null == numberOfMessageNoteModels
          ? _value._numberOfMessageNoteModels
          : numberOfMessageNoteModels // ignore: cast_nullable_to_non_nullable
              as List<NumberOfMessageNoteModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoryChatModelImpl implements _HistoryChatModel {
  const _$HistoryChatModelImpl(
      {this.userId = '',
      final List<NumberOfMessageNoteModel> numberOfMessageNoteModels =
          const []})
      : _numberOfMessageNoteModels = numberOfMessageNoteModels;

  factory _$HistoryChatModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoryChatModelImplFromJson(json);

  @override
  @JsonKey()
  final String userId;
  final List<NumberOfMessageNoteModel> _numberOfMessageNoteModels;
  @override
  @JsonKey()
  List<NumberOfMessageNoteModel> get numberOfMessageNoteModels {
    if (_numberOfMessageNoteModels is EqualUnmodifiableListView)
      return _numberOfMessageNoteModels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_numberOfMessageNoteModels);
  }

  @override
  String toString() {
    return 'HistoryChatModel(userId: $userId, numberOfMessageNoteModels: $numberOfMessageNoteModels)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryChatModelImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(
                other._numberOfMessageNoteModels, _numberOfMessageNoteModels));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId,
      const DeepCollectionEquality().hash(_numberOfMessageNoteModels));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryChatModelImplCopyWith<_$HistoryChatModelImpl> get copyWith =>
      __$$HistoryChatModelImplCopyWithImpl<_$HistoryChatModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoryChatModelImplToJson(
      this,
    );
  }
}

abstract class _HistoryChatModel implements HistoryChatModel {
  const factory _HistoryChatModel(
          {final String userId,
          final List<NumberOfMessageNoteModel> numberOfMessageNoteModels}) =
      _$HistoryChatModelImpl;

  factory _HistoryChatModel.fromJson(Map<String, dynamic> json) =
      _$HistoryChatModelImpl.fromJson;

  @override
  String get userId;
  @override
  List<NumberOfMessageNoteModel> get numberOfMessageNoteModels;
  @override
  @JsonKey(ignore: true)
  _$$HistoryChatModelImplCopyWith<_$HistoryChatModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
