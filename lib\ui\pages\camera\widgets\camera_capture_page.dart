import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/camera/cubit/camera_state.dart';
import 'package:note_x/base/base_page_state.dart';

class CameraCapturePage extends StatefulWidget {
  static const routeName = 'CameraCapturePage';

  final VoidCallback? onCreateNoteSuccess;
  final bool isFromWelcome;
  final bool hasImagesInParent;
  final int existingImagesCount;

  const CameraCapturePage({
    Key? key,
    this.onCreateNoteSuccess,
    this.isFromWelcome = false,
    this.hasImagesInParent = false,
    this.existingImagesCount = 0,
  }) : super(key: key);

  @override
  State<CameraCapturePage> createState() => _CameraCapturePageState();
}

class _CameraCapturePageState extends BasePageStateDelegate<CameraCapturePage, CameraCubit> {
  bool _isReturningWithImages = false;
  final ValueNotifier<bool> _isCapturing = ValueNotifier<bool>(false);
  final ValueNotifier<double> _currentZoomLevel = ValueNotifier<double>(1.0);

  @override
  void initState() {
    super.initState();
    cubit.logEventTrackingOpenCameraPage();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
        await cubit.initializeCamera();
    });
  }

  @override
  void dispose() {
    debugPrint('Disposing CameraCapturePage');

    if (!_isReturningWithImages) {
      debugPrint('Disposing camera in dispose() method');

      WidgetsBinding.instance.addPostFrameCallback((_) async {
        try {
          await cubit.disposeCamera();
        } catch (e) {
          debugPrint('Error disposing camera in dispose(): $e');
        }
      });
    } else {
      debugPrint('Skipping camera disposal in dispose() as we are returning with images');
    }

    _isCapturing.dispose();
    _currentZoomLevel.dispose();
    super.dispose();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<CameraCubit, CameraState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent,
      listener: (context, state) {
        if (state.oneShotEvent == CreateNoteWithCameraOneShotEvent.cameraInitFailed) {
          _showCameraPermissionDialog(context);
        } else if (state.oneShotEvent != CreateNoteWithCameraOneShotEvent.none) {
          _handleOneShotEvent(state.oneShotEvent);
        }
      },
      child: child,
    );
  }

  void _showCameraPermissionDialog(BuildContext context) {
    if (context.mounted) {
      cubit.showCameraPermissionDialog(context);
    }
    cubit.resetEnumState();
  }

  void _handleOneShotEvent(CreateNoteWithCameraOneShotEvent event) {
    switch (event) {
      case CreateNoteWithCameraOneShotEvent.maxImagesReached:
        cubit.logEventTrackingCameraCaptureMaxImagesReached();
        CommonDialogs.showToast(S.current.support_for_up_to_10_images);
        break;
      case CreateNoteWithCameraOneShotEvent.cameraInitialized:
        break;
      case CreateNoteWithCameraOneShotEvent.cameraInitFailed:
        cubit.logEventTrackingCameraCaptureInitFailed();
        break;
      default:
        break;
    }
    cubit.resetEnumState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: context.colorScheme.mainBackground,
        appBar: _buildAppBar(),
        body: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  child: _buildCameraSection(),
                ),
                _buildBottomControls(),
              ],
            ),
            Align(
             alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: EdgeInsets.only(bottom: cubit.appCubit.isTablet ? 60 : 40.h),
                  child: _buildCaptureButton(),
                ),
            )
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.close, color: context.colorScheme.mainPrimary),
        onPressed: _onBackPressed,
      ),
      actions: [
        // Flash button
        BlocBuilder<CameraCubit, CameraState>(
          buildWhen: (prev, curr) => prev.flashMode != curr.flashMode,
          builder: (context, state) {
            return IconButton(
              icon: Icon(_getFlashIcon(state.flashMode), color: context.colorScheme.mainPrimary),
              onPressed: _toggleFlashMode,
            );
          },
        ),
      ],
    );
  }

  IconData _getFlashIcon(FlashMode mode) {
    switch (mode) {
      case FlashMode.off: return Icons.flash_off;
      case FlashMode.auto: return Icons.flash_auto;
      case FlashMode.always: return Icons.flash_on;
      case FlashMode.torch: return Icons.highlight;
    }
  }

  Widget _buildCameraSection() {
    return BlocBuilder<CameraCubit, CameraState>(
      buildWhen: (prev, curr) =>
          prev.isCameraInitialized != curr.isCameraInitialized ||
          prev.isCameraActive != curr.isCameraActive ||
          prev.flashMode != curr.flashMode,
      builder: (context, state) {
        final cameraController = cubit.cameraController;

        if (!state.isCameraInitialized ||
            !state.isCameraActive ||
            cameraController == null ||
            !cameraController.value.isInitialized) {
          return _buildCameraPlaceholder();
        }

        return _buildCameraPreview(cameraController);
      },
    );
  }

  Widget _buildCameraPlaceholder() {
    return Container(
      width: double.infinity,
      color: context.colorScheme.mainBackground,
      child: Center(
        child: CupertinoActivityIndicator(
          radius: cubit.appCubit.isTablet ? 20 : 16.w,
          color: context.colorScheme.mainPrimary,
        ),
      ),
    );
  }

  Widget _buildCameraPreview(CameraController cameraController) {
      return Stack(
        children: [
          _buildCameraGestureDetector(cameraController),
          _buildZoomIndicator(),
        ],
      );
  }

  Widget _buildCameraGestureDetector(CameraController cameraController) {
    return ValueListenableBuilder<double>(
      valueListenable: _currentZoomLevel,
      builder: (context, currentZoom, child) {
        return GestureDetector(
          onScaleStart: (_) {},
          onScaleUpdate: (details) {
            if (!cameraController.value.isInitialized) return;

            // Adjust zoom sensitivity for tablets
            final double zoomSensitivity = cubit.appCubit.isTablet ? 0.8 : 1.0;
            final double newScale = (currentZoom * details.scale * zoomSensitivity).clamp(1.0, 5.0);

            // Only log zoom event if there's a significant change
            if ((newScale - currentZoom).abs() > 0.5) {
              cubit.logEventTrackingCameraCaptureZoom();
            }

            cubit.setZoomLevel(newScale);
            _currentZoomLevel.value = newScale;
          },
          child: AspectRatio(
            aspectRatio: 3 / 4,
            child: CameraPreview(cameraController),
          ),
        );
      },
    );
  }

  Widget _buildZoomIndicator() {
    return ValueListenableBuilder<double>(
      valueListenable: _currentZoomLevel,
      builder: (context, zoomLevel, child) {
        if (zoomLevel <= 1.1) {
          return const SizedBox.shrink();
        }

        final bool isTablet = cubit.appCubit.isTablet;

        return Positioned(
          top: isTablet ? 30 : 20,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 16 : 12.w,
                vertical: isTablet ? 8 : 6.h
              ),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                '${zoomLevel.toStringAsFixed(1)}x',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return BlocBuilder<CameraCubit, CameraState>(
      buildWhen: (prev, curr) => prev.selectedImagePaths != curr.selectedImagePaths,
      builder: (context, state) {
        final hasImages = state.selectedImagePaths.isNotEmpty;
        final images = state.selectedImagePaths;
        final bool isTablet = cubit.appCubit.isTablet;

        // Increase height to accommodate content with text below image
        final double containerHeight = isTablet ? 180 : 140.h;

        return Container(
          color: context.colorScheme.mainBackground,
          height: containerHeight,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: isTablet ? 30 : 20.h,
              top: isTablet ? 10 : 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Thumbnail preview
                Padding(
                  padding: EdgeInsets.only(left: isTablet ? 16 : 16.w),
                  child: _buildThumbnailPreview(images),
                ),
                const Spacer(),
                // Done button
                Align(
                  alignment: Alignment.bottomRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: isTablet ? 16 : 16.w),
                    child: _buildDoneButton(hasImages),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCaptureButton() {
    return GestureDetector(
      onTapDown: (_) {
        _isCapturing.value = true;
      },
      onTapUp: (_) {
        _isCapturing.value = false;
        _onCaptureButtonPressed();
      },
      onTapCancel: () {
        _isCapturing.value = false;
      },
      child: ValueListenableBuilder<bool>(
        valueListenable: _isCapturing,
        builder: (context, isCapturing, child) {
          return Transform.scale(
            scale: isCapturing ? 0.9 : 1.0,
            child: SvgPicture.asset(
              Assets.icons.icCameraScan,
              width: cubit.appCubit.isTablet ? 80 : 80.w,
              height: cubit.appCubit.isTablet ? 80 : 80.h,
            ),
          );
        },
      ),
    );
  }

  Future<void> _onCaptureButtonPressed() async {
    if (_isCapturing.value) return;

    _isCapturing.value = true;

    try {
      await cubit.takeSinglePhoto(existingImagesCount: widget.existingImagesCount);
    } catch (e) {
      debugPrint('Error taking photo: $e');
    } finally {
      if (mounted) {
        _isCapturing.value = false;
      }
    }
  }

  Widget _buildThumbnailPreview(List<String> images) {
    final bool isTablet = cubit.appCubit.isTablet;
    final double thumbnailSize = isTablet ? 70 : 60.w;

    if (images.isEmpty) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: thumbnailSize,
            height: thumbnailSize,
          ),
          AppConstants.kSpacingItem8,
          CommonText(
            '',
            style: TextStyle(
              fontSize: isTablet ? 16 : 14.sp,
              color: context.colorScheme.mainGray,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      );
    }

    final lastImagePath = images.last;
    return GestureDetector(
      onTap: () {
        cubit.logEventTrackingCameraCapturePreview();
        _showImageGallery(images);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: thumbnailSize,
            height: thumbnailSize,
            child: Stack(
              children: [
                // Image thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Image.file(
                    File(lastImagePath),
                    width: thumbnailSize,
                    height: thumbnailSize,
                    fit: BoxFit.cover,
                    key: ValueKey(lastImagePath),
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey,
                        child: Icon(Icons.error,
                          color: Colors.white,
                          size: isTablet ? 32 : 24,
                        ),
                      );
                    },
                  ),
                ),
                // Counter overlay for multiple images
                if (images.length > 1) _buildImageCountOverlay(images.length),
              ],
            ),
          ),
          AppConstants.kSpacingItem8,
          CommonText(
            S.current.photos,
            style: TextStyle(
              fontSize: isTablet ? 16 : 14.sp,
              color: context.colorScheme.mainGray,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageCountOverlay(int count) {
    final bool isTablet = cubit.appCubit.isTablet;
    final double thumbnailSize = isTablet ? 70 : 60.w;

    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        width: thumbnailSize,
        height: thumbnailSize,
        color: Colors.black.withOpacity(0.4),
        alignment: Alignment.center,
        child: CommonText(
          '+$count',
          style: TextStyle(
            color: Colors.white,
            fontSize: isTablet ? 18 : 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _showImageGallery(List<String> images) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImagePreviewGallery(
          imagePaths: images,
          initialIndex: images.length - 1,
          onSwapImages: (oldIndex, newIndex) {
            cubit.swapImages(oldIndex, newIndex);
          },
          onDeleteImage: (index) {
            cubit.removeImage(index);
          },
          onRefresh: () {
            setState(() {});
          },
        ),
      ),
    );
  }

  Widget _buildDoneButton(bool hasImages) {
    final bool isTablet = cubit.appCubit.isTablet;

    return GestureDetector(
      onTap: hasImages ? _returnToCameraPage : null,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.all(isTablet ? 18 : 18.w),
            child: SvgPicture.asset(
              cubit.appCubit.isReverseView
                  ? Assets.icons.icFlipCreateNoteDone
                  : Assets.icons.icCreateNoteDone,
              width: isTablet ? 32 : 24.w,
              height: isTablet ? 32 : 24.h,
              colorFilter: ColorFilter.mode(
                context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
          AppConstants.kSpacingItem8,
          CommonText(
            S.current.done_button_label,
            style: TextStyle(
              color: context.colorScheme.mainGray,
              fontSize: isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w400,
            ),
          )
        ],
      ),
    );
  }

  void _onBackPressed() async {
    cubit.logEventTrackingCameraBack();

    if (cubit.state.selectedImagePaths.isNotEmpty) {
      showNewCupertinoDialog(
        context: context,
        title: S.current.discard_changes,
        message: S.current.content_discard_changes_image,
        image: Assets.icons.icDiscardChanges,
        cancelButton: S.current.cancel,
        confirmButton: S.current.discard,
        onCancel: () {},
        onConfirm: () async {
          cubit.discardChanges();
          _navigateBackAfterOutcome();
        },
      );
    } else {
      // No unsaved changes, check if we should go back to home
      _navigateBackAfterOutcome();
    }
  }

  // Helper method to handle navigation logic
  void _navigateBackAfterOutcome() {
    if (mounted) {
      if (!widget.hasImagesInParent) {
        // If parent has no images, navigate back to home
        Navigator.of(context).popUntil((route) => route.isFirst);
      } else {
        // If parent has images, just pop this screen
        Navigator.of(context).pop();
      }
    }
  }

  void _toggleFlashMode() async {
    if (cubit.cameraController == null || !cubit.cameraController!.value.isInitialized) {
      return;
    }

    try {
      cubit.logEventTrackingCameraCaptureFlashToggled();

      FlashMode nextMode;
      switch (cubit.state.flashMode) {
        case FlashMode.off: nextMode = FlashMode.auto; break;
        case FlashMode.auto: nextMode = FlashMode.always; break;
        case FlashMode.always: nextMode = FlashMode.torch; break;
        case FlashMode.torch: nextMode = FlashMode.off; break;
      }

      await cubit.setFlashMode(nextMode);
    } catch (e) {
      debugPrint('Error toggling flash mode: $e');
    }
  }

  void _returnToCameraPage() async {
    _isReturningWithImages = true;
    cubit.logEventTrackingCameraCaptureDone();

    try {
      await Future.delayed(const Duration(milliseconds: 100));
      await cubit.disposeCamera();

      final imagePaths = List<String>.from(cubit.state.selectedImagePaths);
      if (mounted) {
        Navigator.of(context).pop({'result': true, 'imagePaths': imagePaths});
      }
    } catch (e) {
      debugPrint('Error in _returnToCameraPage: $e');
      if (mounted) {
        Navigator.of(context).pop(false);
      }
    }
  }
}
