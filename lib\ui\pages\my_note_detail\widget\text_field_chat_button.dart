import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:animated_text_kit/animated_text_kit.dart';

Widget buildChatTextField(
  BuildContext context, {
  required VoidCallback onTap,
  required List<String> placeholderTexts,
}) {
  return Stack(
    alignment: Alignment.topCenter,
    children: [
      Container(
        height: context.isTablet ? 100 : 94.h,
        decoration: BoxDecoration(
          color: context.colorScheme.mainBackground,
        ),
      ),
      Padding(
        padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
        child: InkWell(
          onTap: onTap,
          child: ShimmerBorderBox(
            child: IgnorePointer(
              ignoring: true,
              child: Stack(
                children: [
                  CupertinoTextField(
                    readOnly: true,
                    placeholder: '',
                    textAlignVertical: TextAlignVertical.top,
                    placeholderStyle: TextStyle(
                      color: context.colorScheme.mainGray,
                      fontSize: context.isTablet ? 16 : 15.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    decoration: BoxDecoration(
                      color: context.colorScheme.mainBackground,
                      borderRadius: BorderRadius.circular(100.r),
                    ),
                    onChanged: (value) {},
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 8.h,
                    ),
                    style: TextStyle(
                      color: context.colorScheme.mainPrimary,
                      fontSize: context.isTablet ? 16 : 15.sp,
                    ),
                    cursorColor: Colors.white,
                    maxLines: 5,
                    minLines: 1,
                    keyboardType: TextInputType.multiline,
                    textCapitalization: TextCapitalization.sentences,
                    prefix: Padding(
                      padding: EdgeInsets.only(left: 2.w),
                      child: Lottie.asset(
                        Assets.videos.typeChat,
                        width: context.isTablet ? 44.0 : 44.0.w,
                        height: context.isTablet ? 44.0 : 44.0.w,
                      ),
                    ),
                    suffix: Container(
                      height: context.isTablet ? 60 : 48.h,
                      margin:
                          EdgeInsets.only(right: context.isTablet ? 8 : 6.w),
                      decoration: BoxDecoration(
                        color: context.colorScheme.mainNeutral,
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(context.isTablet ? 12 : 6.w),
                        child: SvgPicture.asset(
                          Assets.icons.icSendChat,
                          width: context.isTablet ? 20 : 20.w,
                          height: context.isTablet ? 20 : 20.h,
                          colorFilter: ColorFilter.mode(
                            context.colorScheme.mainPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    left: context.isTablet ? 48 : 44.w,
                    top: 0,
                    bottom: 0,
                    child: Center(
                      child: SizedBox(
                        width: context.isTablet ? 250 : 250.w,
                        child: AnimatedTextKit(
                          animatedTexts: placeholderTexts
                              .map(
                                (text) => FadeAnimatedText(
                                  text,
                                  textStyle: TextStyle(
                                    color: context.colorScheme.mainGray,
                                    fontSize: context.isTablet ? 14 : 14.sp,
                                    fontWeight: FontWeight.w400,
                                    wordSpacing: 0,
                                    height: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  duration: const Duration(milliseconds: 3000),
                                ),
                              )
                              .toList(),
                          repeatForever: true,
                          pause: const Duration(milliseconds: 0),
                          displayFullTextOnTap: true,
                          stopPauseOnTap: true,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ],
  );
}
