// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_sets_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuizSetsDtoImpl _$$QuizSetsDtoImplFromJson(Map<String, dynamic> json) =>
    _$QuizSetsDtoImpl(
      setId: json['set_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      difficulty: json['difficulty'] as String? ?? '',
      language: json['language'] as String? ?? '',
      questionsCount: (json['questions_count'] as num?)?.toInt() ?? 0,
      questions: (json['questions'] as List<dynamic>?)
              ?.map((e) => QuizDetailDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
    );

Map<String, dynamic> _$$QuizSetsDtoImplToJson(_$QuizSetsDtoImpl instance) =>
    <String, dynamic>{
      'set_id': instance.setId,
      'name': instance.name,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'language': instance.language,
      'questions_count': instance.questionsCount,
      'questions': instance.questions,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
