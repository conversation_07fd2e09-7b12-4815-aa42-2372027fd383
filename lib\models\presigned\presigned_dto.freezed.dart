// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presigned_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PresignedDto _$PresignedDtoFromJson(Map<String, dynamic> json) {
  return _PresignedDto.fromJson(json);
}

/// @nodoc
mixin _$PresignedDto {
  @JsonKey(name: 'presigned_url')
  String get presignedUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'file_name')
  String get fileName => throw _privateConstructorUsedError;
  @JsonKey(name: 'full_path')
  String get fileUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresignedDtoCopyWith<PresignedDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresignedDtoCopyWith<$Res> {
  factory $PresignedDtoCopyWith(
          PresignedDto value, $Res Function(PresignedDto) then) =
      _$PresignedDtoCopyWithImpl<$Res, PresignedDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'presigned_url') String presignedUrl,
      @JsonKey(name: 'file_name') String fileName,
      @JsonKey(name: 'full_path') String fileUrl});
}

/// @nodoc
class _$PresignedDtoCopyWithImpl<$Res, $Val extends PresignedDto>
    implements $PresignedDtoCopyWith<$Res> {
  _$PresignedDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presignedUrl = null,
    Object? fileName = null,
    Object? fileUrl = null,
  }) {
    return _then(_value.copyWith(
      presignedUrl: null == presignedUrl
          ? _value.presignedUrl
          : presignedUrl // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: null == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresignedDtoImplCopyWith<$Res>
    implements $PresignedDtoCopyWith<$Res> {
  factory _$$PresignedDtoImplCopyWith(
          _$PresignedDtoImpl value, $Res Function(_$PresignedDtoImpl) then) =
      __$$PresignedDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'presigned_url') String presignedUrl,
      @JsonKey(name: 'file_name') String fileName,
      @JsonKey(name: 'full_path') String fileUrl});
}

/// @nodoc
class __$$PresignedDtoImplCopyWithImpl<$Res>
    extends _$PresignedDtoCopyWithImpl<$Res, _$PresignedDtoImpl>
    implements _$$PresignedDtoImplCopyWith<$Res> {
  __$$PresignedDtoImplCopyWithImpl(
      _$PresignedDtoImpl _value, $Res Function(_$PresignedDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presignedUrl = null,
    Object? fileName = null,
    Object? fileUrl = null,
  }) {
    return _then(_$PresignedDtoImpl(
      presignedUrl: null == presignedUrl
          ? _value.presignedUrl
          : presignedUrl // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      fileUrl: null == fileUrl
          ? _value.fileUrl
          : fileUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresignedDtoImpl implements _PresignedDto {
  const _$PresignedDtoImpl(
      {@JsonKey(name: 'presigned_url') this.presignedUrl = '',
      @JsonKey(name: 'file_name') this.fileName = '',
      @JsonKey(name: 'full_path') this.fileUrl = ''});

  factory _$PresignedDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresignedDtoImplFromJson(json);

  @override
  @JsonKey(name: 'presigned_url')
  final String presignedUrl;
  @override
  @JsonKey(name: 'file_name')
  final String fileName;
  @override
  @JsonKey(name: 'full_path')
  final String fileUrl;

  @override
  String toString() {
    return 'PresignedDto(presignedUrl: $presignedUrl, fileName: $fileName, fileUrl: $fileUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresignedDtoImpl &&
            (identical(other.presignedUrl, presignedUrl) ||
                other.presignedUrl == presignedUrl) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.fileUrl, fileUrl) || other.fileUrl == fileUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, presignedUrl, fileName, fileUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresignedDtoImplCopyWith<_$PresignedDtoImpl> get copyWith =>
      __$$PresignedDtoImplCopyWithImpl<_$PresignedDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresignedDtoImplToJson(
      this,
    );
  }
}

abstract class _PresignedDto implements PresignedDto {
  const factory _PresignedDto(
      {@JsonKey(name: 'presigned_url') final String presignedUrl,
      @JsonKey(name: 'file_name') final String fileName,
      @JsonKey(name: 'full_path') final String fileUrl}) = _$PresignedDtoImpl;

  factory _PresignedDto.fromJson(Map<String, dynamic> json) =
      _$PresignedDtoImpl.fromJson;

  @override
  @JsonKey(name: 'presigned_url')
  String get presignedUrl;
  @override
  @JsonKey(name: 'file_name')
  String get fileName;
  @override
  @JsonKey(name: 'full_path')
  String get fileUrl;
  @override
  @JsonKey(ignore: true)
  _$$PresignedDtoImplCopyWith<_$PresignedDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
