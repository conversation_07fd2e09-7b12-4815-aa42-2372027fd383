// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$QuizState {
  List<QuizModelView> get quizzes => throw _privateConstructorUsedError;
  QuizModelView get chosenQuiz => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $QuizStateCopyWith<QuizState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizStateCopyWith<$Res> {
  factory $QuizStateCopyWith(QuizState value, $Res Function(QuizState) then) =
      _$QuizStateCopyWithImpl<$Res, QuizState>;
  @useResult
  $Res call({List<QuizModelView> quizzes, QuizModelView chosenQuiz});

  $QuizModelViewCopyWith<$Res> get chosenQuiz;
}

/// @nodoc
class _$QuizStateCopyWithImpl<$Res, $Val extends QuizState>
    implements $QuizStateCopyWith<$Res> {
  _$QuizStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quizzes = null,
    Object? chosenQuiz = null,
  }) {
    return _then(_value.copyWith(
      quizzes: null == quizzes
          ? _value.quizzes
          : quizzes // ignore: cast_nullable_to_non_nullable
              as List<QuizModelView>,
      chosenQuiz: null == chosenQuiz
          ? _value.chosenQuiz
          : chosenQuiz // ignore: cast_nullable_to_non_nullable
              as QuizModelView,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $QuizModelViewCopyWith<$Res> get chosenQuiz {
    return $QuizModelViewCopyWith<$Res>(_value.chosenQuiz, (value) {
      return _then(_value.copyWith(chosenQuiz: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QuizStateImplCopyWith<$Res>
    implements $QuizStateCopyWith<$Res> {
  factory _$$QuizStateImplCopyWith(
          _$QuizStateImpl value, $Res Function(_$QuizStateImpl) then) =
      __$$QuizStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<QuizModelView> quizzes, QuizModelView chosenQuiz});

  @override
  $QuizModelViewCopyWith<$Res> get chosenQuiz;
}

/// @nodoc
class __$$QuizStateImplCopyWithImpl<$Res>
    extends _$QuizStateCopyWithImpl<$Res, _$QuizStateImpl>
    implements _$$QuizStateImplCopyWith<$Res> {
  __$$QuizStateImplCopyWithImpl(
      _$QuizStateImpl _value, $Res Function(_$QuizStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quizzes = null,
    Object? chosenQuiz = null,
  }) {
    return _then(_$QuizStateImpl(
      quizzes: null == quizzes
          ? _value._quizzes
          : quizzes // ignore: cast_nullable_to_non_nullable
              as List<QuizModelView>,
      chosenQuiz: null == chosenQuiz
          ? _value.chosenQuiz
          : chosenQuiz // ignore: cast_nullable_to_non_nullable
              as QuizModelView,
    ));
  }
}

/// @nodoc

class _$QuizStateImpl implements _QuizState {
  const _$QuizStateImpl(
      {final List<QuizModelView> quizzes = const [],
      this.chosenQuiz = const QuizModelView()})
      : _quizzes = quizzes;

  final List<QuizModelView> _quizzes;
  @override
  @JsonKey()
  List<QuizModelView> get quizzes {
    if (_quizzes is EqualUnmodifiableListView) return _quizzes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_quizzes);
  }

  @override
  @JsonKey()
  final QuizModelView chosenQuiz;

  @override
  String toString() {
    return 'QuizState(quizzes: $quizzes, chosenQuiz: $chosenQuiz)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizStateImpl &&
            const DeepCollectionEquality().equals(other._quizzes, _quizzes) &&
            (identical(other.chosenQuiz, chosenQuiz) ||
                other.chosenQuiz == chosenQuiz));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_quizzes), chosenQuiz);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizStateImplCopyWith<_$QuizStateImpl> get copyWith =>
      __$$QuizStateImplCopyWithImpl<_$QuizStateImpl>(this, _$identity);
}

abstract class _QuizState implements QuizState {
  const factory _QuizState(
      {final List<QuizModelView> quizzes,
      final QuizModelView chosenQuiz}) = _$QuizStateImpl;

  @override
  List<QuizModelView> get quizzes;
  @override
  QuizModelView get chosenQuiz;
  @override
  @JsonKey(ignore: true)
  _$$QuizStateImplCopyWith<_$QuizStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
