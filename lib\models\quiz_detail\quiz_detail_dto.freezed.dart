// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_detail_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

QuizDetailDto _$QuizDetailDtoFromJson(Map<String, dynamic> json) {
  return _QuizDetailDto.fromJson(json);
}

/// @nodoc
mixin _$QuizDetailDto {
  String get question => throw _privateConstructorUsedError;
  List<String> get answers => throw _privateConstructorUsedError;
  @JsonKey(name: 'correct_index')
  int get correctIndex => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  String get context => throw _privateConstructorUsedError;
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizDetailDtoCopyWith<QuizDetailDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizDetailDtoCopyWith<$Res> {
  factory $QuizDetailDtoCopyWith(
          QuizDetailDto value, $Res Function(QuizDetailDto) then) =
      _$QuizDetailDtoCopyWithImpl<$Res, QuizDetailDto>;
  @useResult
  $Res call(
      {String question,
      List<String> answers,
      @JsonKey(name: 'correct_index') int correctIndex,
      String difficulty,
      String context,
      @JsonKey(name: 'transcript_json') List<TranscriptDto> transcriptJson});
}

/// @nodoc
class _$QuizDetailDtoCopyWithImpl<$Res, $Val extends QuizDetailDto>
    implements $QuizDetailDtoCopyWith<$Res> {
  _$QuizDetailDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answers = null,
    Object? correctIndex = null,
    Object? difficulty = null,
    Object? context = null,
    Object? transcriptJson = null,
  }) {
    return _then(_value.copyWith(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _value.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<String>,
      correctIndex: null == correctIndex
          ? _value.correctIndex
          : correctIndex // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String,
      transcriptJson: null == transcriptJson
          ? _value.transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptDto>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizDetailDtoImplCopyWith<$Res>
    implements $QuizDetailDtoCopyWith<$Res> {
  factory _$$QuizDetailDtoImplCopyWith(
          _$QuizDetailDtoImpl value, $Res Function(_$QuizDetailDtoImpl) then) =
      __$$QuizDetailDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String question,
      List<String> answers,
      @JsonKey(name: 'correct_index') int correctIndex,
      String difficulty,
      String context,
      @JsonKey(name: 'transcript_json') List<TranscriptDto> transcriptJson});
}

/// @nodoc
class __$$QuizDetailDtoImplCopyWithImpl<$Res>
    extends _$QuizDetailDtoCopyWithImpl<$Res, _$QuizDetailDtoImpl>
    implements _$$QuizDetailDtoImplCopyWith<$Res> {
  __$$QuizDetailDtoImplCopyWithImpl(
      _$QuizDetailDtoImpl _value, $Res Function(_$QuizDetailDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answers = null,
    Object? correctIndex = null,
    Object? difficulty = null,
    Object? context = null,
    Object? transcriptJson = null,
  }) {
    return _then(_$QuizDetailDtoImpl(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _value._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<String>,
      correctIndex: null == correctIndex
          ? _value.correctIndex
          : correctIndex // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String,
      transcriptJson: null == transcriptJson
          ? _value._transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptDto>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizDetailDtoImpl implements _QuizDetailDto {
  const _$QuizDetailDtoImpl(
      {this.question = '',
      final List<String> answers = const [],
      @JsonKey(name: 'correct_index') this.correctIndex = 0,
      this.difficulty = '',
      this.context = '',
      @JsonKey(name: 'transcript_json')
      final List<TranscriptDto> transcriptJson = const []})
      : _answers = answers,
        _transcriptJson = transcriptJson;

  factory _$QuizDetailDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizDetailDtoImplFromJson(json);

  @override
  @JsonKey()
  final String question;
  final List<String> _answers;
  @override
  @JsonKey()
  List<String> get answers {
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answers);
  }

  @override
  @JsonKey(name: 'correct_index')
  final int correctIndex;
  @override
  @JsonKey()
  final String difficulty;
  @override
  @JsonKey()
  final String context;
  final List<TranscriptDto> _transcriptJson;
  @override
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson {
    if (_transcriptJson is EqualUnmodifiableListView) return _transcriptJson;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transcriptJson);
  }

  @override
  String toString() {
    return 'QuizDetailDto(question: $question, answers: $answers, correctIndex: $correctIndex, difficulty: $difficulty, context: $context, transcriptJson: $transcriptJson)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizDetailDtoImpl &&
            (identical(other.question, question) ||
                other.question == question) &&
            const DeepCollectionEquality().equals(other._answers, _answers) &&
            (identical(other.correctIndex, correctIndex) ||
                other.correctIndex == correctIndex) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.context, context) || other.context == context) &&
            const DeepCollectionEquality()
                .equals(other._transcriptJson, _transcriptJson));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      question,
      const DeepCollectionEquality().hash(_answers),
      correctIndex,
      difficulty,
      context,
      const DeepCollectionEquality().hash(_transcriptJson));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizDetailDtoImplCopyWith<_$QuizDetailDtoImpl> get copyWith =>
      __$$QuizDetailDtoImplCopyWithImpl<_$QuizDetailDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizDetailDtoImplToJson(
      this,
    );
  }
}

abstract class _QuizDetailDto implements QuizDetailDto {
  const factory _QuizDetailDto(
      {final String question,
      final List<String> answers,
      @JsonKey(name: 'correct_index') final int correctIndex,
      final String difficulty,
      final String context,
      @JsonKey(name: 'transcript_json')
      final List<TranscriptDto> transcriptJson}) = _$QuizDetailDtoImpl;

  factory _QuizDetailDto.fromJson(Map<String, dynamic> json) =
      _$QuizDetailDtoImpl.fromJson;

  @override
  String get question;
  @override
  List<String> get answers;
  @override
  @JsonKey(name: 'correct_index')
  int get correctIndex;
  @override
  String get difficulty;
  @override
  String get context;
  @override
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson;
  @override
  @JsonKey(ignore: true)
  _$$QuizDetailDtoImplCopyWith<_$QuizDetailDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
