import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';

class ChatQuickOptions extends StatelessWidget {
  final TextEditingController textController;
  final FocusNode focusNode;
  final Function(String) onSendMessage;
  final List<String> questions;
  final bool isLimitMessages;
  final String noteId;
  final Function(BuildContext) onNavigateToPurchase;

  const ChatQuickOptions({
    Key? key,
    required this.textController,
    required this.focusNode,
    required this.onSendMessage,
    required this.questions,
    required this.onNavigateToPurchase,
    this.isLimitMessages = false,
    required this.noteId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16.0,
        right: 16.0,
        top: 4.0,
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              alignment: Alignment.center,
              child: CommonText(
                S.current.ai_learning_companion,
                style: TextStyle(
                  fontSize: context.isTablet ? 24 : 22.sp,
                  fontWeight: FontWeight.w600,
                  color: context.colorScheme.mainPrimary,
                ),
              ),
            ),
            Lottie.asset(
              Assets.videos.aiChatMain,
              width: context.isTablet ? 140.w : 150.w,
              height: context.isTablet ? 140.w : 150.w,
            ),
            ...questions.map((question) {
              return Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (isLimitMessages &&
                        context
                                .read<ChatCubit>()
                                .getNumberOfLimitMessagesById(noteId) >
                            AppConstants.numberOfLimitMessages) {
                      onNavigateToPurchase(context);
                    } else {
                      AnalyticsService.logAnalyticsEventNoParam(
                        eventName: EventName.ai_chat_choose_suggestion,
                      );
                      textController.text = question;
                      onSendMessage(question);
                    }
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: context.colorScheme.mainNeutral,
                      borderRadius: BorderRadius.circular(48.r),
                    ),
                    child: CommonText(
                      question,
                      style: TextStyle(
                        color: context.colorScheme.mainPrimary,
                        fontSize: context.isTablet ? 14 : 12.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}
