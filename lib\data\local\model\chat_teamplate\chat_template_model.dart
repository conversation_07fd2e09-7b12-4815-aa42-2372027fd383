import 'package:hive/hive.dart';

import '../../../../services/hive/hive_type_id.dart';
part 'chat_template_model.g.dart';

@HiveType(typeId: HiveTypeId.chatTemplateTypeId)
class ChatTemplateModel extends HiveObject {
  @HiveField(0, defaultValue: "")
  String id;

  @HiveField(1, defaultValue: "")
  String name;

  @HiveField(2, defaultValue: "")
  String prompt;

  @HiveField(3, defaultValue: "")
  String type;

  ChatTemplateModel({
    required this.id,
    required this.name,
    required this.prompt,
    required this.type,
  });
}
