part of 'app_theme.dart';

extension AppColorSchemeExt on ColorScheme {
  Color get mainBlue => _appTheme.blue.toColor;

  Color get mainGray => _appTheme.gray.toColor;

  Color get mainPrimary => _appTheme.primary.toColor;

  Color get mainSecondary => _appTheme.secondary.toColor;

  Color get mainNeutral => _appTheme.neutral.toColor;

  Color get mainSummary => _appTheme.summary.toColor;

  Color get mainRed => _appTheme.red.toColor;

  Color get tagYellow => _appTheme.yellow.toColor;

  Color get tagBlue2 => _appTheme.blue2.toColor;

  Color get tagGreen => _appTheme.green.toColor;

  Color get tagPurple => _appTheme.purple.toColor;

  Color get tagViolet => _appTheme.violet.toColor;

  Color get themeWhite => _appTheme.white.toColor;

  Color get themeBlack => _appTheme.black.toColor;

  Color get mainBackground => _appTheme.bg.toColor;
}
