// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'note_share_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

NoteShareDto _$NoteShareDtoFromJson(Map<String, dynamic> json) {
  return _NoteShareDto.fromJson(json);
}

/// @nodoc
mixin _$NoteShareDto {
  @JsonKey(name: 'is_public')
  bool get isPublic => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_password_protected')
  bool get isPasswordProtected => throw _privateConstructorUsedError;
  @JsonKey(name: 'share_password')
  String get sharePassword => throw _privateConstructorUsedError;
  @JsonKey(name: 'share_link')
  String get shareLink => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NoteShareDtoCopyWith<NoteShareDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NoteShareDtoCopyWith<$Res> {
  factory $NoteShareDtoCopyWith(
          NoteShareDto value, $Res Function(NoteShareDto) then) =
      _$NoteShareDtoCopyWithImpl<$Res, NoteShareDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'is_public') bool isPublic,
      @JsonKey(name: 'is_password_protected') bool isPasswordProtected,
      @JsonKey(name: 'share_password') String sharePassword,
      @JsonKey(name: 'share_link') String shareLink});
}

/// @nodoc
class _$NoteShareDtoCopyWithImpl<$Res, $Val extends NoteShareDto>
    implements $NoteShareDtoCopyWith<$Res> {
  _$NoteShareDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPublic = null,
    Object? isPasswordProtected = null,
    Object? sharePassword = null,
    Object? shareLink = null,
  }) {
    return _then(_value.copyWith(
      isPublic: null == isPublic
          ? _value.isPublic
          : isPublic // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordProtected: null == isPasswordProtected
          ? _value.isPasswordProtected
          : isPasswordProtected // ignore: cast_nullable_to_non_nullable
              as bool,
      sharePassword: null == sharePassword
          ? _value.sharePassword
          : sharePassword // ignore: cast_nullable_to_non_nullable
              as String,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NoteShareDtoImplCopyWith<$Res>
    implements $NoteShareDtoCopyWith<$Res> {
  factory _$$NoteShareDtoImplCopyWith(
          _$NoteShareDtoImpl value, $Res Function(_$NoteShareDtoImpl) then) =
      __$$NoteShareDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'is_public') bool isPublic,
      @JsonKey(name: 'is_password_protected') bool isPasswordProtected,
      @JsonKey(name: 'share_password') String sharePassword,
      @JsonKey(name: 'share_link') String shareLink});
}

/// @nodoc
class __$$NoteShareDtoImplCopyWithImpl<$Res>
    extends _$NoteShareDtoCopyWithImpl<$Res, _$NoteShareDtoImpl>
    implements _$$NoteShareDtoImplCopyWith<$Res> {
  __$$NoteShareDtoImplCopyWithImpl(
      _$NoteShareDtoImpl _value, $Res Function(_$NoteShareDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPublic = null,
    Object? isPasswordProtected = null,
    Object? sharePassword = null,
    Object? shareLink = null,
  }) {
    return _then(_$NoteShareDtoImpl(
      isPublic: null == isPublic
          ? _value.isPublic
          : isPublic // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordProtected: null == isPasswordProtected
          ? _value.isPasswordProtected
          : isPasswordProtected // ignore: cast_nullable_to_non_nullable
              as bool,
      sharePassword: null == sharePassword
          ? _value.sharePassword
          : sharePassword // ignore: cast_nullable_to_non_nullable
              as String,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NoteShareDtoImpl implements _NoteShareDto {
  const _$NoteShareDtoImpl(
      {@JsonKey(name: 'is_public') this.isPublic = false,
      @JsonKey(name: 'is_password_protected') this.isPasswordProtected = false,
      @JsonKey(name: 'share_password') this.sharePassword = '',
      @JsonKey(name: 'share_link') this.shareLink = ''});

  factory _$NoteShareDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$NoteShareDtoImplFromJson(json);

  @override
  @JsonKey(name: 'is_public')
  final bool isPublic;
  @override
  @JsonKey(name: 'is_password_protected')
  final bool isPasswordProtected;
  @override
  @JsonKey(name: 'share_password')
  final String sharePassword;
  @override
  @JsonKey(name: 'share_link')
  final String shareLink;

  @override
  String toString() {
    return 'NoteShareDto(isPublic: $isPublic, isPasswordProtected: $isPasswordProtected, sharePassword: $sharePassword, shareLink: $shareLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoteShareDtoImpl &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.isPasswordProtected, isPasswordProtected) ||
                other.isPasswordProtected == isPasswordProtected) &&
            (identical(other.sharePassword, sharePassword) ||
                other.sharePassword == sharePassword) &&
            (identical(other.shareLink, shareLink) ||
                other.shareLink == shareLink));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, isPublic, isPasswordProtected, sharePassword, shareLink);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NoteShareDtoImplCopyWith<_$NoteShareDtoImpl> get copyWith =>
      __$$NoteShareDtoImplCopyWithImpl<_$NoteShareDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NoteShareDtoImplToJson(
      this,
    );
  }
}

abstract class _NoteShareDto implements NoteShareDto {
  const factory _NoteShareDto(
      {@JsonKey(name: 'is_public') final bool isPublic,
      @JsonKey(name: 'is_password_protected') final bool isPasswordProtected,
      @JsonKey(name: 'share_password') final String sharePassword,
      @JsonKey(name: 'share_link')
      final String shareLink}) = _$NoteShareDtoImpl;

  factory _NoteShareDto.fromJson(Map<String, dynamic> json) =
      _$NoteShareDtoImpl.fromJson;

  @override
  @JsonKey(name: 'is_public')
  bool get isPublic;
  @override
  @JsonKey(name: 'is_password_protected')
  bool get isPasswordProtected;
  @override
  @JsonKey(name: 'share_password')
  String get sharePassword;
  @override
  @JsonKey(name: 'share_link')
  String get shareLink;
  @override
  @JsonKey(ignore: true)
  _$$NoteShareDtoImplCopyWith<_$NoteShareDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
