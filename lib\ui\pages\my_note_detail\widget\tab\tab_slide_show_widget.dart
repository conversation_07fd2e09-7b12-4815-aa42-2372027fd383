import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:note_x/lib.dart';

class TabSlideShowWidget extends StatelessWidget {
  const TabSlideShowWidget({
    super.key,
    required this.noteModel,
  });

  final NoteModel noteModel;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().noteBox.listenable(),
      builder: (context, box, child) {
        final currentNote = box.get(noteModel.id) ?? noteModel;
        return currentNote.slidePdfUrl.isNotEmpty
            ? SlideShowPage(
                note: currentNote,
              )
            : EmptyPage(
                image: Assets.images.imgMascotSlideShow,
                title: S.current.click_create_slide,
                noteModel: currentNote,
                isLoading: currentNote.noteStatus == NoteStatus.loading,
                contentButton: currentNote.isGeneratingSlide
                    ? CupertinoActivityIndicator(
                        radius: 12.r,
                        color: context.colorScheme.themeWhite,
                      )
                    : Text(
                        S.current.create_slide,
                        style: TextStyle(
                          fontSize: context.isTablet ? 16 : 14.sp,
                          fontWeight: context.isTablet
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: currentNote.noteStatus == NoteStatus.error ||
                                  currentNote.noteStatus == NoteStatus.loading
                              ? context.colorScheme.mainPrimary
                                  .withOpacity(0.38)
                              : context.colorScheme.themeWhite,
                        ),
                      ),
                onTap: () {
                  if (currentNote.noteStatus != NoteStatus.loading &&
                      currentNote.noteStatus != NoteStatus.error &&
                      !currentNote.isGeneratingSlide) {
                    AnalyticsService.logAnalyticsEventNoParam(
                      eventName: EventName.slide_show_create_slide_show_clicked,
                    );
                    showCupertinoModalBottomSheet(
                      context: context,
                      isDismissible: true,
                      enableDrag: true,
                      barrierColor:
                          context.colorScheme.mainBackground.withOpacity(0.8),
                      backgroundColor: context.colorScheme.mainBackground,
                      topRadius: const Radius.circular(12),
                      expand: false,
                      useRootNavigator: true,
                      closeProgressThreshold: 0.6,
                      duration: const Duration(milliseconds: 300),
                      animationCurve: Curves.easeOut,
                      builder: (context) => Material(
                        color: context.colorScheme.mainNeutral,
                        child: CreateSlideShowWidget(
                          noteId: noteModel.id,
                        ),
                      ),
                    );
                  }
                },
              );
      },
    );
  }
}
