// <PERSON><PERSON><PERSON> một widget riêng để xử lý GIF
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:note_x/lib.dart';

class GifTemplateItem extends StatefulWidget {
  final SlideTemplateDto template;
  final bool isSelected;
  final VoidCallback onTap;

  const GifTemplateItem({
    Key? key,
    required this.template,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  State<GifTemplateItem> createState() => _GifTemplateItemState();
}

class _GifTemplateItemState extends State<GifTemplateItem> {
  late GifController _controller;

  @override
  void initState() {
    super.initState();
    _controller = GifController()..init(autoPlay: false, loop: true);

    // <PERSON>hông cần chạy GIF ngay từ đầu vì ban đầu không có template nào được chọn
  }

  @override
  void didUpdateWidget(GifTemplateItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Start playing when selected, stop when unselected
    if (widget.isSelected && !oldWidget.isSelected) {
      _controller.play(); // Start playing when selected
    } else if (!widget.isSelected && oldWidget.isSelected) {
      _controller.stop(); // Stop when unselected
    }
  }

  @override
  void dispose() {
    _controller.dispose(); // Quan trọng: cần dispose controller
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: context.colorScheme.mainSecondary,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(16.r),
              child: GifView.network(
                widget.template.gifUrl,
                progressBuilder: (context) =>
                    const CupertinoActivityIndicator(),
                controller: _controller,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                autoPlay: false,
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
                child: Image.asset(
                  Assets.images.imgBlurThumb.path,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              bottom: 8,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: CommonText(
                  widget.template.name,
                  style: TextStyle(
                    color: context.colorScheme.themeWhite,
                    fontSize: context.isTablet ? 16 : 12.sp,
                    fontWeight: FontWeight.w800,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
              ),
            ),
            if (widget.isSelected)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: context.colorScheme.mainBlue,
                      width: 2.5,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
