// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mindmap_export_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MindMapExportDto _$MindMapExportDtoFromJson(Map<String, dynamic> json) {
  return _MindMapExportDto.fromJson(json);
}

/// @nodoc
mixin _$MindMapExportDto {
  @JsonKey(name: 'url')
  String? get url => throw _privateConstructorUsedError;
  @JsonKey(name: 'note_id')
  String? get noteId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MindMapExportDtoCopyWith<MindMapExportDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MindMapExportDtoCopyWith<$Res> {
  factory $MindMapExportDtoCopyWith(
          MindMapExportDto value, $Res Function(MindMapExportDto) then) =
      _$MindMapExportDtoCopyWithImpl<$Res, MindMapExportDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class _$MindMapExportDtoCopyWithImpl<$Res, $Val extends MindMapExportDto>
    implements $MindMapExportDtoCopyWith<$Res> {
  _$MindMapExportDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MindMapExportDtoImplCopyWith<$Res>
    implements $MindMapExportDtoCopyWith<$Res> {
  factory _$$MindMapExportDtoImplCopyWith(_$MindMapExportDtoImpl value,
          $Res Function(_$MindMapExportDtoImpl) then) =
      __$$MindMapExportDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class __$$MindMapExportDtoImplCopyWithImpl<$Res>
    extends _$MindMapExportDtoCopyWithImpl<$Res, _$MindMapExportDtoImpl>
    implements _$$MindMapExportDtoImplCopyWith<$Res> {
  __$$MindMapExportDtoImplCopyWithImpl(_$MindMapExportDtoImpl _value,
      $Res Function(_$MindMapExportDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_$MindMapExportDtoImpl(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MindMapExportDtoImpl implements _MindMapExportDto {
  const _$MindMapExportDtoImpl(
      {@JsonKey(name: 'url') this.url, @JsonKey(name: 'note_id') this.noteId});

  factory _$MindMapExportDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$MindMapExportDtoImplFromJson(json);

  @override
  @JsonKey(name: 'url')
  final String? url;
  @override
  @JsonKey(name: 'note_id')
  final String? noteId;

  @override
  String toString() {
    return 'MindMapExportDto(url: $url, noteId: $noteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MindMapExportDtoImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.noteId, noteId) || other.noteId == noteId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url, noteId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MindMapExportDtoImplCopyWith<_$MindMapExportDtoImpl> get copyWith =>
      __$$MindMapExportDtoImplCopyWithImpl<_$MindMapExportDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MindMapExportDtoImplToJson(
      this,
    );
  }
}

abstract class _MindMapExportDto implements MindMapExportDto {
  const factory _MindMapExportDto(
      {@JsonKey(name: 'url') final String? url,
      @JsonKey(name: 'note_id') final String? noteId}) = _$MindMapExportDtoImpl;

  factory _MindMapExportDto.fromJson(Map<String, dynamic> json) =
      _$MindMapExportDtoImpl.fromJson;

  @override
  @JsonKey(name: 'url')
  String? get url;
  @override
  @JsonKey(name: 'note_id')
  String? get noteId;
  @override
  @JsonKey(ignore: true)
  _$$MindMapExportDtoImplCopyWith<_$MindMapExportDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
