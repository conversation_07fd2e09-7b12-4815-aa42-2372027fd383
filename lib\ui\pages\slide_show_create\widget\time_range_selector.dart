import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class TimeRangeSelector extends StatefulWidget {
  final List<String> ranges;
  final ValueChanged<String> onSelected;
  final String? initialValue;
  final bool enabled;

  const TimeRangeSelector({
    super.key,
    required this.ranges,
    required this.onSelected,
    this.initialValue,
    this.enabled = true,
  });

  @override
  State<TimeRangeSelector> createState() => _TimeRangeSelectorState();
}

class _TimeRangeSelectorState extends State<TimeRangeSelector> {
  late int selectedIndex;

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.initialValue != null
        ? widget.ranges.indexOf(widget.initialValue!)
        : 0;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(widget.ranges.length, (index) {
          final isSelected = index == selectedIndex;
          final enabled = widget.enabled;
          return Padding(
            padding: EdgeInsets.only(
              left: context.isTablet ? 0 : 0,
              right: context.isTablet ? 16 : 12.w,
            ),
            child: GestureDetector(
              onTap: enabled
                  ? () {
                      setState(() {
                        selectedIndex = index;
                      });
                      widget.onSelected(widget.ranges[index]);
                    }
                  : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: context.isTablet ? 30 : 14.w,
                  vertical: context.isTablet ? 12 : 8.w,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? context.colorScheme.mainPrimary
                      : context.colorScheme.mainSecondary,
                  borderRadius:
                      BorderRadius.circular(context.isTablet ? 32 : 20.sp),
                ),
                child: Text(
                  widget.ranges[index],
                  style: TextStyle(
                    color: isSelected
                        ? context.colorScheme.mainNeutral
                        : context.colorScheme.mainPrimary,
                    fontWeight: FontWeight.w500,
                    fontSize: context.isTablet ? 15 : 14.sp,
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
