// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_model_view.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$QuizModelView {
  String get question => throw _privateConstructorUsedError;
  List<String> get answers => throw _privateConstructorUsedError;
  int get correctIndex => throw _privateConstructorUsedError;
  int get answerChosenIndex => throw _privateConstructorUsedError;
  bool get isAnswered => throw _privateConstructorUsedError;
  List<TranscriptModel> get transcriptJson =>
      throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  String get context => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $QuizModelViewCopyWith<QuizModelView> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizModelViewCopyWith<$Res> {
  factory $QuizModelViewCopyWith(
          QuizModelView value, $Res Function(QuizModelView) then) =
      _$QuizModelViewCopyWithImpl<$Res, QuizModelView>;
  @useResult
  $Res call(
      {String question,
      List<String> answers,
      int correctIndex,
      int answerChosenIndex,
      bool isAnswered,
      List<TranscriptModel> transcriptJson,
      String difficulty,
      String context});
}

/// @nodoc
class _$QuizModelViewCopyWithImpl<$Res, $Val extends QuizModelView>
    implements $QuizModelViewCopyWith<$Res> {
  _$QuizModelViewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answers = null,
    Object? correctIndex = null,
    Object? answerChosenIndex = null,
    Object? isAnswered = null,
    Object? transcriptJson = null,
    Object? difficulty = null,
    Object? context = null,
  }) {
    return _then(_value.copyWith(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _value.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<String>,
      correctIndex: null == correctIndex
          ? _value.correctIndex
          : correctIndex // ignore: cast_nullable_to_non_nullable
              as int,
      answerChosenIndex: null == answerChosenIndex
          ? _value.answerChosenIndex
          : answerChosenIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isAnswered: null == isAnswered
          ? _value.isAnswered
          : isAnswered // ignore: cast_nullable_to_non_nullable
              as bool,
      transcriptJson: null == transcriptJson
          ? _value.transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptModel>,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizModelViewImplCopyWith<$Res>
    implements $QuizModelViewCopyWith<$Res> {
  factory _$$QuizModelViewImplCopyWith(
          _$QuizModelViewImpl value, $Res Function(_$QuizModelViewImpl) then) =
      __$$QuizModelViewImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String question,
      List<String> answers,
      int correctIndex,
      int answerChosenIndex,
      bool isAnswered,
      List<TranscriptModel> transcriptJson,
      String difficulty,
      String context});
}

/// @nodoc
class __$$QuizModelViewImplCopyWithImpl<$Res>
    extends _$QuizModelViewCopyWithImpl<$Res, _$QuizModelViewImpl>
    implements _$$QuizModelViewImplCopyWith<$Res> {
  __$$QuizModelViewImplCopyWithImpl(
      _$QuizModelViewImpl _value, $Res Function(_$QuizModelViewImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answers = null,
    Object? correctIndex = null,
    Object? answerChosenIndex = null,
    Object? isAnswered = null,
    Object? transcriptJson = null,
    Object? difficulty = null,
    Object? context = null,
  }) {
    return _then(_$QuizModelViewImpl(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _value._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<String>,
      correctIndex: null == correctIndex
          ? _value.correctIndex
          : correctIndex // ignore: cast_nullable_to_non_nullable
              as int,
      answerChosenIndex: null == answerChosenIndex
          ? _value.answerChosenIndex
          : answerChosenIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isAnswered: null == isAnswered
          ? _value.isAnswered
          : isAnswered // ignore: cast_nullable_to_non_nullable
              as bool,
      transcriptJson: null == transcriptJson
          ? _value._transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptModel>,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$QuizModelViewImpl implements _QuizModelView {
  const _$QuizModelViewImpl(
      {this.question = '',
      final List<String> answers = const [],
      this.correctIndex = 0,
      this.answerChosenIndex = -1,
      this.isAnswered = false,
      final List<TranscriptModel> transcriptJson = const [],
      this.difficulty = '',
      this.context = ''})
      : _answers = answers,
        _transcriptJson = transcriptJson;

  @override
  @JsonKey()
  final String question;
  final List<String> _answers;
  @override
  @JsonKey()
  List<String> get answers {
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answers);
  }

  @override
  @JsonKey()
  final int correctIndex;
  @override
  @JsonKey()
  final int answerChosenIndex;
  @override
  @JsonKey()
  final bool isAnswered;
  final List<TranscriptModel> _transcriptJson;
  @override
  @JsonKey()
  List<TranscriptModel> get transcriptJson {
    if (_transcriptJson is EqualUnmodifiableListView) return _transcriptJson;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transcriptJson);
  }

  @override
  @JsonKey()
  final String difficulty;
  @override
  @JsonKey()
  final String context;

  @override
  String toString() {
    return 'QuizModelView(question: $question, answers: $answers, correctIndex: $correctIndex, answerChosenIndex: $answerChosenIndex, isAnswered: $isAnswered, transcriptJson: $transcriptJson, difficulty: $difficulty, context: $context)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizModelViewImpl &&
            (identical(other.question, question) ||
                other.question == question) &&
            const DeepCollectionEquality().equals(other._answers, _answers) &&
            (identical(other.correctIndex, correctIndex) ||
                other.correctIndex == correctIndex) &&
            (identical(other.answerChosenIndex, answerChosenIndex) ||
                other.answerChosenIndex == answerChosenIndex) &&
            (identical(other.isAnswered, isAnswered) ||
                other.isAnswered == isAnswered) &&
            const DeepCollectionEquality()
                .equals(other._transcriptJson, _transcriptJson) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.context, context) || other.context == context));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      question,
      const DeepCollectionEquality().hash(_answers),
      correctIndex,
      answerChosenIndex,
      isAnswered,
      const DeepCollectionEquality().hash(_transcriptJson),
      difficulty,
      context);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizModelViewImplCopyWith<_$QuizModelViewImpl> get copyWith =>
      __$$QuizModelViewImplCopyWithImpl<_$QuizModelViewImpl>(this, _$identity);
}

abstract class _QuizModelView implements QuizModelView {
  const factory _QuizModelView(
      {final String question,
      final List<String> answers,
      final int correctIndex,
      final int answerChosenIndex,
      final bool isAnswered,
      final List<TranscriptModel> transcriptJson,
      final String difficulty,
      final String context}) = _$QuizModelViewImpl;

  @override
  String get question;
  @override
  List<String> get answers;
  @override
  int get correctIndex;
  @override
  int get answerChosenIndex;
  @override
  bool get isAnswered;
  @override
  List<TranscriptModel> get transcriptJson;
  @override
  String get difficulty;
  @override
  String get context;
  @override
  @JsonKey(ignore: true)
  _$$QuizModelViewImplCopyWith<_$QuizModelViewImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
