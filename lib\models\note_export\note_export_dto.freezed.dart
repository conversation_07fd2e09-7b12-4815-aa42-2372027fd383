// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'note_export_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

NoteExportDto _$NoteExportDtoFromJson(Map<String, dynamic> json) {
  return _NoteExportDto.fromJson(json);
}

/// @nodoc
mixin _$NoteExportDto {
  @JsonKey(name: 'url')
  String? get url => throw _privateConstructorUsedError;
  @JsonKey(name: 'note_id')
  String? get noteId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NoteExportDtoCopyWith<NoteExportDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NoteExportDtoCopyWith<$Res> {
  factory $NoteExportDtoCopyWith(
          NoteExportDto value, $Res Function(NoteExportDto) then) =
      _$NoteExportDtoCopyWithImpl<$Res, NoteExportDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class _$NoteExportDtoCopyWithImpl<$Res, $Val extends NoteExportDto>
    implements $NoteExportDtoCopyWith<$Res> {
  _$NoteExportDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NoteExportDtoImplCopyWith<$Res>
    implements $NoteExportDtoCopyWith<$Res> {
  factory _$$NoteExportDtoImplCopyWith(
          _$NoteExportDtoImpl value, $Res Function(_$NoteExportDtoImpl) then) =
      __$$NoteExportDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class __$$NoteExportDtoImplCopyWithImpl<$Res>
    extends _$NoteExportDtoCopyWithImpl<$Res, _$NoteExportDtoImpl>
    implements _$$NoteExportDtoImplCopyWith<$Res> {
  __$$NoteExportDtoImplCopyWithImpl(
      _$NoteExportDtoImpl _value, $Res Function(_$NoteExportDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_$NoteExportDtoImpl(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NoteExportDtoImpl implements _NoteExportDto {
  const _$NoteExportDtoImpl(
      {@JsonKey(name: 'url') this.url, @JsonKey(name: 'note_id') this.noteId});

  factory _$NoteExportDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$NoteExportDtoImplFromJson(json);

  @override
  @JsonKey(name: 'url')
  final String? url;
  @override
  @JsonKey(name: 'note_id')
  final String? noteId;

  @override
  String toString() {
    return 'NoteExportDto(url: $url, noteId: $noteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoteExportDtoImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.noteId, noteId) || other.noteId == noteId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url, noteId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NoteExportDtoImplCopyWith<_$NoteExportDtoImpl> get copyWith =>
      __$$NoteExportDtoImplCopyWithImpl<_$NoteExportDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NoteExportDtoImplToJson(
      this,
    );
  }
}

abstract class _NoteExportDto implements NoteExportDto {
  const factory _NoteExportDto(
      {@JsonKey(name: 'url') final String? url,
      @JsonKey(name: 'note_id') final String? noteId}) = _$NoteExportDtoImpl;

  factory _NoteExportDto.fromJson(Map<String, dynamic> json) =
      _$NoteExportDtoImpl.fromJson;

  @override
  @JsonKey(name: 'url')
  String? get url;
  @override
  @JsonKey(name: 'note_id')
  String? get noteId;
  @override
  @JsonKey(ignore: true)
  _$$NoteExportDtoImplCopyWith<_$NoteExportDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
