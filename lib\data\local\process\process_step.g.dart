// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'process_step.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProcessStepAdapter extends TypeAdapter<ProcessStep> {
  @override
  final int typeId = 9;

  @override
  ProcessStep read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProcessStep.savingRecording;
      case 1:
        return ProcessStep.uploadingToServer;
      case 2:
        return ProcessStep.transcribing;
      case 3:
        return ProcessStep.generatingAINote;
      default:
        return ProcessStep.savingRecording;
    }
  }

  @override
  void write(BinaryWriter writer, ProcessStep obj) {
    switch (obj) {
      case ProcessStep.savingRecording:
        writer.writeByte(0);
        break;
      case ProcessStep.uploadingToServer:
        writer.writeByte(1);
        break;
      case ProcessStep.transcribing:
        writer.writeByte(2);
        break;
      case ProcessStep.generatingAINote:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProcessStepAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
