import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class PdfGridPreview extends StatelessWidget {
  final String pdfPath;
  final int totalPages;
  final int currentPage;
  final Function(int) onPageTap;

  const PdfGridPreview({
    Key? key,
    required this.pdfPath,
    required this.totalPages,
    required this.currentPage,
    required this.onPageTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: EdgeInsets.only(
        left: context.isTablet ? 0 : 0,
        right: context.isTablet ? 0 : 0,
        top: context.isTablet ? 0 : 0,
        bottom: context.isTablet ? 80.h : 0,
      ),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // hoặc 3 nếu muốn thumbnail nhỏ hơn
        mainAxisSpacing: 0,
        crossAxisSpacing: 0,
        childAspectRatio: 16 / 9, // hoặc 4/3, tuỳ slide
      ),
      itemCount: totalPages,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.all(4),
          child: PdfPageThumbnail(
            pdfPath: pdfPath,
            pageNumber: index + 1,
            isSelected: false,
            onPageTap: onPageTap,
            width: 200, // tuỳ chỉnh
            height: 200, // tuỳ chỉnh
          ),
        );
      },
    );
  }
}
