// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slide_show_create_cubit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$SlideShowCreateCubitState {
  String get cardCount => throw _privateConstructorUsedError;
  bool get isAdvancedMode => throw _privateConstructorUsedError;
  int get selectedTemplateIndex => throw _privateConstructorUsedError;
  List<SlideTemplateDto> get slideTemplates =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SlideShowCreateCubitStateCopyWith<SlideShowCreateCubitState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SlideShowCreateCubitStateCopyWith<$Res> {
  factory $SlideShowCreateCubitStateCopyWith(SlideShowCreateCubitState value,
          $Res Function(SlideShowCreateCubitState) then) =
      _$SlideShowCreateCubitStateCopyWithImpl<$Res, SlideShowCreateCubitState>;
  @useResult
  $Res call(
      {String cardCount,
      bool isAdvancedMode,
      int selectedTemplateIndex,
      List<SlideTemplateDto> slideTemplates});
}

/// @nodoc
class _$SlideShowCreateCubitStateCopyWithImpl<$Res,
        $Val extends SlideShowCreateCubitState>
    implements $SlideShowCreateCubitStateCopyWith<$Res> {
  _$SlideShowCreateCubitStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardCount = null,
    Object? isAdvancedMode = null,
    Object? selectedTemplateIndex = null,
    Object? slideTemplates = null,
  }) {
    return _then(_value.copyWith(
      cardCount: null == cardCount
          ? _value.cardCount
          : cardCount // ignore: cast_nullable_to_non_nullable
              as String,
      isAdvancedMode: null == isAdvancedMode
          ? _value.isAdvancedMode
          : isAdvancedMode // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedTemplateIndex: null == selectedTemplateIndex
          ? _value.selectedTemplateIndex
          : selectedTemplateIndex // ignore: cast_nullable_to_non_nullable
              as int,
      slideTemplates: null == slideTemplates
          ? _value.slideTemplates
          : slideTemplates // ignore: cast_nullable_to_non_nullable
              as List<SlideTemplateDto>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SlideShowCreateCubitStateImplCopyWith<$Res>
    implements $SlideShowCreateCubitStateCopyWith<$Res> {
  factory _$$SlideShowCreateCubitStateImplCopyWith(
          _$SlideShowCreateCubitStateImpl value,
          $Res Function(_$SlideShowCreateCubitStateImpl) then) =
      __$$SlideShowCreateCubitStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String cardCount,
      bool isAdvancedMode,
      int selectedTemplateIndex,
      List<SlideTemplateDto> slideTemplates});
}

/// @nodoc
class __$$SlideShowCreateCubitStateImplCopyWithImpl<$Res>
    extends _$SlideShowCreateCubitStateCopyWithImpl<$Res,
        _$SlideShowCreateCubitStateImpl>
    implements _$$SlideShowCreateCubitStateImplCopyWith<$Res> {
  __$$SlideShowCreateCubitStateImplCopyWithImpl(
      _$SlideShowCreateCubitStateImpl _value,
      $Res Function(_$SlideShowCreateCubitStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardCount = null,
    Object? isAdvancedMode = null,
    Object? selectedTemplateIndex = null,
    Object? slideTemplates = null,
  }) {
    return _then(_$SlideShowCreateCubitStateImpl(
      cardCount: null == cardCount
          ? _value.cardCount
          : cardCount // ignore: cast_nullable_to_non_nullable
              as String,
      isAdvancedMode: null == isAdvancedMode
          ? _value.isAdvancedMode
          : isAdvancedMode // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedTemplateIndex: null == selectedTemplateIndex
          ? _value.selectedTemplateIndex
          : selectedTemplateIndex // ignore: cast_nullable_to_non_nullable
              as int,
      slideTemplates: null == slideTemplates
          ? _value._slideTemplates
          : slideTemplates // ignore: cast_nullable_to_non_nullable
              as List<SlideTemplateDto>,
    ));
  }
}

/// @nodoc

class _$SlideShowCreateCubitStateImpl implements _SlideShowCreateCubitState {
  const _$SlideShowCreateCubitStateImpl(
      {this.cardCount = 'auto',
      this.isAdvancedMode = false,
      this.selectedTemplateIndex = -1,
      final List<SlideTemplateDto> slideTemplates = const []})
      : _slideTemplates = slideTemplates;

  @override
  @JsonKey()
  final String cardCount;
  @override
  @JsonKey()
  final bool isAdvancedMode;
  @override
  @JsonKey()
  final int selectedTemplateIndex;
  final List<SlideTemplateDto> _slideTemplates;
  @override
  @JsonKey()
  List<SlideTemplateDto> get slideTemplates {
    if (_slideTemplates is EqualUnmodifiableListView) return _slideTemplates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_slideTemplates);
  }

  @override
  String toString() {
    return 'SlideShowCreateCubitState(cardCount: $cardCount, isAdvancedMode: $isAdvancedMode, selectedTemplateIndex: $selectedTemplateIndex, slideTemplates: $slideTemplates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SlideShowCreateCubitStateImpl &&
            (identical(other.cardCount, cardCount) ||
                other.cardCount == cardCount) &&
            (identical(other.isAdvancedMode, isAdvancedMode) ||
                other.isAdvancedMode == isAdvancedMode) &&
            (identical(other.selectedTemplateIndex, selectedTemplateIndex) ||
                other.selectedTemplateIndex == selectedTemplateIndex) &&
            const DeepCollectionEquality()
                .equals(other._slideTemplates, _slideTemplates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      cardCount,
      isAdvancedMode,
      selectedTemplateIndex,
      const DeepCollectionEquality().hash(_slideTemplates));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SlideShowCreateCubitStateImplCopyWith<_$SlideShowCreateCubitStateImpl>
      get copyWith => __$$SlideShowCreateCubitStateImplCopyWithImpl<
          _$SlideShowCreateCubitStateImpl>(this, _$identity);
}

abstract class _SlideShowCreateCubitState implements SlideShowCreateCubitState {
  const factory _SlideShowCreateCubitState(
          {final String cardCount,
          final bool isAdvancedMode,
          final int selectedTemplateIndex,
          final List<SlideTemplateDto> slideTemplates}) =
      _$SlideShowCreateCubitStateImpl;

  @override
  String get cardCount;
  @override
  bool get isAdvancedMode;
  @override
  int get selectedTemplateIndex;
  @override
  List<SlideTemplateDto> get slideTemplates;
  @override
  @JsonKey(ignore: true)
  _$$SlideShowCreateCubitStateImplCopyWith<_$SlideShowCreateCubitStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
