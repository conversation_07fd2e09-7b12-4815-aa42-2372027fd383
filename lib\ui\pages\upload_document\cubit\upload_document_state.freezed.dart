// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_document_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$UploadDocumentState {
  CreateNoteWithDocumentOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  String get documentFilePath => throw _privateConstructorUsedError;
  String get documentFileName => throw _privateConstructorUsedError;
  bool get isDocumentFileOverLimit => throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $UploadDocumentStateCopyWith<UploadDocumentState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadDocumentStateCopyWith<$Res> {
  factory $UploadDocumentStateCopyWith(
          UploadDocumentState value, $Res Function(UploadDocumentState) then) =
      _$UploadDocumentStateCopyWithImpl<$Res, UploadDocumentState>;
  @useResult
  $Res call(
      {CreateNoteWithDocumentOneShotEvent oneShotEvent,
      String documentFilePath,
      String documentFileName,
      bool isDocumentFileOverLimit,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class _$UploadDocumentStateCopyWithImpl<$Res, $Val extends UploadDocumentState>
    implements $UploadDocumentStateCopyWith<$Res> {
  _$UploadDocumentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? documentFilePath = null,
    Object? documentFileName = null,
    Object? isDocumentFileOverLimit = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithDocumentOneShotEvent,
      documentFilePath: null == documentFilePath
          ? _value.documentFilePath
          : documentFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      documentFileName: null == documentFileName
          ? _value.documentFileName
          : documentFileName // ignore: cast_nullable_to_non_nullable
              as String,
      isDocumentFileOverLimit: null == isDocumentFileOverLimit
          ? _value.isDocumentFileOverLimit
          : isDocumentFileOverLimit // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadDocumentStateImplCopyWith<$Res>
    implements $UploadDocumentStateCopyWith<$Res> {
  factory _$$UploadDocumentStateImplCopyWith(_$UploadDocumentStateImpl value,
          $Res Function(_$UploadDocumentStateImpl) then) =
      __$$UploadDocumentStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreateNoteWithDocumentOneShotEvent oneShotEvent,
      String documentFilePath,
      String documentFileName,
      bool isDocumentFileOverLimit,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class __$$UploadDocumentStateImplCopyWithImpl<$Res>
    extends _$UploadDocumentStateCopyWithImpl<$Res, _$UploadDocumentStateImpl>
    implements _$$UploadDocumentStateImplCopyWith<$Res> {
  __$$UploadDocumentStateImplCopyWithImpl(_$UploadDocumentStateImpl _value,
      $Res Function(_$UploadDocumentStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? documentFilePath = null,
    Object? documentFileName = null,
    Object? isDocumentFileOverLimit = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_$UploadDocumentStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithDocumentOneShotEvent,
      documentFilePath: null == documentFilePath
          ? _value.documentFilePath
          : documentFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      documentFileName: null == documentFileName
          ? _value.documentFileName
          : documentFileName // ignore: cast_nullable_to_non_nullable
              as String,
      isDocumentFileOverLimit: null == isDocumentFileOverLimit
          ? _value.isDocumentFileOverLimit
          : isDocumentFileOverLimit // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UploadDocumentStateImpl implements _UploadDocumentState {
  const _$UploadDocumentStateImpl(
      {this.oneShotEvent = CreateNoteWithDocumentOneShotEvent.none,
      this.documentFilePath = '',
      this.documentFileName = '',
      this.isDocumentFileOverLimit = false,
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = ''});

  @override
  @JsonKey()
  final CreateNoteWithDocumentOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final String documentFilePath;
  @override
  @JsonKey()
  final String documentFileName;
  @override
  @JsonKey()
  final bool isDocumentFileOverLimit;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;

  @override
  String toString() {
    return 'UploadDocumentState(oneShotEvent: $oneShotEvent, documentFilePath: $documentFilePath, documentFileName: $documentFileName, isDocumentFileOverLimit: $isDocumentFileOverLimit, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadDocumentStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.documentFilePath, documentFilePath) ||
                other.documentFilePath == documentFilePath) &&
            (identical(other.documentFileName, documentFileName) ||
                other.documentFileName == documentFileName) &&
            (identical(
                    other.isDocumentFileOverLimit, isDocumentFileOverLimit) ||
                other.isDocumentFileOverLimit == isDocumentFileOverLimit) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      documentFilePath,
      documentFileName,
      isDocumentFileOverLimit,
      summaryStyle,
      writingStyle,
      additionalInstructions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadDocumentStateImplCopyWith<_$UploadDocumentStateImpl> get copyWith =>
      __$$UploadDocumentStateImplCopyWithImpl<_$UploadDocumentStateImpl>(
          this, _$identity);
}

abstract class _UploadDocumentState implements UploadDocumentState {
  const factory _UploadDocumentState(
      {final CreateNoteWithDocumentOneShotEvent oneShotEvent,
      final String documentFilePath,
      final String documentFileName,
      final bool isDocumentFileOverLimit,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions}) = _$UploadDocumentStateImpl;

  @override
  CreateNoteWithDocumentOneShotEvent get oneShotEvent;
  @override
  String get documentFilePath;
  @override
  String get documentFileName;
  @override
  bool get isDocumentFileOverLimit;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  @JsonKey(ignore: true)
  _$$UploadDocumentStateImplCopyWith<_$UploadDocumentStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
