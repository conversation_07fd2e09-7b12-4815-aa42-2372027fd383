import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:note_x/lib.dart';
import 'package:intl/intl.dart';

class ChatHistoryDrawer extends StatefulWidget {
  final NoteModel note;
  const ChatHistoryDrawer({super.key, required this.note});

  @override
  State<ChatHistoryDrawer> createState() => _ChatHistoryDrawerState();
}

class _ChatHistoryDrawerState extends State<ChatHistoryDrawer> {
  late NoteModel _noteLocal;

  @override
  void initState() {
    super.initState();
    _loadNoteData();
  }

  @override
  void dispose() {
    super.dispose();
    FocusManager.instance.primaryFocus?.unfocus();
  }

  void _loadNoteData() {
    _noteLocal = HiveService().noteBox.get(widget.note.id) ?? widget.note;
    if (_noteLocal.saveToNotes.isNotEmpty) {
      _noteLocal = _noteLocal.copyWith(
          saveToNotes: List.from(_noteLocal.saveToNotes)
            ..sort((a, b) => DateTime.parse(b.createdAt)
                .compareTo(DateTime.parse(a.createdAt))));
    }
  }

  String _formatDate(String dateStr) {
    if (dateStr.isEmpty) return '';

    final date = DateTime.parse(dateStr);
    final now = DateTime.now();
    final isSameDay =
        date.year == now.year && date.month == now.month && date.day == now.day;
    final isYesterday = date.year == now.year &&
        date.month == now.month &&
        date.day == now.day - 1;

    if (isSameDay) {
      return S.current.to_day;
    } else if (isYesterday) {
      return S.current.yesterday;
    } else {
      return DateFormat('E, dd MMM').format(date);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Box<NoteModel>>(
      valueListenable: HiveService().noteBox.listenable(
        keys: [widget.note.id],
      ),
      builder: (context, box, _) {
        if (box.isEmpty) return const SizedBox.shrink();

        final updatedNoteModel = box.get(widget.note.id) ?? widget.note;
        _noteLocal = updatedNoteModel;

        return Drawer(
          width: MediaQuery.of(context).size.width * 0.85,
          backgroundColor: context.colorScheme.mainNeutral,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              Expanded(
                child: _noteLocal.saveToNotes.isEmpty
                    ? _buildEmptyState(context)
                    : _buildChatList(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
        left: 8,
        right: 16,
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.ai_chat_close_saved_chat,
              );
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_rounded,
              color: context.colorScheme.mainPrimary,
              size: context.isTablet ? 24 : 24.sp,
            ),
          ),
          const Spacer(),
          Text(
            S.current.saved_chat,
            style: TextStyle(
              color: context.colorScheme.mainPrimary,
              fontSize: context.isTablet ? 14 : 14.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            Assets.images.imgEmptyMascot,
            width: context.isTablet ? 140 : 140.w,
            height: context.isTablet ? 140 : 140.w,
          ),
          Text(
            S.current.chat_empty,
            style: TextStyle(
              color: context.colorScheme.mainPrimary,
              fontSize: context.isTablet ? 16 : 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _noteLocal.saveToNotes.length,
      itemBuilder: (context, index) {
        final message = _noteLocal.saveToNotes[index];
        final date = _formatDate(message.createdAt);
        final isFirstOfDate = index == 0 ||
            _formatDate(_noteLocal.saveToNotes[index - 1].createdAt) != date;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isFirstOfDate) _buildDateHeader(context, date),
            _buildChatItem(context, message),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(BuildContext context, String date) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 8),
      child: Text(
        date,
        style: TextStyle(
          color: context.colorScheme.mainGray,
          fontWeight: FontWeight.w400,
          fontSize: context.isTablet ? 12 : 12.sp,
        ),
      ),
    );
  }

  Widget _buildChatItem(BuildContext context, ChatMessageModel message) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => _navigateToChatDetail(context, message),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.colorScheme.mainGray.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: SvgPicture.asset(
                Assets.icons.icHomeDoc,
                width: context.isTablet ? 20 : 20.w,
                height: context.isTablet ? 20 : 20.w,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
            context.isTablet
                ? AppConstants.kSpacingItemW8
                : AppConstants.kSpacingItemW12,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonText(
                    message.title.removeNewLines(),
                    style: TextStyle(
                      color: context.colorScheme.mainPrimary,
                      fontSize: context.isTablet ? 14 : 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                  ),
                  AppConstants.kSpacingItem4,
                  CommonText(
                    MyUtils.markdownToPlainText(message.answer),
                    style: TextStyle(
                      color: context.colorScheme.mainGray,
                      fontSize: context.isTablet ? 12 : 12.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToChatDetail(BuildContext context, dynamic message) {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.ai_chat_open_saved_chat_detail,
    );
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatHistoryDetail(
          chatMessageModel: message,
          noteModel: _noteLocal,
        ),
      ),
    );
  }
}
