// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_template_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ChatTemplateDto _$ChatTemplateDtoFromJson(Map<String, dynamic> json) {
  return _ChatTemplateDto.fromJson(json);
}

/// @nodoc
mixin _$ChatTemplateDto {
  @JsonKey(name: 'id')
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'prompt')
  String get prompt => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  String get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChatTemplateDtoCopyWith<ChatTemplateDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatTemplateDtoCopyWith<$Res> {
  factory $ChatTemplateDtoCopyWith(
          ChatTemplateDto value, $Res Function(ChatTemplateDto) then) =
      _$ChatTemplateDtoCopyWithImpl<$Res, ChatTemplateDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'prompt') String prompt,
      @JsonKey(name: 'type') String type});
}

/// @nodoc
class _$ChatTemplateDtoCopyWithImpl<$Res, $Val extends ChatTemplateDto>
    implements $ChatTemplateDtoCopyWith<$Res> {
  _$ChatTemplateDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? prompt = null,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      prompt: null == prompt
          ? _value.prompt
          : prompt // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatTemplateDtoImplCopyWith<$Res>
    implements $ChatTemplateDtoCopyWith<$Res> {
  factory _$$ChatTemplateDtoImplCopyWith(_$ChatTemplateDtoImpl value,
          $Res Function(_$ChatTemplateDtoImpl) then) =
      __$$ChatTemplateDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'prompt') String prompt,
      @JsonKey(name: 'type') String type});
}

/// @nodoc
class __$$ChatTemplateDtoImplCopyWithImpl<$Res>
    extends _$ChatTemplateDtoCopyWithImpl<$Res, _$ChatTemplateDtoImpl>
    implements _$$ChatTemplateDtoImplCopyWith<$Res> {
  __$$ChatTemplateDtoImplCopyWithImpl(
      _$ChatTemplateDtoImpl _value, $Res Function(_$ChatTemplateDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? prompt = null,
    Object? type = null,
  }) {
    return _then(_$ChatTemplateDtoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      prompt: null == prompt
          ? _value.prompt
          : prompt // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatTemplateDtoImpl implements _ChatTemplateDto {
  const _$ChatTemplateDtoImpl(
      {@JsonKey(name: 'id') this.id = '',
      @JsonKey(name: 'name') this.name = '',
      @JsonKey(name: 'prompt') this.prompt = '',
      @JsonKey(name: 'type') this.type = ''});

  factory _$ChatTemplateDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatTemplateDtoImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'prompt')
  final String prompt;
  @override
  @JsonKey(name: 'type')
  final String type;

  @override
  String toString() {
    return 'ChatTemplateDto(id: $id, name: $name, prompt: $prompt, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatTemplateDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.prompt, prompt) || other.prompt == prompt) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, prompt, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatTemplateDtoImplCopyWith<_$ChatTemplateDtoImpl> get copyWith =>
      __$$ChatTemplateDtoImplCopyWithImpl<_$ChatTemplateDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatTemplateDtoImplToJson(
      this,
    );
  }
}

abstract class _ChatTemplateDto implements ChatTemplateDto {
  const factory _ChatTemplateDto(
      {@JsonKey(name: 'id') final String id,
      @JsonKey(name: 'name') final String name,
      @JsonKey(name: 'prompt') final String prompt,
      @JsonKey(name: 'type') final String type}) = _$ChatTemplateDtoImpl;

  factory _ChatTemplateDto.fromJson(Map<String, dynamic> json) =
      _$ChatTemplateDtoImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String get id;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'prompt')
  String get prompt;
  @override
  @JsonKey(name: 'type')
  String get type;
  @override
  @JsonKey(ignore: true)
  _$$ChatTemplateDtoImplCopyWith<_$ChatTemplateDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
