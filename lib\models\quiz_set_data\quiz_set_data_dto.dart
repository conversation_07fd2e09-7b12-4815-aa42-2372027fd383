// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'quiz_set_data_dto.freezed.dart';
part 'quiz_set_data_dto.g.dart';

@freezed
class QuizSetDataDto with _$QuizSetDataDto {
  const factory QuizSetDataDto({
    @JsonKey(name: 'set_id') @Default('') String setId,
    @JsonKey(name: 'name') @Default('') String name,
    @Json<PERSON>ey(name: 'questions_count') @Default(0) int questionsCount,
    @Json<PERSON>ey(name: 'difficulty') @Default('') String difficulty,
  }) = _QuizSetDataDto;

  factory QuizSetDataDto.fromJson(Map<String, dynamic> json) =>
      _$QuizSetDataDtoFromJson(json);
}
