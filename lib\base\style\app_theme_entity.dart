import 'dart:ui';

class AppThemeEntity {
  final int id;
  final String name;
  final Brightness brightness;

  // New color properties
  final String blue;
  final String gray;
  final String primary;
  final String secondary;
  final String neutral;
  final String summary;
  final String red;
  final String yellow;
  final String green;
  final String blue2;
  final String purple;
  final String violet;
  final String white;
  final String black;
  final String bg;

  const AppThemeEntity({
    required this.id,
    required this.name,
    required this.brightness,
    required this.blue,
    required this.gray,
    required this.primary,
    required this.secondary,
    required this.neutral,
    required this.summary,
    required this.red,
    required this.yellow,
    required this.blue2,
    required this.purple,
    required this.violet,
    required this.white,
    required this.green,
    required this.black,
    required this.bg,
  });
}
