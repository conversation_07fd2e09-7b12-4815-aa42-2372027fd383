// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_text_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$UploadTextState {
  CreateNoteWithTextOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  String get textContent => throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $UploadTextStateCopyWith<UploadTextState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadTextStateCopyWith<$Res> {
  factory $UploadTextStateCopyWith(
          UploadTextState value, $Res Function(UploadTextState) then) =
      _$UploadTextStateCopyWithImpl<$Res, UploadTextState>;
  @useResult
  $Res call(
      {CreateNoteWithTextOneShotEvent oneShotEvent,
      String textContent,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class _$UploadTextStateCopyWithImpl<$Res, $Val extends UploadTextState>
    implements $UploadTextStateCopyWith<$Res> {
  _$UploadTextStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? textContent = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithTextOneShotEvent,
      textContent: null == textContent
          ? _value.textContent
          : textContent // ignore: cast_nullable_to_non_nullable
              as String,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadTextStateImplCopyWith<$Res>
    implements $UploadTextStateCopyWith<$Res> {
  factory _$$UploadTextStateImplCopyWith(_$UploadTextStateImpl value,
          $Res Function(_$UploadTextStateImpl) then) =
      __$$UploadTextStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreateNoteWithTextOneShotEvent oneShotEvent,
      String textContent,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class __$$UploadTextStateImplCopyWithImpl<$Res>
    extends _$UploadTextStateCopyWithImpl<$Res, _$UploadTextStateImpl>
    implements _$$UploadTextStateImplCopyWith<$Res> {
  __$$UploadTextStateImplCopyWithImpl(
      _$UploadTextStateImpl _value, $Res Function(_$UploadTextStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? textContent = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_$UploadTextStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithTextOneShotEvent,
      textContent: null == textContent
          ? _value.textContent
          : textContent // ignore: cast_nullable_to_non_nullable
              as String,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UploadTextStateImpl implements _UploadTextState {
  const _$UploadTextStateImpl(
      {this.oneShotEvent = CreateNoteWithTextOneShotEvent.none,
      this.textContent = '',
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = ''});

  @override
  @JsonKey()
  final CreateNoteWithTextOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final String textContent;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;

  @override
  String toString() {
    return 'UploadTextState(oneShotEvent: $oneShotEvent, textContent: $textContent, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadTextStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.textContent, textContent) ||
                other.textContent == textContent) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions));
  }

  @override
  int get hashCode => Object.hash(runtimeType, oneShotEvent, textContent,
      summaryStyle, writingStyle, additionalInstructions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadTextStateImplCopyWith<_$UploadTextStateImpl> get copyWith =>
      __$$UploadTextStateImplCopyWithImpl<_$UploadTextStateImpl>(
          this, _$identity);
}

abstract class _UploadTextState implements UploadTextState {
  const factory _UploadTextState(
      {final CreateNoteWithTextOneShotEvent oneShotEvent,
      final String textContent,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions}) = _$UploadTextStateImpl;

  @override
  CreateNoteWithTextOneShotEvent get oneShotEvent;
  @override
  String get textContent;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  @JsonKey(ignore: true)
  _$$UploadTextStateImplCopyWith<_$UploadTextStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
