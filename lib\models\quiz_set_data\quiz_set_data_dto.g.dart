// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_set_data_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuizSetDataDtoImpl _$$QuizSetDataDtoImplFromJson(Map<String, dynamic> json) =>
    _$QuizSetDataDtoImpl(
      setId: json['set_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      questionsCount: (json['questions_count'] as num?)?.toInt() ?? 0,
      difficulty: json['difficulty'] as String? ?? '',
    );

Map<String, dynamic> _$$QuizSetDataDtoImplToJson(
        _$QuizSetDataDtoImpl instance) =>
    <String, dynamic>{
      'set_id': instance.setId,
      'name': instance.name,
      'questions_count': instance.questionsCount,
      'difficulty': instance.difficulty,
    };
