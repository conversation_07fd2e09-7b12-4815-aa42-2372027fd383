import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/lib.dart';

typedef FormatCallback = void Function(ExportType type);

class NoteOverflowMenu extends StatefulWidget {
  final NoteModel note;
  final bool isCommunityNote;
  final Function(BuildContext) onShowFolder;
  final MyNoteDetailCubit cubit;
  final FormatCallback onShowBottomSheet;
  final FocusNode focusNode;
  final Func0 onShowSharingView;
  final GlobalKey menuKey;
  final List<String> tabList;

  const NoteOverflowMenu({
    super.key,
    required this.note,
    required this.menuKey,
    required this.isCommunityNote,
    required this.onShowFolder,
    required this.cubit,
    required this.onShowBottomSheet,
    required this.focusNode,
    required this.onShowSharingView,
    required this.tabList,
  });

  @override
  State<NoteOverflowMenu> createState() => _NoteOverflowMenuState();
}

class _NoteOverflowMenuState extends State<NoteOverflowMenu>
    with SingleTickerProviderStateMixin {
  OverlayEntry? _menuOverlay;
  OverlayEntry? _subMenuOverlay;
  Offset _menuPosition = Offset.zero;
  late AnimationController _submenuAnimationController;

  @override
  void initState() {
    super.initState();
    _submenuAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _removeMenu();
    _submenuAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: widget.menuKey,
      onTapDown: (details) {
        _menuPosition = details.globalPosition;
        _showMenu(context);
      },
      child: Padding(
        padding: EdgeInsets.only(right: context.isTablet ? 12 : 12.w),
        child: SvgPicture.asset(
          Assets.icons.icMore,
          width: context.isTablet ? 24 : 24.w,
          height: context.isTablet ? 24 : 24.w,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainPrimary,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  void _showMenu(BuildContext context) {
    _removeMenu();

    final overlay = Overlay.of(context);

    _menuOverlay = OverlayEntry(
      builder: (_) => _MenuWidget(
        position: _menuPosition,
        items: _buildMainMenuItems(context),
        onDismiss: _removeMenu,
        onSubMenuRequested: _showSubMenu,
      ),
    );

    overlay.insert(_menuOverlay!);
  }

  List<MenuItem> _buildMainMenuItems(BuildContext context) {
    final isNoteFailed = widget.note.noteStatus == NoteStatus.error;
    return [
      MenuItem(
        isShowIconLeft: true,
        title: S.current.export,
        subItems: [
          MenuItem(
            isShowIconLeft: true,
            title: S.current.export,
            onTap: () {
              _removeSubMenu();
            },
            urlSvg: Assets.icons.icExport01,
          ),
          MenuItem(
            title: S.current.summary,
            onTap: () {
              _removeAll();
              widget.onShowBottomSheet(ExportType.summary);
              AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.note_detail_export_summary,
              );
            },
          ),
          MenuItem(
            title: S.current.transcript,
            onTap: () {
              _removeAll();
              widget.onShowBottomSheet(ExportType.transcript);
              AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.note_detail_export_transcript,
              );
            },
          ),
          if (widget.tabList.contains(S.current.flashcard))
            MenuItem(
              title: S.current.flashcard,
              onTap: () {
                _removeAll();
                widget.onShowBottomSheet(ExportType.flashcards);
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.note_detail_export_flashcard,
                );
              },
            ),
          if (widget.tabList.contains(S.current.quizzes))
            MenuItem(
              title: S.current.quiz,
              onTap: () {
                _removeAll();
                widget.onShowBottomSheet(ExportType.quiz);
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.note_detail_export_quiz,
                );
              },
            ),
        ],
        urlSvg: Assets.icons.icExport01,
      ),
      MenuItem(
        isShowIconLeft: true,
        title: S.current.copy,
        subItems: [
          MenuItem(
            isShowIconLeft: true,
            title: S.current.copy,
            onTap: () {
              _removeSubMenu();
            },
            urlSvg: Assets.icons.icCopy,
          ),
          MenuItem(
            title: S.current.summary,
            onTap: () async {
              _removeAll();
              await _copyToClipboard(widget.note.summary);
              widget.cubit.onShareSummaryEvent();
            },
          ),
          MenuItem(
            title: S.current.transcript,
            onTap: () async {
              _removeAll();
              await _copyToClipboard(widget.note.transcriptJson.isNotEmpty
                  ? widget.note.transcriptJson.getTranscriptJsonContent()
                  : widget.note.transcript);
              widget.cubit.onShareTranscriptEvent();
            },
          ),
        ],
        urlSvg: Assets.icons.icCopy,
      ),
      MenuItem(
        title: S.current.translate_note,
        onTap: () {
          _removeAll();
          widget.cubit.onTranslateNote();
        },
        urlSvg: Assets.icons.icTranslate,
      ),
      MenuItem(
        title: S.current.add_folder,
        onTap: () {
          _removeAll();
          widget.onShowFolder(context);
          widget.cubit.onAddFolderEvent();
        },
        urlSvg: Assets.icons.icMoveToFolder,
      ),
      if (!widget.isCommunityNote && !isNoteFailed)
        MenuItem(
          title: S.current.share_note_link,
          onTap: () {
            _removeAll();
            widget.onShowSharingView();
          },
          urlSvg: Assets.icons.icShareNote,
        ),
      if (!widget.isCommunityNote)
        MenuItem(
          title: S.current.delete_note_item,
          onTap: () {
            _removeAll();
            showNewCupertinoDialog(
              context: context,
              title: S.current.delete_note,
              message: S.current.content_delete_note,
              image: Assets.icons.icMascottDelete,
              cancelButton: S.current.cancel,
              confirmButton: S.current.delete,
              onCancel: () => widget.cubit.onCancelDeleteNote(),
              onConfirm: () =>
                  widget.cubit.onDeleteNote(isNoteFailed: isNoteFailed),
            );
            widget.cubit.onTapDeleteNoteEvent();
          },
          urlSvg: Assets.icons.icDelete,
        ),
    ];
  }

  void _showSubMenu(
      BuildContext context, Offset parentPosition, List<MenuItem> items) {
    _removeSubMenu();

    final overlay = Overlay.of(context);

    // Lấy vị trí chính xác của menu cha
    final RenderBox? renderBox =
        widget.menuKey.currentContext?.findRenderObject() as RenderBox?;

    /// vị trí toàn cục của menu cha
    final Offset? globalOffset = renderBox?.localToGlobal(Offset.zero);

    final double submenuLeft = globalOffset?.dx ?? parentPosition.dx;

    /// nằm ngay dưới menu cha
    final double submenuTop =
        (globalOffset?.dy ?? parentPosition.dy) + (renderBox?.size.height ?? 0);

    _submenuAnimationController.forward(from: 0);

    // Remove main menu before showing submenu
    _menuOverlay?.remove();
    _menuOverlay = null;

    _subMenuOverlay = OverlayEntry(
      builder: (_) => FadeTransition(
        opacity: _submenuAnimationController,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.2, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _submenuAnimationController,
            curve: Curves.easeOut,
          )),
          child: _MenuWidget(
            position: Offset(submenuLeft + 15, submenuTop),
            items: items,
            onDismiss: _removeAll,
            // Update to remove all menus when dismissing submenu
            onSubMenuRequested: _showSubMenu,
          ),
        ),
      ),
    );

    overlay.insert(_subMenuOverlay!);
  }

  void _removeMenu() {
    _menuOverlay?.remove();
    _menuOverlay = null;
    _removeSubMenu();
  }

  void _removeSubMenu() {
    _subMenuOverlay?.remove();
    _subMenuOverlay = null;
  }

  void _removeAll() {
    _menuOverlay?.remove();
    _menuOverlay = null;
    _removeSubMenu();
  }

  Future<void> _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
    await HapticFeedback.mediumImpact();
    CommonDialogs.showToast(S.current.copied_to_clipboard);
  }
}

class MenuItem {
  final String title;
  final VoidCallback? onTap;
  final List<MenuItem>? subItems;
  final String? urlSvg;
  final bool isShowIconLeft;

  MenuItem({
    required this.title,
    this.onTap,
    this.subItems,
    this.urlSvg,
    this.isShowIconLeft = false,
  });
}

class _MenuWidget extends StatelessWidget {
  final Offset position;
  final List<MenuItem> items;
  final VoidCallback onDismiss;
  final void Function(BuildContext, Offset, List<MenuItem>) onSubMenuRequested;

  const _MenuWidget({
    Key? key,
    required this.position,
    required this.items,
    required this.onDismiss,
    required this.onSubMenuRequested,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onDismiss,
      child: Stack(
        children: [
          Positioned.fill(child: Container(color: Colors.transparent)),
          Positioned(
            right: 12.w,
            top: position.dy + 12,
            child: Material(
              elevation: 8,
              color: context.colorScheme.mainNeutral,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: items.map((item) {
                  return InkWell(
                    onTap: () {
                      if (item.subItems != null && item.subItems!.isNotEmpty) {
                        final box = context.findRenderObject() as RenderBox;
                        final globalPosition = box.localToGlobal(Offset.zero);
                        onSubMenuRequested(
                            context, globalPosition, item.subItems!);
                      } else {
                        item.onTap?.call();
                      }
                    },
                    child: Container(
                      width: 200,
                      padding: EdgeInsets.only(
                        top: 12.h,
                        bottom: 12.h,
                        left: 8.w,
                        right: 14.w,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (item.isShowIconLeft)
                            SvgPicture.asset(
                              item.subItems != null && item.subItems!.isNotEmpty
                                  ? Assets.icons.icArrowRight
                                  : Assets.icons.icArrowUp,
                              width: context.isTablet ? 16 : 16.w,
                              height: context.isTablet ? 16 : 16.h,
                              colorFilter: ColorFilter.mode(
                                context.colorScheme.mainPrimary,
                                BlendMode.srcIn,
                              ),
                            ),
                          if (item.isShowIconLeft == false) ...[
                            context.isTablet
                                ? const SizedBox(
                                    width: 14,
                                  )
                                : AppConstants.kSpacingItemW14,
                          ],
                          Expanded(
                            child: CommonText(
                              item.title,
                              style: TextStyle(
                                fontSize: context.isTablet ? 16 : 14.sp,
                                fontWeight: FontWeight.w500,
                                color: item.title == S.current.delete_note_item
                                    ? AppColors.primaryRed
                                    : context.colorScheme.mainPrimary,
                              ),
                            ),
                          ),
                          item.urlSvg?.isNotEmpty == true
                              ? SvgPicture.asset(
                                  item.urlSvg!,
                                  width: context.isTablet ? 24 : 24.w,
                                  height: context.isTablet ? 24 : 24.h,
                                  colorFilter:
                                      item.title == S.current.delete_note_item
                                          ? const ColorFilter.mode(
                                              AppColors.primaryRed,
                                              BlendMode.srcIn,
                                            )
                                          : ColorFilter.mode(
                                              context.colorScheme.mainPrimary,
                                              BlendMode.srcIn,
                                            ),
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
