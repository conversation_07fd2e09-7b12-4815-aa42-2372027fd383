// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PurchaseState {
  List<PackageProductDto> get listPackage => throw _privateConstructorUsedError;
  PurchaseOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;
  int get selectedIndex => throw _privateConstructorUsedError;
  bool get isVisibleClose => throw _privateConstructorUsedError;
  bool get isEnableSwitch => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PurchaseStateCopyWith<PurchaseState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseStateCopyWith<$Res> {
  factory $PurchaseStateCopyWith(
          PurchaseState value, $Res Function(PurchaseState) then) =
      _$PurchaseStateCopyWithImpl<$Res, PurchaseState>;
  @useResult
  $Res call(
      {List<PackageProductDto> listPackage,
      PurchaseOneShotEvent oneShotEvent,
      int selectedIndex,
      bool isVisibleClose,
      bool isEnableSwitch});
}

/// @nodoc
class _$PurchaseStateCopyWithImpl<$Res, $Val extends PurchaseState>
    implements $PurchaseStateCopyWith<$Res> {
  _$PurchaseStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listPackage = null,
    Object? oneShotEvent = null,
    Object? selectedIndex = null,
    Object? isVisibleClose = null,
    Object? isEnableSwitch = null,
  }) {
    return _then(_value.copyWith(
      listPackage: null == listPackage
          ? _value.listPackage
          : listPackage // ignore: cast_nullable_to_non_nullable
              as List<PackageProductDto>,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as PurchaseOneShotEvent,
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isVisibleClose: null == isVisibleClose
          ? _value.isVisibleClose
          : isVisibleClose // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableSwitch: null == isEnableSwitch
          ? _value.isEnableSwitch
          : isEnableSwitch // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseStateImplCopyWith<$Res>
    implements $PurchaseStateCopyWith<$Res> {
  factory _$$PurchaseStateImplCopyWith(
          _$PurchaseStateImpl value, $Res Function(_$PurchaseStateImpl) then) =
      __$$PurchaseStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<PackageProductDto> listPackage,
      PurchaseOneShotEvent oneShotEvent,
      int selectedIndex,
      bool isVisibleClose,
      bool isEnableSwitch});
}

/// @nodoc
class __$$PurchaseStateImplCopyWithImpl<$Res>
    extends _$PurchaseStateCopyWithImpl<$Res, _$PurchaseStateImpl>
    implements _$$PurchaseStateImplCopyWith<$Res> {
  __$$PurchaseStateImplCopyWithImpl(
      _$PurchaseStateImpl _value, $Res Function(_$PurchaseStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listPackage = null,
    Object? oneShotEvent = null,
    Object? selectedIndex = null,
    Object? isVisibleClose = null,
    Object? isEnableSwitch = null,
  }) {
    return _then(_$PurchaseStateImpl(
      listPackage: null == listPackage
          ? _value._listPackage
          : listPackage // ignore: cast_nullable_to_non_nullable
              as List<PackageProductDto>,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as PurchaseOneShotEvent,
      selectedIndex: null == selectedIndex
          ? _value.selectedIndex
          : selectedIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isVisibleClose: null == isVisibleClose
          ? _value.isVisibleClose
          : isVisibleClose // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableSwitch: null == isEnableSwitch
          ? _value.isEnableSwitch
          : isEnableSwitch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$PurchaseStateImpl implements _PurchaseState {
  const _$PurchaseStateImpl(
      {final List<PackageProductDto> listPackage = const [],
      this.oneShotEvent = PurchaseOneShotEvent.none,
      this.selectedIndex = -1,
      this.isVisibleClose = false,
      this.isEnableSwitch = false})
      : _listPackage = listPackage;

  final List<PackageProductDto> _listPackage;
  @override
  @JsonKey()
  List<PackageProductDto> get listPackage {
    if (_listPackage is EqualUnmodifiableListView) return _listPackage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listPackage);
  }

  @override
  @JsonKey()
  final PurchaseOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final int selectedIndex;
  @override
  @JsonKey()
  final bool isVisibleClose;
  @override
  @JsonKey()
  final bool isEnableSwitch;

  @override
  String toString() {
    return 'PurchaseState(listPackage: $listPackage, oneShotEvent: $oneShotEvent, selectedIndex: $selectedIndex, isVisibleClose: $isVisibleClose, isEnableSwitch: $isEnableSwitch)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseStateImpl &&
            const DeepCollectionEquality()
                .equals(other._listPackage, _listPackage) &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.selectedIndex, selectedIndex) ||
                other.selectedIndex == selectedIndex) &&
            (identical(other.isVisibleClose, isVisibleClose) ||
                other.isVisibleClose == isVisibleClose) &&
            (identical(other.isEnableSwitch, isEnableSwitch) ||
                other.isEnableSwitch == isEnableSwitch));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_listPackage),
      oneShotEvent,
      selectedIndex,
      isVisibleClose,
      isEnableSwitch);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseStateImplCopyWith<_$PurchaseStateImpl> get copyWith =>
      __$$PurchaseStateImplCopyWithImpl<_$PurchaseStateImpl>(this, _$identity);
}

abstract class _PurchaseState implements PurchaseState {
  const factory _PurchaseState(
      {final List<PackageProductDto> listPackage,
      final PurchaseOneShotEvent oneShotEvent,
      final int selectedIndex,
      final bool isVisibleClose,
      final bool isEnableSwitch}) = _$PurchaseStateImpl;

  @override
  List<PackageProductDto> get listPackage;
  @override
  PurchaseOneShotEvent get oneShotEvent;
  @override
  int get selectedIndex;
  @override
  bool get isVisibleClose;
  @override
  bool get isEnableSwitch;
  @override
  @JsonKey(ignore: true)
  _$$PurchaseStateImplCopyWith<_$PurchaseStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
