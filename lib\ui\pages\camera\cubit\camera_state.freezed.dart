// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'camera_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CameraState {
  CreateNoteWithCameraOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  List<String> get selectedImagePaths => throw _privateConstructorUsedError;
  bool get hasSelectedImages => throw _privateConstructorUsedError;
  bool get isMergingImages => throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;
  bool get isCameraInitialized => throw _privateConstructorUsedError;
  bool get isCameraActive => throw _privateConstructorUsedError;
  FlashMode get flashMode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CameraStateCopyWith<CameraState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CameraStateCopyWith<$Res> {
  factory $CameraStateCopyWith(
          CameraState value, $Res Function(CameraState) then) =
      _$CameraStateCopyWithImpl<$Res, CameraState>;
  @useResult
  $Res call(
      {CreateNoteWithCameraOneShotEvent oneShotEvent,
      List<String> selectedImagePaths,
      bool hasSelectedImages,
      bool isMergingImages,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions,
      bool isCameraInitialized,
      bool isCameraActive,
      FlashMode flashMode});
}

/// @nodoc
class _$CameraStateCopyWithImpl<$Res, $Val extends CameraState>
    implements $CameraStateCopyWith<$Res> {
  _$CameraStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? selectedImagePaths = null,
    Object? hasSelectedImages = null,
    Object? isMergingImages = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
    Object? isCameraInitialized = null,
    Object? isCameraActive = null,
    Object? flashMode = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithCameraOneShotEvent,
      selectedImagePaths: null == selectedImagePaths
          ? _value.selectedImagePaths
          : selectedImagePaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      hasSelectedImages: null == hasSelectedImages
          ? _value.hasSelectedImages
          : hasSelectedImages // ignore: cast_nullable_to_non_nullable
              as bool,
      isMergingImages: null == isMergingImages
          ? _value.isMergingImages
          : isMergingImages // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      isCameraInitialized: null == isCameraInitialized
          ? _value.isCameraInitialized
          : isCameraInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      isCameraActive: null == isCameraActive
          ? _value.isCameraActive
          : isCameraActive // ignore: cast_nullable_to_non_nullable
              as bool,
      flashMode: null == flashMode
          ? _value.flashMode
          : flashMode // ignore: cast_nullable_to_non_nullable
              as FlashMode,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CameraStateImplCopyWith<$Res>
    implements $CameraStateCopyWith<$Res> {
  factory _$$CameraStateImplCopyWith(
          _$CameraStateImpl value, $Res Function(_$CameraStateImpl) then) =
      __$$CameraStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreateNoteWithCameraOneShotEvent oneShotEvent,
      List<String> selectedImagePaths,
      bool hasSelectedImages,
      bool isMergingImages,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions,
      bool isCameraInitialized,
      bool isCameraActive,
      FlashMode flashMode});
}

/// @nodoc
class __$$CameraStateImplCopyWithImpl<$Res>
    extends _$CameraStateCopyWithImpl<$Res, _$CameraStateImpl>
    implements _$$CameraStateImplCopyWith<$Res> {
  __$$CameraStateImplCopyWithImpl(
      _$CameraStateImpl _value, $Res Function(_$CameraStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? selectedImagePaths = null,
    Object? hasSelectedImages = null,
    Object? isMergingImages = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
    Object? isCameraInitialized = null,
    Object? isCameraActive = null,
    Object? flashMode = null,
  }) {
    return _then(_$CameraStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithCameraOneShotEvent,
      selectedImagePaths: null == selectedImagePaths
          ? _value._selectedImagePaths
          : selectedImagePaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      hasSelectedImages: null == hasSelectedImages
          ? _value.hasSelectedImages
          : hasSelectedImages // ignore: cast_nullable_to_non_nullable
              as bool,
      isMergingImages: null == isMergingImages
          ? _value.isMergingImages
          : isMergingImages // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      isCameraInitialized: null == isCameraInitialized
          ? _value.isCameraInitialized
          : isCameraInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      isCameraActive: null == isCameraActive
          ? _value.isCameraActive
          : isCameraActive // ignore: cast_nullable_to_non_nullable
              as bool,
      flashMode: null == flashMode
          ? _value.flashMode
          : flashMode // ignore: cast_nullable_to_non_nullable
              as FlashMode,
    ));
  }
}

/// @nodoc

class _$CameraStateImpl implements _CameraState {
  const _$CameraStateImpl(
      {this.oneShotEvent = CreateNoteWithCameraOneShotEvent.none,
      final List<String> selectedImagePaths = const [],
      this.hasSelectedImages = false,
      this.isMergingImages = false,
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = '',
      this.isCameraInitialized = false,
      this.isCameraActive = false,
      this.flashMode = FlashMode.off})
      : _selectedImagePaths = selectedImagePaths;

  @override
  @JsonKey()
  final CreateNoteWithCameraOneShotEvent oneShotEvent;
  final List<String> _selectedImagePaths;
  @override
  @JsonKey()
  List<String> get selectedImagePaths {
    if (_selectedImagePaths is EqualUnmodifiableListView)
      return _selectedImagePaths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedImagePaths);
  }

  @override
  @JsonKey()
  final bool hasSelectedImages;
  @override
  @JsonKey()
  final bool isMergingImages;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;
  @override
  @JsonKey()
  final bool isCameraInitialized;
  @override
  @JsonKey()
  final bool isCameraActive;
  @override
  @JsonKey()
  final FlashMode flashMode;

  @override
  String toString() {
    return 'CameraState(oneShotEvent: $oneShotEvent, selectedImagePaths: $selectedImagePaths, hasSelectedImages: $hasSelectedImages, isMergingImages: $isMergingImages, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions, isCameraInitialized: $isCameraInitialized, isCameraActive: $isCameraActive, flashMode: $flashMode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CameraStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._selectedImagePaths, _selectedImagePaths) &&
            (identical(other.hasSelectedImages, hasSelectedImages) ||
                other.hasSelectedImages == hasSelectedImages) &&
            (identical(other.isMergingImages, isMergingImages) ||
                other.isMergingImages == isMergingImages) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions) &&
            (identical(other.isCameraInitialized, isCameraInitialized) ||
                other.isCameraInitialized == isCameraInitialized) &&
            (identical(other.isCameraActive, isCameraActive) ||
                other.isCameraActive == isCameraActive) &&
            (identical(other.flashMode, flashMode) ||
                other.flashMode == flashMode));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      const DeepCollectionEquality().hash(_selectedImagePaths),
      hasSelectedImages,
      isMergingImages,
      summaryStyle,
      writingStyle,
      additionalInstructions,
      isCameraInitialized,
      isCameraActive,
      flashMode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CameraStateImplCopyWith<_$CameraStateImpl> get copyWith =>
      __$$CameraStateImplCopyWithImpl<_$CameraStateImpl>(this, _$identity);
}

abstract class _CameraState implements CameraState {
  const factory _CameraState(
      {final CreateNoteWithCameraOneShotEvent oneShotEvent,
      final List<String> selectedImagePaths,
      final bool hasSelectedImages,
      final bool isMergingImages,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions,
      final bool isCameraInitialized,
      final bool isCameraActive,
      final FlashMode flashMode}) = _$CameraStateImpl;

  @override
  CreateNoteWithCameraOneShotEvent get oneShotEvent;
  @override
  List<String> get selectedImagePaths;
  @override
  bool get hasSelectedImages;
  @override
  bool get isMergingImages;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  bool get isCameraInitialized;
  @override
  bool get isCameraActive;
  @override
  FlashMode get flashMode;
  @override
  @JsonKey(ignore: true)
  _$$CameraStateImplCopyWith<_$CameraStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
