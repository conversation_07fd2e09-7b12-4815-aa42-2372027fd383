// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flashcard_set_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FlashcardSetDtoImpl _$$FlashcardSetDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$FlashcardSetDtoImpl(
      setId: json['set_id'] as String? ?? '',
      noteId: json['note_id'] as String? ?? '',
      userId: json['user_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      difficulty: json['difficulty'] as String? ?? '',
      language: json['language'] as String? ?? '',
      cardsCount: (json['cards_count'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
    );

Map<String, dynamic> _$$FlashcardSetDtoImplToJson(
        _$FlashcardSetDtoImpl instance) =>
    <String, dynamic>{
      'set_id': instance.setId,
      'note_id': instance.noteId,
      'user_id': instance.userId,
      'name': instance.name,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'language': instance.language,
      'cards_count': instance.cardsCount,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
