plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id 'com.google.firebase.crashlytics'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.sota.ainotex"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.sota.ainotex"
        minSdkVersion 30
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true

    }
    flavorDimensions "env"
    productFlavors {
        dev {
            versionCode 41
            versionName "2.23.0"
            dimension "env"
            applicationId "com.sota.ainotex.dev"
            resValue("string", "app_name", "NoteX AI Dev")
        }

        staging {
            versionCode 41
            versionName "2.23.0"
            dimension "env"
            applicationId "com.sota.ainotex.stg"
            resValue("string", "app_name", "NoteX AI")
        }

        production {
            dimension "env"
            applicationId "com.sota.ainotex"
            resValue("string", "app_name", "NoteX AI")
            versionCode 41
            versionName "2.23.0"
        }
    }

    signingConfigs {
        create("dev") {
            keyAlias keystoreProperties['DEV_RELEASE_KEY_ALIAS']
            keyPassword keystoreProperties['DEV_RELEASE_KEY_PASSWORD']
            storeFile keystoreProperties['DEV_RELEASE_KEY_PATH'] ? file(keystoreProperties['DEV_RELEASE_KEY_PATH']) : null
            storePassword keystoreProperties['DEV_RELEASE_STORE_PASSWORD']
        }
        create("stg") {
            keyAlias keystoreProperties['STG_RELEASE_KEY_ALIAS']
            keyPassword keystoreProperties['STG_RELEASE_KEY_PASSWORD']
            storeFile keystoreProperties['STG_RELEASE_KEY_PATH'] ? file(keystoreProperties['STG_RELEASE_KEY_PATH']) : null
            storePassword keystoreProperties['STG_RELEASE_STORE_PASSWORD']
        }
        create("production") {
            keyAlias keystoreProperties['RELEASE_KEY_ALIAS']
            keyPassword keystoreProperties['RELEASE_KEY_PASSWORD']
            storeFile keystoreProperties['RELEASE_KEY_PATH'] ? file(keystoreProperties['RELEASE_KEY_PATH']) : null
            storePassword keystoreProperties['RELEASE_STORE_PASSWORD']
        }
    }

    buildTypes {
        debug {
            signingConfig = signingConfigs.getByName("dev")
            versionNameSuffix = "-debug"
        }
        release {
            signingConfig = signingConfigs.getByName("production")
            proguardFile(getDefaultProguardFile("proguard-android.txt"))
            proguardFile("proguard-rules.pro")
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation(platform("com.google.firebase:firebase-bom:33.0.0"))
    implementation("com.google.firebase:firebase-analytics")
    implementation("com.facebook.android:facebook-android-sdk:16.0.0")
    implementation("com.android.billingclient:billing-ktx:6.2.0")
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:review-ktx:2.0.1'
    implementation 'com.appsflyer:af-android-sdk:6.14.2'
    implementation "com.android.installreferrer:installreferrer:2.2"
    implementation 'com.huawei.hms:componentverifysdk:13.3.1.301'
    implementation "com.miui.referrer:homereferrer:1.0.0.6"
}
