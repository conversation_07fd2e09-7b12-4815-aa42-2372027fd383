// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$LoginState {
  LoginEntity get loginEntity => throw _privateConstructorUsedError;
  LoginOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;
  String get errorMessage => throw _privateConstructorUsedError;
  EmailLinkSendingState get emailLinkState =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $LoginStateCopyWith<LoginState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginStateCopyWith<$Res> {
  factory $LoginStateCopyWith(
          LoginState value, $Res Function(LoginState) then) =
      _$LoginStateCopyWithImpl<$Res, LoginState>;
  @useResult
  $Res call(
      {LoginEntity loginEntity,
      LoginOneShotEvent oneShotEvent,
      String errorMessage,
      EmailLinkSendingState emailLinkState});

  $LoginEntityCopyWith<$Res> get loginEntity;
}

/// @nodoc
class _$LoginStateCopyWithImpl<$Res, $Val extends LoginState>
    implements $LoginStateCopyWith<$Res> {
  _$LoginStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginEntity = null,
    Object? oneShotEvent = null,
    Object? errorMessage = null,
    Object? emailLinkState = null,
  }) {
    return _then(_value.copyWith(
      loginEntity: null == loginEntity
          ? _value.loginEntity
          : loginEntity // ignore: cast_nullable_to_non_nullable
              as LoginEntity,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as LoginOneShotEvent,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      emailLinkState: null == emailLinkState
          ? _value.emailLinkState
          : emailLinkState // ignore: cast_nullable_to_non_nullable
              as EmailLinkSendingState,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LoginEntityCopyWith<$Res> get loginEntity {
    return $LoginEntityCopyWith<$Res>(_value.loginEntity, (value) {
      return _then(_value.copyWith(loginEntity: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LoginStateImplCopyWith<$Res>
    implements $LoginStateCopyWith<$Res> {
  factory _$$LoginStateImplCopyWith(
          _$LoginStateImpl value, $Res Function(_$LoginStateImpl) then) =
      __$$LoginStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoginEntity loginEntity,
      LoginOneShotEvent oneShotEvent,
      String errorMessage,
      EmailLinkSendingState emailLinkState});

  @override
  $LoginEntityCopyWith<$Res> get loginEntity;
}

/// @nodoc
class __$$LoginStateImplCopyWithImpl<$Res>
    extends _$LoginStateCopyWithImpl<$Res, _$LoginStateImpl>
    implements _$$LoginStateImplCopyWith<$Res> {
  __$$LoginStateImplCopyWithImpl(
      _$LoginStateImpl _value, $Res Function(_$LoginStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginEntity = null,
    Object? oneShotEvent = null,
    Object? errorMessage = null,
    Object? emailLinkState = null,
  }) {
    return _then(_$LoginStateImpl(
      loginEntity: null == loginEntity
          ? _value.loginEntity
          : loginEntity // ignore: cast_nullable_to_non_nullable
              as LoginEntity,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as LoginOneShotEvent,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      emailLinkState: null == emailLinkState
          ? _value.emailLinkState
          : emailLinkState // ignore: cast_nullable_to_non_nullable
              as EmailLinkSendingState,
    ));
  }
}

/// @nodoc

class _$LoginStateImpl implements _LoginState {
  const _$LoginStateImpl(
      {this.loginEntity = const LoginEntity(),
      this.oneShotEvent = LoginOneShotEvent.none,
      this.errorMessage = '',
      this.emailLinkState = EmailLinkSendingState.none});

  @override
  @JsonKey()
  final LoginEntity loginEntity;
  @override
  @JsonKey()
  final LoginOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final EmailLinkSendingState emailLinkState;

  @override
  String toString() {
    return 'LoginState(loginEntity: $loginEntity, oneShotEvent: $oneShotEvent, errorMessage: $errorMessage, emailLinkState: $emailLinkState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginStateImpl &&
            (identical(other.loginEntity, loginEntity) ||
                other.loginEntity == loginEntity) &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.emailLinkState, emailLinkState) ||
                other.emailLinkState == emailLinkState));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, loginEntity, oneShotEvent, errorMessage, emailLinkState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginStateImplCopyWith<_$LoginStateImpl> get copyWith =>
      __$$LoginStateImplCopyWithImpl<_$LoginStateImpl>(this, _$identity);
}

abstract class _LoginState implements LoginState {
  const factory _LoginState(
      {final LoginEntity loginEntity,
      final LoginOneShotEvent oneShotEvent,
      final String errorMessage,
      final EmailLinkSendingState emailLinkState}) = _$LoginStateImpl;

  @override
  LoginEntity get loginEntity;
  @override
  LoginOneShotEvent get oneShotEvent;
  @override
  String get errorMessage;
  @override
  EmailLinkSendingState get emailLinkState;
  @override
  @JsonKey(ignore: true)
  _$$LoginStateImplCopyWith<_$LoginStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
