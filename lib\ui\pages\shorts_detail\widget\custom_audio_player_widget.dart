import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';
import 'package:note_x/lib.dart';

// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:pull_down_button/pull_down_button.dart';

class CustomAudioPlayerWidget extends StatefulWidget {
  final String audioFilePath;
  final String? audioUrl;
  final bool isCommunityNote;
  final bool fullScreen;
  final bool isShowMore;
  final Color backgroundColor;

  const CustomAudioPlayerWidget({
    Key? key,
    required this.audioFilePath,
    this.audioUrl,
    required this.isCommunityNote,
    this.fullScreen = false,
    this.isShowMore = true,
    required this.backgroundColor,
  }) : super(key: key);

  @override
  State<CustomAudioPlayerWidget> createState() =>
      _CustomAudioPlayerWidgetState();
}

class _CustomAudioPlayerWidgetState extends State<CustomAudioPlayerWidget> {
  late final AudioPlayer _audioPlayer;
  final ValueNotifier<Duration> _audioDuration = ValueNotifier(Duration.zero);
  final ValueNotifier<Duration> _audioPosition = ValueNotifier(Duration.zero);
  final ValueNotifier<double> _playbackSpeed = ValueNotifier(1.0);
  final List<double> _playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
  final AppCubit appCubit = GetIt.instance.get<AppCubit>();
  int _currentSpeedIndex = 2;
  bool _isDragging = false;
  bool _isMuted = false;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initAudioPlayer();
    _audioPlayer.positionStream.listen((position) {
      if (!_isDragging && mounted) {
        _audioPosition.value = position;
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _audioDuration.dispose();
    _audioPosition.dispose();
    _playbackSpeed.dispose();
    super.dispose();
  }

  Future<void> _initAudioPlayer() async {
    try {
      await (widget.audioUrl != null && widget.audioFilePath.isEmpty
          ? _audioPlayer.setUrl(widget.audioUrl!)
          : _audioPlayer.setFilePath(widget.audioFilePath));
      final duration = _audioPlayer.duration;
      setState(() => _audioDuration.value = duration ?? Duration.zero);
    } catch (e) {
      if (mounted) {
        CommonDialogs.showErrorDialog(
          context,
          title: S.current.unable_load_audio,
          content: e.toString().contains('-1009')
              ? S.current.no_internet_connection
              : e.toString(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<PlayerState>(
      stream: _audioPlayer.playerStateStream,
      builder: (context, snapshot) {
        final playerState = snapshot.data;
        final isPlaying = (playerState?.playing ?? false) &&
            playerState?.processingState != ProcessingState.completed;

        return SizedBox(
          width: widget.fullScreen ? double.infinity : null,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOutCubic,
            width: isPlaying || widget.fullScreen
                ? null
                : context.isTablet
                    ? 65
                    : 50,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 32.w),
              child: Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isPlaying || widget.fullScreen)
                        Expanded(
                          child: ClipRect(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildTimeDisplay(),
                                AppConstants.kSpacingItemW8,
                                Expanded(child: _buildProgressBar()),
                                AppConstants.kSpacingItemW8,
                                _buildTimeTotal(),
                                if (context.isTablet)
                                  AppConstants.kSpacingItemW4,
                                if (widget.isShowMore) _buildShareButton(),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                  AppConstants.kSpacingItem24,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: _handleSpeakerTap,
                        child: Container(
                          width: context.isTablet ? 48 : 48.w,
                          alignment: Alignment.centerLeft,
                          child: SvgPicture.asset(
                            _isMuted
                                ? Assets.icons.icLoudSpeakerMute
                                : Assets.icons.icLoudSpeaker,
                            width: context.isTablet ? 24 : 24.w,
                            height: context.isTablet ? 24 : 24.h,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: _handleRewindTap,
                        child: SvgPicture.asset(
                          Assets.icons.icForwardSlow5,
                          width: context.isTablet ? 24 : 24.w,
                          height: context.isTablet ? 24 : 24.h,
                          colorFilter: ColorFilter.mode(
                            context.colorScheme.mainPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      _buildPlayPauseButton(),
                      GestureDetector(
                        onTap: _handleFastForwardTap,
                        child: SvgPicture.asset(
                          Assets.icons.icForwardFast5,
                          width: context.isTablet ? 24 : 24.w,
                          height: context.isTablet ? 24 : 24.h,
                          colorFilter: ColorFilter.mode(
                            context.colorScheme.mainPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      _buildPlaybackSpeedButton(),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildShareButton() {
    return PullDownButton(
      itemBuilder: (pullDownContext) => [
        _buildMenuItem(
          S.of(context).share_audio_file,
          appCubit.isReverseView
              ? Assets.icons.icFlipShare2
              : Assets.icons.icShare2,
          () => widget.audioFilePath.isNotEmpty
              ? _shareAudioFile(pullDownContext)
              : _shareAudioLink(pullDownContext),
        ),
        // Nếu bạn muốn bỏ comment phần download, có thể thêm vào như sau:
        _buildMenuItem(
          S.of(context).download_audio_file,
          Assets.icons.icShare2,
          widget.audioUrl != null
              ? () => _downloadAudioFile(pullDownContext)
              : _saveAudioLink,
        ),
      ],
      buttonBuilder: (contextUi, showMenu) => IconButton(
        padding: EdgeInsets.all(context.isTablet ? 8 : 4.w),
        icon: SvgPicture.asset(
          Assets.icons.icMore,
          width: context.isTablet ? 32 : 20.w,
          height: context.isTablet ? 32 : 20.h,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainGray,
            BlendMode.srcIn,
          ),
        ),
        onPressed: showMenu,
      ),
    );
  }

  PullDownMenuItem _buildMenuItem(
      String title, String iconAsset, Function() onTap) {
    return PullDownMenuItem(
      itemTheme: PullDownMenuItemTheme(
        textStyle: TextStyle(fontSize: context.isTablet ? 20 : 14.sp),
      ),
      title: title,
      iconWidget: SvgPicture.asset(iconAsset,
          colorFilter: ColorFilter.mode(
            context.colorScheme.themeWhite,
            BlendMode.srcIn,
          )),
      onTap: onTap,
    );
  }

  Future<void> _shareAudioFile(BuildContext context) async {
    try {
      final file = File(widget.audioFilePath);
      HapticFeedback.lightImpact();
      if (await file.exists()) {
        // ignore: use_build_context_synchronously
        final box = context.findRenderObject() as RenderBox?;
        await ShareService().shareFile(
          filePath: file.path,
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
        );
      } else {
        _showErrorSnackBar(S.current.not_found_audio);
      }
    } catch (e) {
      _showErrorSnackBar(S.current.unable_share_audio);
    }
  }

  Future<void> _shareAudioLink(BuildContext context) async {
    if (widget.audioUrl != null) {
      final box = context.findRenderObject() as RenderBox?;
      await ShareService().shareLink(
        link: widget.audioUrl!,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      );
    }
  }

  Future<void> _downloadAudioFile(BuildContext context) async {
    try {
      /// Check for valid URL
      if (widget.audioUrl == null || widget.audioUrl!.isEmpty) {
        _showErrorSnackBar(S.current.unable_download_file);
        return;
      }

      final tempDir = await getTemporaryDirectory();
      final fileName = path.basename(widget.audioUrl!);
      final tempFilePath = path.join(tempDir.path, fileName);

      final response = await http.get(Uri.parse(widget.audioUrl!));
      if (response.statusCode == 200) {
        final file = File(tempFilePath);
        await file.writeAsBytes(response.bodyBytes);

        // ignore: use_build_context_synchronously
        final box = context.findRenderObject() as RenderBox?;
        // Sử dụng XFile để chia sẻ file
        await ShareService().shareFile(
          filePath: tempFilePath,
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
        );
      } else {
        _showErrorSnackBar(S.current.unable_download_file);
      }
    } catch (e) {
      _showErrorSnackBar(S.current.unable_download_file);
    }
  }

  Future<void> _saveAudioLink() async {}

  void _showSnackBar(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: backgroundColor),
    );
  }

  void _showErrorSnackBar(String message) => _showSnackBar(message, Colors.red);

  Widget _buildPlayPauseButton() {
    return StreamBuilder<PlayerState>(
      stream: _audioPlayer.playerStateStream,
      builder: (context, snapshot) {
        final playerState = snapshot.data;
        final playing = playerState?.playing;
        final processingState = playerState?.processingState;

        String iconAsset;
        VoidCallback onPressed;

        if (playing != true) {
          iconAsset = appCubit.isReverseView
              ? Assets.icons.icFlipPlayWhite
              : Assets.icons.icPlayWhite;
          onPressed = _audioPlayer.play;
        } else if (processingState != ProcessingState.completed) {
          iconAsset = Assets.icons.icPause;
          onPressed = _audioPlayer.pause;
        } else {
          iconAsset = appCubit.isReverseView
              ? Assets.icons.icFlipPlayWhite
              : Assets.icons.icPlayWhite;
          onPressed = () => _audioPlayer.seek(Duration.zero);
        }

        return GestureDetector(
          onTap: onPressed,
          child: Container(
            padding: EdgeInsets.all(context.isTablet ? 8 : 4.w),
            decoration: BoxDecoration(
              color: widget.backgroundColor,
              borderRadius: BorderRadius.circular(100.r),
            ),
            child: SvgPicture.asset(
              iconAsset,
              width: context.isTablet ? 42 : 36.w,
              height: context.isTablet ? 42 : 36.w,
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaybackSpeedButton() {
    return ValueListenableBuilder<double>(
      valueListenable: _playbackSpeed,
      builder: (context, speed, _) {
        return GestureDetector(
          onTap: _changePlaybackSpeed,
          child: Container(
            width: context.isTablet ? 48 : 48.w,
            alignment: Alignment.centerRight,
            child: CommonText(
              '${speed}x',
              style: TextStyle(
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w700,
                color: context.colorScheme.mainPrimary,
              ),
            ),
          ),
        );
      },
    );
  }

  void _changePlaybackSpeed() {
    HapticFeedback.lightImpact();
    if (_audioPlayer.playerState.processingState == ProcessingState.completed) {
      // Nếu audio đã kết thúc, reset về đầu trước khi thay đổi tốc độ
      _audioPlayer.seek(Duration.zero);
      _audioPlayer.play();
    }

    _currentSpeedIndex = (_currentSpeedIndex + 1) % _playbackSpeeds.length;
    double newSpeed = _playbackSpeeds[_currentSpeedIndex];
    _audioPlayer.setSpeed(newSpeed);
    _playbackSpeed.value = newSpeed;
  }

  Widget _buildProgressBar() {
    return ValueListenableBuilder<Duration>(
      valueListenable: _audioDuration,
      builder: (context, duration, child) {
        return ValueListenableBuilder<Duration>(
          valueListenable: _audioPosition,
          builder: (context, position, child) {
            return SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 4.0,
                thumbShape:
                    const RoundSliderThumbShape(enabledThumbRadius: 6.0),
                overlayShape:
                    const RoundSliderOverlayShape(overlayRadius: 10.0),
                activeTrackColor: context.colorScheme.mainBlue,
                inactiveTrackColor:
                    context.colorScheme.mainPrimary.withOpacity(0.1),
                thumbColor: AppColors.white,
                overlayColor: Colors.blue.withOpacity(0.4),
              ),
              child: Slider(
                min: 0.0,
                max: duration.inMilliseconds.toDouble(),
                value: position.inMilliseconds
                    .toDouble()
                    .clamp(0.0, duration.inMilliseconds.toDouble()),
                onChangeStart: (value) {
                  _isDragging = true;
                },
                onChanged: (value) {
                  _audioPosition.value = Duration(milliseconds: value.round());
                },
                onChangeEnd: (value) {
                  _isDragging = false;
                  _audioPlayer.seek(Duration(milliseconds: value.round()));
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTimeDisplay() {
    return StreamBuilder<Duration>(
      stream: _audioPlayer.positionStream,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        return Text(
          _formatDuration(position),
          style: TextStyle(
            fontSize: context.isTablet ? 17 : 14.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray,
          ),
        );
      },
    );
  }

  Widget _buildTimeTotal() {
    return StreamBuilder<Duration>(
      stream: _audioPlayer.positionStream,
      builder: (context, snapshot) {
        return Text(
          _formatDuration(_audioDuration.value),
          style: TextStyle(
            fontSize: context.isTablet ? 17 : 14.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray,
          ),
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  void _handleSpeakerTap() {
    setState(() {
      _isMuted = !_isMuted;
      _audioPlayer.setVolume(_isMuted ? 0.0 : 1.0);
    });
  }

  void _handleRewindTap() {
    final currentPosition = _audioPlayer.position;
    final newPosition = currentPosition - const Duration(seconds: 5);

    if (newPosition.inSeconds <= 0) {
      _audioPlayer.seek(Duration.zero);
    } else {
      _audioPlayer.seek(newPosition);
    }
  }

  void _handleFastForwardTap() {
    final currentPosition = _audioPlayer.position;
    final newPosition = currentPosition + const Duration(seconds: 5);
    final maxDuration = _audioDuration.value;

    if (newPosition > maxDuration) {
      _audioPlayer.seek(maxDuration);
    } else {
      _audioPlayer.seek(newPosition);
    }
  }
}
