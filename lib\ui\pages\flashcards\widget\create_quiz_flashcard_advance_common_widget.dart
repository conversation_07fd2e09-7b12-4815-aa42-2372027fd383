import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:note_x/lib.dart';

class CreateQuizFlashcardAdvanceCommonWidget extends StatefulWidget {
  final Function(String, String, String) onSubmit;
  final bool? isCreateQuiz;

  const CreateQuizFlashcardAdvanceCommonWidget({
    Key? key,
    required this.onSubmit,
    this.isCreateQuiz = true,
  }) : super(key: key);

  @override
  State<CreateQuizFlashcardAdvanceCommonWidget> createState() =>
      _CreateQuizFlashcardAdvanceCommonWidgetState();
}

class _CreateQuizFlashcardAdvanceCommonWidgetState
    extends State<CreateQuizFlashcardAdvanceCommonWidget> {
  // Constants for tooltip timing
  static const int kTooltipDelayMs = 300;
  static const int kTooltipDisplayDurationMs = 2000;

  final FocusNode _cardCountFocusNode = FocusNode();
  final ElTooltipController _tooltipController = ElTooltipController();

  bool _hasManuallyDismissedKeyboard = false;
  bool _tooltipShown = false;

  // ValueNotifier to track the state values
  late final ValueNotifier<Map<String, dynamic>> _stateValues;

  @override
  void initState() {
    super.initState();
    // Initialize with default values
    _stateValues = ValueNotifier({
      AppConstants.kCardCountKey: AppConstants.kDefaultCardCount,
      AppConstants.kDifficultyKey: AppConstants.kDefaultDifficulty,
      AppConstants.kTopicKey: AppConstants.kDefaultTopic,
      AppConstants.kAdvancedModeKey: AppConstants.kDefaultAdvancedMode,
    });
  }

  @override
  void dispose() {
    _cardCountFocusNode.dispose();
    _tooltipController.dispose();
    _stateValues.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Map<String, dynamic>>(
      valueListenable: _stateValues,
      builder: (context, stateValues, _) {
        // Get current state values
        final bool isAdvancedMode = stateValues[AppConstants.kAdvancedModeKey];
        final String cardCount = stateValues[AppConstants.kCardCountKey];
        final String difficulty = stateValues[AppConstants.kDifficultyKey];
        final String topic = stateValues[AppConstants.kTopicKey];

        // Handle tooltip based on current state
        if (mounted) {
          _handleTooltip(isAdvancedMode);
        }

        return GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            _hasManuallyDismissedKeyboard = true;
          },
          behavior: HitTestBehavior.opaque,
          child: SizedBox(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Advanced Mode toggle
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: CommonText(
                        S.current.advance_mode,
                        style: TextStyle(
                          color: context.colorScheme.mainGray,
                          fontWeight: FontWeight.w400,
                          fontSize: context.isTablet ? 16 : 14.sp,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 44.w,
                      height: 24.h,
                      child: Transform.scale(
                        scale: 0.8,
                        child: CupertinoSwitch(
                          value: isAdvancedMode,
                          onChanged: (value) {
                            if (!value) {
                              _tooltipController.hide();
                              _tooltipShown = false;
                            } else {
                              _tooltipShown =
                                  false; // Cho phép hiện lại khi bật
                            }
                            // Create a new map to ensure ValueListenableBuilder detects the change
                            final updatedValues =
                                Map<String, dynamic>.from(_stateValues.value);
                            updatedValues[AppConstants.kAdvancedModeKey] =
                                value;
                            _stateValues.value = updatedValues;
                          },
                          activeColor: context.colorScheme.mainBlue,
                          trackColor: context.colorScheme.mainGray,
                          thumbColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),

                /// Card Count
                AppConstants.kSpacingItem16,
                Row(
                  children: [
                    CommonText(
                      widget.isCreateQuiz == true
                          ? S.current.quiz_count
                          : S.current.card_count,
                      style: TextStyle(
                        fontSize: context.isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w400,
                        color: context.colorScheme.mainGray,
                      ),
                    ),
                    AppConstants.kSpacingItemW4,
                    IgnorePointer(
                      ignoring: !isAdvancedMode, // Ngăn mọi tương tác
                      child: ElTooltip(
                        controller: _tooltipController,
                        showModal: false,
                        padding: EdgeInsets.all(8.w),
                        content: Container(
                          constraints: BoxConstraints(maxWidth: 158.w),
                          child: CommonText(
                            widget.isCreateQuiz == true
                                ? S.current.max_30_quiz_sets
                                : S.current.max_30_cards_per_set,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: context.isTablet ? 12 : 10.sp,
                              color: context.colorScheme.themeWhite,
                            ),
                          ),
                        ),
                        position: ElTooltipPosition.bottomCenter,
                        color: context.colorScheme.mainBlue,
                        radius: Radius.circular(12.r),
                        child: SvgPicture.asset(
                          Assets.icons.icCommonInfoTooltip,
                          height: context.isTablet ? 16 : 16.w,
                          width: context.isTablet ? 16 : 16.w,
                          colorFilter: ColorFilter.mode(
                            context.colorScheme.mainPrimary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                AppConstants.kSpacingItem8,
                TextFieldWithIcon(
                  hintText: widget.isCreateQuiz == true
                      ? S.current.enter_quiz_count
                      : S.current.enter_card_count,
                  onValueChanged: (value) {
                    final updatedValues =
                        Map<String, dynamic>.from(_stateValues.value);
                    updatedValues[AppConstants.kCardCountKey] = value;
                    _stateValues.value = updatedValues;
                  },
                  isShowMaxLength: false,
                  svgIconPath: Assets.icons.icAutoCount,
                  enabled: isAdvancedMode,
                  textInputType: TextInputType.number,
                  maxValue: 30,
                  initialValue: cardCount == 'auto' ? '' : cardCount,
                  focusNode: _cardCountFocusNode,
                ),

                /// Card Difficulty
                AppConstants.kSpacingItem16,
                CommonText(
                  widget.isCreateQuiz == true
                      ? S.current.quiz_diff
                      : S.current.card_difficulty,
                  style: TextStyle(
                    color: context.colorScheme.mainGray,
                    fontWeight: FontWeight.w400,
                    fontSize: context.isTablet ? 16 : 14.sp,
                  ),
                ),
                AppConstants.kSpacingItem8,
                DifficultySelectorWidget(
                  enabled: isAdvancedMode,
                  onDifficultyChanged: (key) {
                    final updatedValues =
                        Map<String, dynamic>.from(_stateValues.value);
                    updatedValues[AppConstants.kDifficultyKey] = key;
                    _stateValues.value = updatedValues;
                  },
                  initialDifficulty: difficulty,
                ),

                /// Topic (optional)
                AppConstants.kSpacingItem16,
                CommonText(
                  S.current.topic_option,
                  style: TextStyle(
                    color: context.colorScheme.mainGray,
                    fontWeight: FontWeight.w400,
                    fontSize: context.isTablet ? 16 : 14.sp,
                  ),
                ),
                AppConstants.kSpacingItem8,
                TextFieldWithIcon(
                  textInputType: TextInputType.text,
                  hintText: S.current.add_focus,
                  onValueChanged: (value) {
                    final updatedValues =
                        Map<String, dynamic>.from(_stateValues.value);
                    updatedValues[AppConstants.kTopicKey] = value;
                    _stateValues.value = updatedValues;
                  },
                  svgIconPath: Assets.icons.icAddSpecific,
                  enabled: isAdvancedMode,
                  initialValue: topic,
                ),

                AppConstants.kSpacingItem220,
                AppCommonButton(
                  width: double.infinity,
                  height: context.isTablet ? 44 : 44.h,
                  borderRadius: BorderRadius.circular(24.r),
                  backgroundColor: context.colorScheme.mainBlue,
                  textWidget: Text(
                    widget.isCreateQuiz == true
                        ? S.current.content_button_quiz
                        : S.current.content_button_flashcard,
                    style: TextStyle(
                      fontSize: context.isTablet ? 18 : 16.sp,
                      fontWeight: FontWeight.w500,
                      color: context.colorScheme.themeWhite,
                    ),
                  ),
                  onPressed: () {
                    widget.onSubmit.call(
                      cardCount,
                      difficulty,
                      topic,
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleTooltip(bool isAdvancedMode) {
    if (!mounted) return;

    // Nếu advanced mode bị tắt, ẩn tooltip nếu đã được hiển thị trước đó
    if (!isAdvancedMode) {
      if (_tooltipShown) {
        // Chỉ gọi hide() nếu tooltip đã được hiển thị trước đó
        _tooltipController.hide();
        _tooltipShown = false;
      }
      return;
    }

    // Nếu tooltip chưa hiển thị và keyboard chưa bị người dùng tắt
    if (isAdvancedMode && !_tooltipShown && !_hasManuallyDismissedKeyboard) {
      _tooltipShown = true;

      WidgetsBinding.instance.addPostFrameCallback((_) async {
        // Đảm bảo tooltip trước đó bị tắt - chỉ khi tooltip đã được hiển thị
        if (_tooltipShown) {
          _tooltipController.hide();
        }

        await Future.delayed(const Duration(milliseconds: kTooltipDelayMs));

        if (!mounted) return;

        _tooltipController.show();

        await Future.delayed(
            const Duration(milliseconds: kTooltipDisplayDurationMs));

        if (!mounted) return;

        _tooltipController.hide();

        if (!_cardCountFocusNode.hasFocus) {
          _cardCountFocusNode.requestFocus();
        }
      });
    }
  }
}
