// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_detail_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuizDetailDtoImpl _$$QuizDetailDtoImplFromJson(Map<String, dynamic> json) =>
    _$QuizDetailDtoImpl(
      question: json['question'] as String? ?? '',
      answers: (json['answers'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      correctIndex: (json['correct_index'] as num?)?.toInt() ?? 0,
      difficulty: json['difficulty'] as String? ?? '',
      context: json['context'] as String? ?? '',
      transcriptJson: (json['transcript_json'] as List<dynamic>?)
              ?.map((e) => TranscriptDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$QuizDetailDtoImplToJson(_$QuizDetailDtoImpl instance) =>
    <String, dynamic>{
      'question': instance.question,
      'answers': instance.answers,
      'correct_index': instance.correctIndex,
      'difficulty': instance.difficulty,
      'context': instance.context,
      'transcript_json': instance.transcriptJson,
    };
