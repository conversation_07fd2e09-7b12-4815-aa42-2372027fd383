import 'dart:io';

import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class ImagePreviewGallery extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;
  final Function(int oldIndex, int newIndex)? onSwapImages;
  final Function(int index)? onDeleteImage;
  final Function()? onRefresh;

  const ImagePreviewGallery({
    Key? key,
    required this.imagePaths,
    required this.initialIndex,
    this.onSwapImages,
    this.onDeleteImage,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<ImagePreviewGallery> createState() => _ImagePreviewGalleryState();
}

class _ImagePreviewGalleryState extends State<ImagePreviewGallery> {
  late int _currentIndex;
  late SwiperController _swiperController;
  late ScrollController _scrollController;
  late List<String> _localImagePaths;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _swiperController = SwiperController();
    _scrollController = ScrollController();
    _localImagePaths = List.from(widget.imagePaths);

    // Initial scroll to make the selected image visible
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedImage();
    });
  }

  void _scrollToSelectedImage() {
    if (!_scrollController.hasClients) return;

    final itemWidth = 60.w + 8.w;
    final screenWidth = MediaQuery.of(context).size.width - 32.w;
    final maxScroll = _scrollController.position.maxScrollExtent;

    double targetScroll =
        (_currentIndex * itemWidth) - (screenWidth - itemWidth) / 2;

    targetScroll = targetScroll.clamp(0.0, maxScroll);

    _scrollController.animateTo(
      targetScroll,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    _swiperController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ImagePreviewGallery oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!listEquals(widget.imagePaths, oldWidget.imagePaths)) {
      setState(() {
        _localImagePaths = List.from(widget.imagePaths);
      });

      if (_localImagePaths.isEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Navigator.of(context).pop();
        });
        return;
      }

      if (_currentIndex >= _localImagePaths.length) {
        _currentIndex = _localImagePaths.length - 1;
        _swiperController.move(_currentIndex);
      }
    }
  }

  void _handleDeleteImage(int index) {
    widget.onDeleteImage!(index);

    setState(() {
      _localImagePaths.removeAt(index);
    });

    if (_localImagePaths.isEmpty) {
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      if (index == _currentIndex) {
        if (index == _localImagePaths.length) {
          _currentIndex = index - 1;
        }
      } else if (index < _currentIndex) {
        _currentIndex--;
      }

      _swiperController.move(_currentIndex);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedImage();
    });

    widget.onRefresh?.call();
  }

  void _handleImageTap(int index) {
    setState(() {
      _currentIndex = index;
      _swiperController.move(index);
    });
  }

  Widget _buildMainImageViewer() {
    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        width: MediaQuery.of(context).size.width,
        child: Stack(
          children: [
            Swiper(
              itemBuilder: (BuildContext context, int index) {
                return InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: Container(
                    color: context.colorScheme.mainBackground,
                    child: Center(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16.r),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width - 32.w,
                            maxHeight: MediaQuery.of(context).size.height * 0.7,
                          ),
                          child: Image.file(
                            File(_localImagePaths[index]),
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
              controller: _swiperController,
              itemCount: _localImagePaths.length,
              index: _currentIndex,
              loop: false,
              onIndexChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
                _scrollToSelectedImage();
              },
            ),
            // Image position indicator
            Positioned(
              top: 20,
              child: _buildImagePositionIndicator(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePositionIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(40),
      ),
      child: Row(
        children: [
          Container(
            width: 24.w,
            height: 24.h,
            decoration: BoxDecoration(
              color: context.colorScheme.mainPrimary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: EdgeInsets.all(3.w),
              child: SvgPicture.asset(
                Assets.icons.icMultipleImg,
                width: 12.w,
                height: 12.h,
              ),
            ),
          ),
          AppConstants.kSpacingItemW6,
          Text(
            '${_currentIndex + 1} ${S.current.of_index} ${_localImagePaths.length}',
            style: TextStyle(
              color: context.colorScheme.mainPrimary,
              fontWeight: FontWeight.w500,
              fontSize: context.isTablet ? 14 : 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageThumbnail(int index, {Key? key}) {
    return Stack(
      key: key,
      children: [
        Container(
          width: context.isTablet ? 60 : 60.w,
          height: context.isTablet ? 60 : 60.h,
          margin: EdgeInsets.only(right: context.isTablet ? 8 : 8.w),
          decoration: BoxDecoration(
            border: Border.all(
              color: _currentIndex == index
                  ? context.colorScheme.mainBlue
                  : Colors.transparent,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6.r),
            child: Image.file(
              File(_localImagePaths[index]),
              width: context.isTablet ? 60 : 60.w,
              height: context.isTablet ? 60 : 60.h,
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (widget.onDeleteImage != null)
          Positioned(
            top: 3,
            right: 12,
            child: GestureDetector(
              onTap: () => _handleDeleteImage(index),
              child: SvgPicture.asset(
                Assets.icons.icCloseBlack,
                width: context.isTablet ? 20 : 12.w,
                height: context.isTablet ? 20 : 12.h,
              ),
            ),
          ),
        Positioned(
          bottom: 0,
          left: 0,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withOpacity(0.3),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8.r),
                topRight: Radius.circular(8.r),
              ),
              boxShadow: const [
                BoxShadow(
                  color: AppColors.primaryBlue,
                  blurRadius: 30,
                  spreadRadius: -10,
                  blurStyle: BlurStyle.inner,
                ),
              ],
            ),
            child: Text(
              '${index + 1}',
              style: TextStyle(
                color: Colors.white,
                fontSize: context.isTablet ? 12 : 10.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildThumbnailGallery() {
    return Container(
      height: 70.h,
      padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
      margin: EdgeInsets.only(bottom: 8.h),
      child: widget.onSwapImages != null
          ? _buildReorderableGallery()
          : _buildSimpleGallery(),
    );
  }

  Widget _buildReorderableGallery() {
    return ReorderableListView.builder(
      scrollDirection: Axis.horizontal,
      scrollController: _scrollController,
      itemCount: _localImagePaths.length,
      padding: EdgeInsets.zero,
      proxyDecorator: (child, index, animation) {
        return AnimatedBuilder(
          animation: animation,
          builder: (BuildContext context, Widget? child) {
            return Material(
              elevation: 4.0 * animation.value,
              color: Colors.transparent,
              child: child,
            );
          },
          child: child,
        );
      },
      onReorderStart: (index) {
        setState(() {});
      },
      onReorder: (oldIndex, newIndex) {
        if (oldIndex < newIndex) {
          newIndex -= 1;
        }

        widget.onSwapImages!(oldIndex, newIndex);

        setState(() {
          final image = _localImagePaths.removeAt(oldIndex);
          _localImagePaths.insert(newIndex, image);

          if (_currentIndex == oldIndex) {
            _currentIndex = newIndex;
          } else if (_currentIndex > oldIndex && _currentIndex <= newIndex) {
            _currentIndex--;
          } else if (_currentIndex < oldIndex && _currentIndex >= newIndex) {
            _currentIndex++;
          }
        });

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _swiperController.move(_currentIndex);
          _scrollToSelectedImage();
        });

        widget.onRefresh?.call();
      },
      itemBuilder: (context, index) {
        return GestureDetector(
          key: ValueKey(_localImagePaths[index]),
          onTap: () => _handleImageTap(index),
          child: _buildImageThumbnail(index),
        );
      },
    );
  }

  Widget _buildSimpleGallery() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      controller: _scrollController,
      itemCount: _localImagePaths.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () => _handleImageTap(index),
          child: _buildImageThumbnail(index),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      appBar: AppBarWidget(
        title: S.current.image,
        isShowLeftButton: true,
        onPressed: () {
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: EventName.imagePreviewBackClicked,
          );
          Navigator.of(context).pop();
        },
      ),
      body: SafeArea(
        child: Column(
          children: [
            _buildMainImageViewer(),
            _buildThumbnailGallery(),
            CommonText(
              S.current.enables_swap,
              style: TextStyle(
                color: context.colorScheme.mainGray,
                fontSize: context.isTablet ? 12 : 10.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            AppConstants.kSpacingItem8,
          ],
        ),
      ),
    );
  }
}
