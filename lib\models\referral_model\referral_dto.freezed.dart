// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'referral_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ReferralDto _$ReferralDtoFromJson(Map<String, dynamic> json) {
  return _ReferralDto.fromJson(json);
}

/// @nodoc
mixin _$ReferralDto {
  @JsonKey(name: 'referral_code')
  String get referralCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'rewards')
  RewardDto get rewards => throw _privateConstructorUsedError;
  @JsonKey(name: 'available_credits')
  int get availableCredits => throw _privateConstructorUsedError;
  @JsonKey(name: 'your_referrals')
  int get yourReferrals => throw _privateConstructorUsedError;
  @JsonKey(name: 'used_credits')
  int get usedCredits => throw _privateConstructorUsedError;
  @JsonKey(name: 'earned_credits')
  int get earnedCredits => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReferralDtoCopyWith<ReferralDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReferralDtoCopyWith<$Res> {
  factory $ReferralDtoCopyWith(
          ReferralDto value, $Res Function(ReferralDto) then) =
      _$ReferralDtoCopyWithImpl<$Res, ReferralDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'referral_code') String referralCode,
      @JsonKey(name: 'rewards') RewardDto rewards,
      @JsonKey(name: 'available_credits') int availableCredits,
      @JsonKey(name: 'your_referrals') int yourReferrals,
      @JsonKey(name: 'used_credits') int usedCredits,
      @JsonKey(name: 'earned_credits') int earnedCredits});

  $RewardDtoCopyWith<$Res> get rewards;
}

/// @nodoc
class _$ReferralDtoCopyWithImpl<$Res, $Val extends ReferralDto>
    implements $ReferralDtoCopyWith<$Res> {
  _$ReferralDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = null,
    Object? rewards = null,
    Object? availableCredits = null,
    Object? yourReferrals = null,
    Object? usedCredits = null,
    Object? earnedCredits = null,
  }) {
    return _then(_value.copyWith(
      referralCode: null == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String,
      rewards: null == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as RewardDto,
      availableCredits: null == availableCredits
          ? _value.availableCredits
          : availableCredits // ignore: cast_nullable_to_non_nullable
              as int,
      yourReferrals: null == yourReferrals
          ? _value.yourReferrals
          : yourReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      usedCredits: null == usedCredits
          ? _value.usedCredits
          : usedCredits // ignore: cast_nullable_to_non_nullable
              as int,
      earnedCredits: null == earnedCredits
          ? _value.earnedCredits
          : earnedCredits // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RewardDtoCopyWith<$Res> get rewards {
    return $RewardDtoCopyWith<$Res>(_value.rewards, (value) {
      return _then(_value.copyWith(rewards: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReferralDtoImplCopyWith<$Res>
    implements $ReferralDtoCopyWith<$Res> {
  factory _$$ReferralDtoImplCopyWith(
          _$ReferralDtoImpl value, $Res Function(_$ReferralDtoImpl) then) =
      __$$ReferralDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'referral_code') String referralCode,
      @JsonKey(name: 'rewards') RewardDto rewards,
      @JsonKey(name: 'available_credits') int availableCredits,
      @JsonKey(name: 'your_referrals') int yourReferrals,
      @JsonKey(name: 'used_credits') int usedCredits,
      @JsonKey(name: 'earned_credits') int earnedCredits});

  @override
  $RewardDtoCopyWith<$Res> get rewards;
}

/// @nodoc
class __$$ReferralDtoImplCopyWithImpl<$Res>
    extends _$ReferralDtoCopyWithImpl<$Res, _$ReferralDtoImpl>
    implements _$$ReferralDtoImplCopyWith<$Res> {
  __$$ReferralDtoImplCopyWithImpl(
      _$ReferralDtoImpl _value, $Res Function(_$ReferralDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = null,
    Object? rewards = null,
    Object? availableCredits = null,
    Object? yourReferrals = null,
    Object? usedCredits = null,
    Object? earnedCredits = null,
  }) {
    return _then(_$ReferralDtoImpl(
      referralCode: null == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String,
      rewards: null == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as RewardDto,
      availableCredits: null == availableCredits
          ? _value.availableCredits
          : availableCredits // ignore: cast_nullable_to_non_nullable
              as int,
      yourReferrals: null == yourReferrals
          ? _value.yourReferrals
          : yourReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      usedCredits: null == usedCredits
          ? _value.usedCredits
          : usedCredits // ignore: cast_nullable_to_non_nullable
              as int,
      earnedCredits: null == earnedCredits
          ? _value.earnedCredits
          : earnedCredits // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReferralDtoImpl implements _ReferralDto {
  const _$ReferralDtoImpl(
      {@JsonKey(name: 'referral_code') this.referralCode = '',
      @JsonKey(name: 'rewards') this.rewards = const RewardDto(),
      @JsonKey(name: 'available_credits') this.availableCredits = 0,
      @JsonKey(name: 'your_referrals') this.yourReferrals = 0,
      @JsonKey(name: 'used_credits') this.usedCredits = 0,
      @JsonKey(name: 'earned_credits') this.earnedCredits = 0});

  factory _$ReferralDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReferralDtoImplFromJson(json);

  @override
  @JsonKey(name: 'referral_code')
  final String referralCode;
  @override
  @JsonKey(name: 'rewards')
  final RewardDto rewards;
  @override
  @JsonKey(name: 'available_credits')
  final int availableCredits;
  @override
  @JsonKey(name: 'your_referrals')
  final int yourReferrals;
  @override
  @JsonKey(name: 'used_credits')
  final int usedCredits;
  @override
  @JsonKey(name: 'earned_credits')
  final int earnedCredits;

  @override
  String toString() {
    return 'ReferralDto(referralCode: $referralCode, rewards: $rewards, availableCredits: $availableCredits, yourReferrals: $yourReferrals, usedCredits: $usedCredits, earnedCredits: $earnedCredits)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferralDtoImpl &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.rewards, rewards) || other.rewards == rewards) &&
            (identical(other.availableCredits, availableCredits) ||
                other.availableCredits == availableCredits) &&
            (identical(other.yourReferrals, yourReferrals) ||
                other.yourReferrals == yourReferrals) &&
            (identical(other.usedCredits, usedCredits) ||
                other.usedCredits == usedCredits) &&
            (identical(other.earnedCredits, earnedCredits) ||
                other.earnedCredits == earnedCredits));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, referralCode, rewards,
      availableCredits, yourReferrals, usedCredits, earnedCredits);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferralDtoImplCopyWith<_$ReferralDtoImpl> get copyWith =>
      __$$ReferralDtoImplCopyWithImpl<_$ReferralDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReferralDtoImplToJson(
      this,
    );
  }
}

abstract class _ReferralDto implements ReferralDto {
  const factory _ReferralDto(
          {@JsonKey(name: 'referral_code') final String referralCode,
          @JsonKey(name: 'rewards') final RewardDto rewards,
          @JsonKey(name: 'available_credits') final int availableCredits,
          @JsonKey(name: 'your_referrals') final int yourReferrals,
          @JsonKey(name: 'used_credits') final int usedCredits,
          @JsonKey(name: 'earned_credits') final int earnedCredits}) =
      _$ReferralDtoImpl;

  factory _ReferralDto.fromJson(Map<String, dynamic> json) =
      _$ReferralDtoImpl.fromJson;

  @override
  @JsonKey(name: 'referral_code')
  String get referralCode;
  @override
  @JsonKey(name: 'rewards')
  RewardDto get rewards;
  @override
  @JsonKey(name: 'available_credits')
  int get availableCredits;
  @override
  @JsonKey(name: 'your_referrals')
  int get yourReferrals;
  @override
  @JsonKey(name: 'used_credits')
  int get usedCredits;
  @override
  @JsonKey(name: 'earned_credits')
  int get earnedCredits;
  @override
  @JsonKey(ignore: true)
  _$$ReferralDtoImplCopyWith<_$ReferralDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
