import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

/// A specialized widget for displaying community notes.
/// This widget handles all community note-specific logic and UI.
class CommunityNoteListView extends StatefulWidget {
  final List<NoteModel> listNote;
  final Function(String)? onDeleteNote;
  final Function()? onNoteItemTap;

  const CommunityNoteListView({
    super.key,
    required this.listNote,
    this.onDeleteNote,
    this.onNoteItemTap,
  });

  @override
  State<CommunityNoteListView> createState() => _CommunityNoteListViewState();
}

class _CommunityNoteListViewState extends State<CommunityNoteListView> {
  final _localService = GetIt.instance.get<LocalService>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildCommunityNoteListView(context)),
      ],
    );
  }

  Widget _buildCommunityNoteListView(BuildContext context) {
    // For community notes, we always show a header at index 0
    final int totalItemCount = widget.listNote.length + 1;

    return ListView.builder(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).padding.bottom,
        left: context.isTablet ? 24 : 0,
        right: context.isTablet ? 24 : 0,
      ),
      itemCount: totalItemCount,
      itemBuilder: (context, index) {
        // Show header at index 0
        if (index == 0) {
          return _CommunityNoteHeader(isTablet: context.isTablet);
        }

        // Calculate the actual note index (offset by 1 for the header)
        final int noteIndex = index - 1;
        final note = widget.listNote[noteIndex];

        // Build and return the note item
        return _buildNoteItem(context, note, noteIndex);
      },
    );
  }

  Widget _buildNoteItem(BuildContext context, NoteModel note, int index) {
    return FadeAnimation(
      (1.0 + index) / 4,
      Padding(
        padding: context.isTablet
            ? const EdgeInsets.symmetric(horizontal: 10, vertical: 8)
            : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        child: CommunityItemNoteWidget(
          noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
          onTap: () {
            widget.onNoteItemTap?.call();
            _navigateToNoteDetail(context, note);
          },
        ),
      ),
    );
  }

  void _navigateToNoteDetail(BuildContext context, NoteModel note) async {
    final savedTabs = await _initTabBar();
    if (!mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MyNoteDetailPage(
          isCommunityNote: true,
          noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
          isTablet: context.isTablet,
          from: NoteDetailPageFrom.homeScreen,
          savedTabs: savedTabs,
        ),
      ),
    );
  }

  Future<List<TabType>> _initTabBar() async {
    return await _localService.loadSelectedItems();
  }
}

/// Header widget shown at the top of the community notes list
class _CommunityNoteHeader extends StatelessWidget {
  final bool isTablet;

  const _CommunityNoteHeader({required this.isTablet});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: isTablet
          ? const EdgeInsets.fromLTRB(10, 8, 10, 28)
          : EdgeInsets.fromLTRB(16.w, 6.h, 16.w, 10.h),
      padding: isTablet
          ? const EdgeInsets.symmetric(vertical: 24, horizontal: 132)
          : EdgeInsets.symmetric(vertical: 16.h, horizontal: 30.w),
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonText(
            S.current.import_note_links,
            style: TextStyle(
              fontSize: isTablet ? 18 : 16.sp,
              fontWeight: isTablet ? FontWeight.w600 : FontWeight.w600,
              color: context.colorScheme.mainPrimary,
            ),
          ),
          SizedBox(height: isTablet ? 8 : AppConstants.kSpacingItem8.height),
          CommonText(
            S.current.easily_import_shared_note_link,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: isTablet ? 14 : 12.sp,
              fontWeight: FontWeight.w400,
              color: context.colorScheme.mainGray,
            ),
          ),
          SizedBox(height: isTablet ? 24 : AppConstants.kSpacingItem16.height),
          Container(
            padding: isTablet
                ? const EdgeInsets.symmetric(horizontal: 42, vertical: 11)
                : EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: context.colorScheme.mainSecondary,
              borderRadius: BorderRadius.circular(isTablet ? 24 : 24.r),
            ),
            child: Text(
              S.current.coming_soon,
              style: TextStyle(
                fontSize: isTablet ? 14 : 12.sp,
                fontWeight: isTablet ? FontWeight.w600 : FontWeight.w500,
                color: context.colorScheme.mainPrimary.withOpacity(0.38),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
