// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flash_cards_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FlashCardDto _$FlashCardDtoFromJson(Map<String, dynamic> json) {
  return _FlashCardDto.fromJson(json);
}

/// @nodoc
mixin _$FlashCardDto {
  @JsonKey(name: 'flash_card')
  List<FlashCarDetailDto> get flashCards => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FlashCardDtoCopyWith<FlashCardDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlashCardDtoCopyWith<$Res> {
  factory $FlashCardDtoCopyWith(
          FlashCardDto value, $Res Function(FlashCardDto) then) =
      _$FlashCardDtoCopyWithImpl<$Res, FlashCardDto>;
  @useResult
  $Res call({@JsonKey(name: 'flash_card') List<FlashCarDetailDto> flashCards});
}

/// @nodoc
class _$FlashCardDtoCopyWithImpl<$Res, $Val extends FlashCardDto>
    implements $FlashCardDtoCopyWith<$Res> {
  _$FlashCardDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flashCards = null,
  }) {
    return _then(_value.copyWith(
      flashCards: null == flashCards
          ? _value.flashCards
          : flashCards // ignore: cast_nullable_to_non_nullable
              as List<FlashCarDetailDto>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlashCardDtoImplCopyWith<$Res>
    implements $FlashCardDtoCopyWith<$Res> {
  factory _$$FlashCardDtoImplCopyWith(
          _$FlashCardDtoImpl value, $Res Function(_$FlashCardDtoImpl) then) =
      __$$FlashCardDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'flash_card') List<FlashCarDetailDto> flashCards});
}

/// @nodoc
class __$$FlashCardDtoImplCopyWithImpl<$Res>
    extends _$FlashCardDtoCopyWithImpl<$Res, _$FlashCardDtoImpl>
    implements _$$FlashCardDtoImplCopyWith<$Res> {
  __$$FlashCardDtoImplCopyWithImpl(
      _$FlashCardDtoImpl _value, $Res Function(_$FlashCardDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flashCards = null,
  }) {
    return _then(_$FlashCardDtoImpl(
      flashCards: null == flashCards
          ? _value._flashCards
          : flashCards // ignore: cast_nullable_to_non_nullable
              as List<FlashCarDetailDto>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FlashCardDtoImpl implements _FlashCardDto {
  const _$FlashCardDtoImpl(
      {@JsonKey(name: 'flash_card')
      final List<FlashCarDetailDto> flashCards = const []})
      : _flashCards = flashCards;

  factory _$FlashCardDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FlashCardDtoImplFromJson(json);

  final List<FlashCarDetailDto> _flashCards;
  @override
  @JsonKey(name: 'flash_card')
  List<FlashCarDetailDto> get flashCards {
    if (_flashCards is EqualUnmodifiableListView) return _flashCards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_flashCards);
  }

  @override
  String toString() {
    return 'FlashCardDto(flashCards: $flashCards)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlashCardDtoImpl &&
            const DeepCollectionEquality()
                .equals(other._flashCards, _flashCards));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_flashCards));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FlashCardDtoImplCopyWith<_$FlashCardDtoImpl> get copyWith =>
      __$$FlashCardDtoImplCopyWithImpl<_$FlashCardDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FlashCardDtoImplToJson(
      this,
    );
  }
}

abstract class _FlashCardDto implements FlashCardDto {
  const factory _FlashCardDto(
      {@JsonKey(name: 'flash_card')
      final List<FlashCarDetailDto> flashCards}) = _$FlashCardDtoImpl;

  factory _FlashCardDto.fromJson(Map<String, dynamic> json) =
      _$FlashCardDtoImpl.fromJson;

  @override
  @JsonKey(name: 'flash_card')
  List<FlashCarDetailDto> get flashCards;
  @override
  @JsonKey(ignore: true)
  _$$FlashCardDtoImplCopyWith<_$FlashCardDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
