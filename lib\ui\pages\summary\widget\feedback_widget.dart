import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:note_x/lib.dart';

class FeedbackWidget extends StatefulWidget {
  final VoidCallback onClose;
  final bool isFeedbackSubmitted;
  final AppCubit appCubit;
  final Function(bool) onFeedbackSubmitted;
  final VoidCallback onStateChanged;
  final String noteId;

  const FeedbackWidget({
    Key? key,
    required this.onClose,
    required this.isFeedbackSubmitted,
    required this.onFeedbackSubmitted,
    required this.appCubit,
    required this.noteId,
    required this.onStateChanged,
  }) : super(key: key);

  @override
  FeedbackWidgetState createState() => FeedbackWidgetState();
}

class FeedbackWidgetState extends State<FeedbackWidget> {
  SelectionType? selectionType;
  bool isTextFieldVisible = false;
  bool _isSubmitted = false;
  bool _hasSubmittedFeedback = false;
  int starNumber = 0;
  String comment = '';
  bool _hasRated = false;
  bool _satisfiedTextDisplayed = false;
  final FocusNode _focusNode = FocusNode();

  final List<String> issues = [
    S.current.recording_quality,
    S.current.transcription_precision,
    S.current.summary_usefulness,
    S.current.others
  ];
  late List<bool> isSelected;

  @override
  void initState() {
    super.initState();
    isSelected = List.generate(issues.length, (_) => false);
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 16.h),
      width: context.isTablet
          ? MediaQuery.of(context).size.width * 0.5
          : double.infinity,
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(24.r),
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (_isSubmitted || starNumber >= 4) ...[
                if (_satisfiedTextDisplayed)
                  _buildSatisfiedText()
                else
                  _buildRatingSection(),
              ] else if (!_hasRated) ...[
                _buildRatingSection(),
              ] else if (starNumber < 4) ...[
                _buildFeedbackForm(),
              ],
            ],
          ),
          if (!_satisfiedTextDisplayed) _buildCloseButton(),
        ],
      ),
    );
  }

  Widget _buildRatingSection() {
    return Padding(
      padding: context.isTablet
          ? const EdgeInsets.symmetric(vertical: 24, horizontal: 28)
          : EdgeInsets.symmetric(vertical: 24.h, horizontal: 28.w),
      child: Column(
        children: [
          CommonText(
            S.current.satisfied_quality,
            style: TextStyle(
              fontSize: context.isTablet ? 18 : 16.sp,
              fontWeight: context.isTablet ? FontWeight.w700 : FontWeight.w600,
              color: context.colorScheme.mainGray,
            ),
          ),
          context.isTablet
              ? const SizedBox(height: 16)
              : AppConstants.kSpacingItem16,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              return GestureDetector(
                onTap: (starNumber > 0)
                    ? null
                    : () {
                        _handleRating(index + 1);
                        AnalyticsService.logAnalyticsEventNoParam(
                          eventName: EventName.note_summary_rating,
                        );
                      },
                child: Padding(
                  padding: context.isTablet
                      ? const EdgeInsets.symmetric(horizontal: 8)
                      : EdgeInsets.symmetric(horizontal: 8.w),
                  child: SvgPicture.asset(
                    index < starNumber
                        ? Assets.icons.icStarFilled
                        : Assets.icons.icStarOutline,
                    width: context.isTablet ? 30 : 30.w,
                    height: context.isTablet ? 30 : 30.h,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackForm() {
    return Padding(
      padding: context.isTablet
          ? const EdgeInsets.symmetric(vertical: 24, horizontal: 28)
          : EdgeInsets.symmetric(vertical: 24.h, horizontal: 28.w),
      child: Column(
        children: [
          CommonText(
            S.current.can_improve,
            style: TextStyle(
              fontSize: context.isTablet ? 18 : 16.sp,
              fontWeight: context.isTablet ? FontWeight.w700 : FontWeight.w600,
              color: context.colorScheme.mainGray,
            ),
          ),
          context.isTablet
              ? const SizedBox(height: 10)
              : AppConstants.kSpacingItem18,
          _buildSelectionOptions(),
          if (isSelected[SelectionType.others.index]) ...[
            _buildWhatImproveText(),
            _buildFeedbackTextField(),
          ],
          context.isTablet
              ? const SizedBox(height: 10)
              : AppConstants.kSpacingItem10,
          _buildSubmitButton(),
        ],
      ),
    );
  }

  Widget _buildSelectionOptions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: issues.asMap().entries.map((entry) {
            int index = entry.key;
            String option = entry.value;
            bool selected = isSelected[index];

            return GestureDetector(
              onTap: () {
                setState(() {
                  isSelected[index] = !selected;
                  if (!isSelected[index] &&
                      index == SelectionType.others.index) {
                    comment = '';
                  }
                  if (index == SelectionType.others.index) {
                    Future.delayed(const Duration(milliseconds: 100), () {
                      setState(() {
                        widget.onStateChanged();
                        if (isSelected[index]) {
                          _focusNode.requestFocus();
                        }
                      });
                    });
                  }
                });
              },
              child: Container(
                margin: context.isTablet
                    ? const EdgeInsets.symmetric(vertical: 6)
                    : EdgeInsets.symmetric(vertical: 6.0.h),
                padding: context.isTablet
                    ? const EdgeInsets.symmetric(vertical: 10, horizontal: 16)
                    : EdgeInsets.symmetric(
                        vertical: 8.0.h,
                        horizontal: 16.0.w,
                      ),
                decoration: BoxDecoration(
                  color: selected
                      ? context.colorScheme.mainBlue.withOpacity(0.24)
                      : context.colorScheme.mainSecondary,
                  borderRadius: BorderRadius.circular(16.0.r),
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    color: selected
                        ? context.colorScheme.mainBlue
                        : context.colorScheme.mainGray,
                    fontWeight: FontWeight.w400,
                    fontSize: context.isTablet ? 16 : 14.sp,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        AppConstants.kSpacingItem2,
      ],
    );
  }

  Widget _buildWhatImproveText() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CommonText(
          S.current.what_improve,
          style: TextStyle(
            fontSize: context.isTablet ? 16 : 14.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray,
          ),
        ),
      ],
    );
  }

  Widget _buildFeedbackTextField() {
    return Container(
      margin: context.isTablet
          ? const EdgeInsets.fromLTRB(0, 4, 0, 8)
          : EdgeInsets.fromLTRB(0.w, 4.h, 0.w, 8.h),
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(48.r),
        color: context.colorScheme.mainSecondary,
      ),
      child: TextField(
        focusNode: _focusNode,
        onTap: () {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Future.delayed(const Duration(milliseconds: 300), () {
              setState(() {
                widget.onStateChanged();
              });
            });
          });
        },
        onChanged: (value) {
          comment = value;
        },
        style: TextStyle(
          fontSize: context.isTablet ? 16 : 14.sp,
          fontWeight: FontWeight.w400,
          color: context.colorScheme.mainPrimary,
        ),
        decoration: InputDecoration(
          hintText: S.current.enter_feedback,
          contentPadding: context.isTablet
              ? const EdgeInsets.symmetric(horizontal: 16, vertical: 12)
              : EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          hintStyle: TextStyle(
            fontSize: context.isTablet ? 14 : 12.sp,
            fontWeight: FontWeight.w400,
            height: 1.0,
            color: context.colorScheme.mainPrimary.withOpacity(0.38),
          ),
          border: InputBorder.none,
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    bool hasOthersSelected = isSelected[SelectionType.others.index];
    bool hasOtherOptions = isSelected
        .asMap()
        .entries
        .where((entry) => entry.key != SelectionType.others.index)
        .any((entry) => entry.value);

    bool hasValidInput =
        hasOtherOptions || (hasOthersSelected && comment.trim().isNotEmpty);
    return Center(
      child: CommonButton(
        minWidth: MediaQuery.of(context).size.width,
        height: context.isTablet ? 48 : 48.h,
        radiusSize: 48.r,
        btnTextStyle: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          fontWeight: FontWeight.w600,
          color: hasValidInput && !_hasSubmittedFeedback
              ? context.colorScheme.themeWhite
              : context.colorScheme.mainPrimary.withOpacity(0.38),
        ),
        onPress: (hasValidInput && !_hasSubmittedFeedback)
            ? () async {
                setState(() {
                  _hasSubmittedFeedback = true;
                });
                List<String> selectedIssues = issues
                    .asMap()
                    .entries
                    .where((entry) => isSelected[entry.key])
                    .map((entry) => entry.value)
                    .toList();

                await submitFeedback(comment, selectedIssues);
                setState(() {
                  _isSubmitted = true;
                  _hasRated = true;
                  _satisfiedTextDisplayed = true;
                });
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.note_summary_rating_submit,
                );
              }
            : null,
        backgroundColor: hasValidInput && !_hasSubmittedFeedback
            ? context.colorScheme.mainBlue
            : context.colorScheme.mainSecondary,
        btnText: S.current.submit_button,
      ),
    );
  }

  Widget _buildSatisfiedText() {
    if (_satisfiedTextDisplayed) {
      widget.appCubit.incrementFeedbackCount();
      Future.delayed(const Duration(seconds: 3), () {
        _handleClose();
      });
    }
    return Container(
      padding: context.isTablet
          ? const EdgeInsets.symmetric(vertical: 47, horizontal: 28)
          : EdgeInsets.symmetric(vertical: 47.h, horizontal: 28.w),
      child: Center(
        child: Text(
          S.current.satisfied,
          style: TextStyle(
            fontSize: context.isTablet ? 18 : 16.sp,
            fontWeight: context.isTablet ? FontWeight.w700 : FontWeight.w600,
            color: context.colorScheme.mainGray,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildCloseButton() {
    return Positioned(
      top: context.isTablet ? 8.0 : 0.0,
      right: isReverseView()
          ? context.isTablet
              ? 8.0
              : 0.0
          : null,
      left: isReverseView()
          ? null
          : context.isTablet
              ? 8.0
              : 0.0,
      child: IconButton(
        icon: SvgPicture.asset(
          Assets.icons.icCloseWhite,
          width: context.isTablet ? 24 : 24.w,
          height: context.isTablet ? 24 : 24.h,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainGray,
            BlendMode.srcIn,
          ),
        ),
        onPressed: _handleClose,
      ),
    );
  }

  void _handleClose() async {
    if (_hasRated && !_isSubmitted) {
      await submitFeedback('', []);
    }
    widget.appCubit.onUserHideFeedbackView();
    widget.onClose();
    widget.onFeedbackSubmitted(false);
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_summary_rating_close,
    );
  }

  void _handleRating(int rating) {
    if (starNumber > 0) return;

    setState(() {
      starNumber = rating;

      if (rating >= 4) {
        _isSubmitted = true;
        _hasRated = true;
        widget.appCubit.incrementFeedbackCount();

        submitFeedback('', []);

        Future.delayed(const Duration(seconds: 1), () {
          setState(() {
            _satisfiedTextDisplayed = true;
            widget.onStateChanged();
          });
        });

        Future.delayed(const Duration(milliseconds: 500), () async {
          if (rating == 5) {
            final InAppReview inAppReview = InAppReview.instance;
            if (await inAppReview.isAvailable()) {
              inAppReview.requestReview();
            }
          }
        });
      } else {
        Future.delayed(const Duration(seconds: 1), () {
          setState(() {
            _hasRated = true;
            isTextFieldVisible = true;
            widget.onStateChanged();
          });
        });
      }

      isTextFieldVisible = rating < 4;
      widget.onStateChanged();
    });
  }

  Future<void> submitFeedback(
      String comment, List<String> selectedIssues) async {
    final feedbackService = FeedbackApiServiceImpl();
    try {
      await feedbackService.sendFeedback(
          widget.noteId, selectedIssues, starNumber, comment);
      setState(() {
        _isSubmitted = true;
        _hasRated = true;
      });
      widget.onFeedbackSubmitted(true);
    } catch (e) {
      // Handle error
    }
  }
}

enum SelectionType {
  recordingQuality,
  transcriptionPrecision,
  summaryUsefulness,
  others
}
