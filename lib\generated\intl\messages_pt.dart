// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt';

  static String m0(date) => "Seu período de teste expirará em ${date} dias.";

  static String m1(images) => "${images} fotos foram carregadas";

  static String m2(price, date) =>
      "Sua próxima cobrança de ${price} será em ${date}.";

  static String m3(uid) => "uid ${uid} copiado para a área de transferência!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "Regenerate": MessageLookupByLibrary.simpleMessage("Re-gerar"),
    "a_to_z": MessageLookupByLibrary.simpleMessage("A a Z"),
    "about_us": MessageLookupByLibrary.simpleMessage("Sobre nós"),
    "access_notex_web": MessageLookupByLibrary.simpleMessage(
      "Acesse NoteX web",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Conta"),
    "account_basic": MessageLookupByLibrary.simpleMessage("Básico"),
    "account_content_basic": MessageLookupByLibrary.simpleMessage(
      "Experiência de IA limitada",
    ),
    "account_content_pro": MessageLookupByLibrary.simpleMessage(
      "Desbloquear experiência de IA ilimitada",
    ),
    "account_lifetime": MessageLookupByLibrary.simpleMessage("Vitalício"),
    "achieve_more": MessageLookupByLibrary.simpleMessage("CONQUISTE MAIS"),
    "action_items": MessageLookupByLibrary.simpleMessage("Itens de Ação"),
    "actionable_intelligence": MessageLookupByLibrary.simpleMessage(
      "inteligência acionável",
    ),
    "active_description": MessageLookupByLibrary.simpleMessage(
      "Descrição ativa não encontrada.",
    ),
    "active_recall": MessageLookupByLibrary.simpleMessage("recall ativo"),
    "add_folder": MessageLookupByLibrary.simpleMessage("Mover para pasta"),
    "add_note": MessageLookupByLibrary.simpleMessage("Adicionar nota"),
    "add_password": MessageLookupByLibrary.simpleMessage("Adicionar senha"),
    "add_password_to_public": MessageLookupByLibrary.simpleMessage(
      "Adicionar senha ao link público",
    ),
    "add_to": MessageLookupByLibrary.simpleMessage("Mover para"),
    "add_to_notes": MessageLookupByLibrary.simpleMessage("Adicionar às Notas"),
    "additional_ins": MessageLookupByLibrary.simpleMessage(
      "Instruções Adicionais (opc.)",
    ),
    "advance_mode": MessageLookupByLibrary.simpleMessage("Modo avançado"),
    "advanced": MessageLookupByLibrary.simpleMessage("Avançado"),
    "afternoon_content": MessageLookupByLibrary.simpleMessage(
      "Pequenas notas, grande impacto",
    ),
    "afternoon_content_1": MessageLookupByLibrary.simpleMessage(
      "Pensamentos capturados, mente livre",
    ),
    "afternoon_content_3": MessageLookupByLibrary.simpleMessage(
      "Ordem em meio ao caos",
    ),
    "afternoon_content_4": MessageLookupByLibrary.simpleMessage(
      "Suas ideias, organizadas",
    ),
    "afternoon_content_5": MessageLookupByLibrary.simpleMessage(
      "Clareza em progresso",
    ),
    "afternoon_content_6": MessageLookupByLibrary.simpleMessage(
      "Guarde o que importa",
    ),
    "ai_audio_transcription_per_day": MessageLookupByLibrary.simpleMessage(
      "3 Transcrições AI de Áudio por dia *",
    ),
    "ai_chat": MessageLookupByLibrary.simpleMessage("Nova IA"),
    "ai_chat_assistant": MessageLookupByLibrary.simpleMessage(
      "Assistente de Chat AI",
    ),
    "ai_chat_with_notes": MessageLookupByLibrary.simpleMessage(
      "Chat AI com Notas",
    ),
    "ai_insight": MessageLookupByLibrary.simpleMessage("Insight AI"),
    "ai_learning": MessageLookupByLibrary.simpleMessage("Aprendizado AI"),
    "ai_learning_companion": MessageLookupByLibrary.simpleMessage(
      "Sou a Nova IA do NoteX",
    ),
    "ai_note_create": MessageLookupByLibrary.simpleMessage(
      "Criação de Notas AI",
    ),
    "ai_note_creation": MessageLookupByLibrary.simpleMessage(
      "Criação de Notas AI",
    ),
    "ai_note_from": MessageLookupByLibrary.simpleMessage("Notas AI de Áudio"),
    "ai_notes_10": MessageLookupByLibrary.simpleMessage(
      "Notas AI ilimitadas de YouTube e Documentos",
    ),
    "ai_notes_3": MessageLookupByLibrary.simpleMessage(
      "3 notas de IA por dia de gravações e uploads de áudio (até 60min por arquivo)",
    ),
    "ai_notes_from": MessageLookupByLibrary.simpleMessage(
      "Notas AI de \nYouTube, Web, Docs",
    ),
    "ai_short_1": MessageLookupByLibrary.simpleMessage(
      "3 gerações de vídeos curtos de IA por dia",
    ),
    "ai_short_3": MessageLookupByLibrary.simpleMessage(
      "5 Vídeos Curtos AI por dia (beta)",
    ),
    "ai_short_video": MessageLookupByLibrary.simpleMessage("Vídeos Curtos AI"),
    "ai_study_practice": MessageLookupByLibrary.simpleMessage(
      "Prática de Estudo AI",
    ),
    "ai_study_tools": MessageLookupByLibrary.simpleMessage(
      "Ferramentas de Estudo AI",
    ),
    "ai_summarize": MessageLookupByLibrary.simpleMessage("Resumir com AI"),
    "ai_transcription": MessageLookupByLibrary.simpleMessage("Transcrição AI"),
    "ai_workflow": MessageLookupByLibrary.simpleMessage("Fluxo de Trabalho AI"),
    "all": MessageLookupByLibrary.simpleMessage("Tudo"),
    "all_note": MessageLookupByLibrary.simpleMessage("Todas as Notas"),
    "all_note_in_folder": MessageLookupByLibrary.simpleMessage(
      "Tem certeza que deseja remover esta pasta?",
    ),
    "all_tabs": MessageLookupByLibrary.simpleMessage("Todas as abas"),
    "allow": MessageLookupByLibrary.simpleMessage("Permitir"),
    "almost_done": MessageLookupByLibrary.simpleMessage("Quase pronto"),
    "and": MessageLookupByLibrary.simpleMessage("e"),
    "answer": MessageLookupByLibrary.simpleMessage("Resposta"),
    "anyone_with_link": MessageLookupByLibrary.simpleMessage(
      "Qualquer pessoa com o link pode ver",
    ),
    "app_feedback": MessageLookupByLibrary.simpleMessage(
      "Feedback do app NoteX",
    ),
    "app_store": MessageLookupByLibrary.simpleMessage("revisão na App Store"),
    "appearance": MessageLookupByLibrary.simpleMessage("Aparência"),
    "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
      "Essas informações ajudarão nossa equipe a identificar e resolver seu problema rapidamente. Agradecemos sua colaboração para melhorar o NoteX.",
    ),
    "appreciate_cooperation2": MessageLookupByLibrary.simpleMessage(
      "Isso nos ajuda a investigar e resolver seu problema de forma mais eficaz.",
    ),
    "appreciate_cooperation3": MessageLookupByLibrary.simpleMessage(
      "Obrigado por usar e confiar no NoteX AI!",
    ),
    "are_you_sure": MessageLookupByLibrary.simpleMessage("Oferta única"),
    "ask_anything": MessageLookupByLibrary.simpleMessage(
      "Pergunte qualquer coisa ...",
    ),
    "assist_faster": MessageLookupByLibrary.simpleMessage(
      "Para nos ajudar a atendê-lo mais rápido:",
    ),
    "assistant": MessageLookupByLibrary.simpleMessage("assistente"),
    "at_your_pace": MessageLookupByLibrary.simpleMessage("nota máxima"),
    "audio": MessageLookupByLibrary.simpleMessage("Áudio"),
    "audio_file": MessageLookupByLibrary.simpleMessage("Arquivo de Áudio"),
    "audio_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*Áudio é temporário - Salve antes de fechar",
    ),
    "audio_length_err": MessageLookupByLibrary.simpleMessage(
      "Arquivo de áudio excede o limite. Use um mais curto.",
    ),
    "audio_length_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de Duração do Áudio",
    ),
    "audio_process_err": MessageLookupByLibrary.simpleMessage(
      "Erro ao processar áudio. Tente com outro arquivo.",
    ),
    "audio_recording_ai_notes_daily": MessageLookupByLibrary.simpleMessage(
      "3 Notas AI Diárias de Áudio e Gravação*",
    ),
    "audio_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "Áudio para Notas AI",
    ),
    "audio_upload_note": MessageLookupByLibrary.simpleMessage(
      "Upload de Áudio",
    ),
    "auto": MessageLookupByLibrary.simpleMessage("Automático"),
    "auto_detect": MessageLookupByLibrary.simpleMessage(
      "Detectar automaticamente",
    ),
    "auto_generate_slides": MessageLookupByLibrary.simpleMessage(
      "Gere slides envolventes instantaneamente",
    ),
    "auto_renew_after_trial": MessageLookupByLibrary.simpleMessage(
      "Renova após teste • Cancele quando quiser",
    ),
    "auto_renewable_after_trial": MessageLookupByLibrary.simpleMessage(
      "Renova após teste. Cancele quando quiser",
    ),
    "auto_renewal": MessageLookupByLibrary.simpleMessage(
      "Renovação automática, cancele quando quiser",
    ),
    "available_credits": MessageLookupByLibrary.simpleMessage(
      "Créditos Disponíveis",
    ),
    "available_transcript": MessageLookupByLibrary.simpleMessage(
      "Transcrição disponível após criar a nota!",
    ),
    "back_content": MessageLookupByLibrary.simpleMessage(" pontos"),
    "background_style": MessageLookupByLibrary.simpleMessage("Estilo de Fundo"),
    "balanced": MessageLookupByLibrary.simpleMessage("Equilibrado"),
    "balanced_description": MessageLookupByLibrary.simpleMessage(
      "Ideias principais com contexto",
    ),
    "basic": MessageLookupByLibrary.simpleMessage("Plano Básico"),
    "basic_features": MessageLookupByLibrary.simpleMessage(
      "Recursos AI básicos",
    ),
    "beta": MessageLookupByLibrary.simpleMessage("Beta"),
    "between_concepts": MessageLookupByLibrary.simpleMessage(
      "Conecte os pontos entre conceitos",
    ),
    "black_friday_sale": MessageLookupByLibrary.simpleMessage(
      "Oferta de Natal!",
    ),
    "blurred_output_image": MessageLookupByLibrary.simpleMessage(
      "Falha na geração de estilo! Escolha outro estilo ou imagem!",
    ),
    "body_error_document_upload": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao processar seu documento. Volte ao app e tente novamente.",
    ),
    "body_error_note_document": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao processar seu documento. Volte ao app e tente novamente.",
    ),
    "body_error_note_recording": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao processar sua gravação. Volte ao app e tente novamente.",
    ),
    "body_error_note_upload": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao processar seu áudio. Volte ao app e tente novamente.",
    ),
    "body_error_note_web": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao processar seu link web. Volte ao app e tente novamente.",
    ),
    "body_error_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao processar seu link do YouTube. Volte ao app e tente novamente.",
    ),
    "body_success_note": MessageLookupByLibrary.simpleMessage(
      "Sua nota AI está pronta para revisão.",
    ),
    "bonus_credits_for_new_referred_friends_only":
        MessageLookupByLibrary.simpleMessage(
          "Créditos bônus apenas para novos amigos indicados",
        ),
    "boost_comprehension": MessageLookupByLibrary.simpleMessage(
      "Melhore compreensão e retenção",
    ),
    "boost_comprehension2": MessageLookupByLibrary.simpleMessage(
      "Melhore a compreensão",
    ),
    "boost_flashcards_quizzes": MessageLookupByLibrary.simpleMessage(
      "Aumente sua compreensão com flashcards e quizzes gerados por AI",
    ),
    "boost_knowledge": MessageLookupByLibrary.simpleMessage(
      "Conecte os pontos",
    ),
    "boost_knowledge_retention": MessageLookupByLibrary.simpleMessage(
      "Conecte os pontos entre",
    ),
    "both_you_friends_receive_usage_credits":
        MessageLookupByLibrary.simpleMessage(
          "Você e seus amigos receberão créditos de uso.",
        ),
    "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
      "Interrupção breve do serviço. Tente novamente em breve. Veja atualizações no Discord!",
    ),
    "business_uses": MessageLookupByLibrary.simpleMessage("Usos Corporativos"),
    "button_below": MessageLookupByLibrary.simpleMessage(
      "Toque no botão abaixo ou escolha um tipo de conteúdo específico para começar",
    ),
    "buy_one_forever": MessageLookupByLibrary.simpleMessage(
      "Compre Uma Vez. Desbloqueie Produtividade Máxima Para Sempre.",
    ),
    "by_subscribing": MessageLookupByLibrary.simpleMessage(
      "Ao assinar, você concorda com",
    ),
    "by_taping_continue": MessageLookupByLibrary.simpleMessage(
      "Ao continuar, você aceita nossos",
    ),
    "by_tapping_started": MessageLookupByLibrary.simpleMessage(
      "Ao tocar em \'Começar\', você aceita nossos",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Câmera"),
    "camera_access": MessageLookupByLibrary.simpleMessage(
      "\"NoteX\" Gostaria de Acessar a Câmera",
    ),
    "camera_permission": MessageLookupByLibrary.simpleMessage(
      "Acesso à câmera necessário",
    ),
    "camera_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Acesso à câmera necessário para selecionar imagens. Por favor, conceda o permissão nas configurações.",
    ),
    "can_improve": MessageLookupByLibrary.simpleMessage(
      "O que podemos melhorar?",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "cannot_create_pdf_file_from_image": MessageLookupByLibrary.simpleMessage(
      "Não é possível criar arquivo PDF a partir da imagem",
    ),
    "cannot_extract_text_from_pdf": MessageLookupByLibrary.simpleMessage(
      "Não foi possível ler o documento. Não conseguimos extrair texto deste documento. Isso geralmente acontece com documentos escaneados ou PDFs só de imagem.",
    ),
    "card": MessageLookupByLibrary.simpleMessage("Cartão"),
    "card_count": MessageLookupByLibrary.simpleMessage("Contagem de cartões"),
    "card_difficulty": MessageLookupByLibrary.simpleMessage(
      "Dificuldade do cartão",
    ),
    "change": MessageLookupByLibrary.simpleMessage("Alterar"),
    "change_plan": MessageLookupByLibrary.simpleMessage("Alterar plano"),
    "chaos_into_clarity": MessageLookupByLibrary.simpleMessage(
      "caos em clareza",
    ),
    "characters": MessageLookupByLibrary.simpleMessage("caracteres"),
    "chat_empty": MessageLookupByLibrary.simpleMessage("Chat vazio"),
    "chat_topic_temporary_stored": MessageLookupByLibrary.simpleMessage(
      "Sessão temporária, use \"Salvar Chat\" para manter",
    ),
    "check_if_you": MessageLookupByLibrary.simpleMessage(
      "Verifique se está na conta Google correta",
    ),
    "check_update": MessageLookupByLibrary.simpleMessage(
      "Nova versão disponível",
    ),
    "child_detected": MessageLookupByLibrary.simpleMessage(
      "Criança detectada. Use outra imagem.",
    ),
    "choose_your_note": MessageLookupByLibrary.simpleMessage(
      "Escolha seu NoteX",
    ),
    "choose_your_note_experience": MessageLookupByLibrary.simpleMessage(
      "Escolha sua experiência NoteX",
    ),
    "click_create_podcast": MessageLookupByLibrary.simpleMessage(
      "Clique em \'Criar podcast\' para transformar sua nota em áudio envolvente",
    ),
    "click_create_short": MessageLookupByLibrary.simpleMessage(
      "Clique em \'Criar Curto\' para transformar sua nota em curtos envolventes",
    ),
    "click_create_slide": MessageLookupByLibrary.simpleMessage(
      "Crie um diálogo para visualizar sua nota como uma apresentação",
    ),
    "click_start_flashcard": MessageLookupByLibrary.simpleMessage(
      "Clique em \'Criar flashcards\' para gerar conjuntos de flashcards com base na transcrição. Você pode criar vários conjuntos.",
    ),
    "click_start_mindmap": MessageLookupByLibrary.simpleMessage(
      "Clique em \'Criar mapa mental\' para gerar um mapa mental com base na transcrição",
    ),
    "click_start_quiz": MessageLookupByLibrary.simpleMessage(
      "Clique em \'Criar questionários\' para gerar conjuntos de perguntas com base na transcrição. Você pode criar vários conjuntos.",
    ),
    "click_to_flip": MessageLookupByLibrary.simpleMessage("Clique para virar"),
    "coming_soon": MessageLookupByLibrary.simpleMessage("Em breve"),
    "community": MessageLookupByLibrary.simpleMessage("Comunidade"),
    "community_feedback": MessageLookupByLibrary.simpleMessage(
      "Comunidade e Feedback",
    ),
    "comprehensive": MessageLookupByLibrary.simpleMessage("Abrangente"),
    "comprehensive_description": MessageLookupByLibrary.simpleMessage(
      "Cobertura detalhada com pontos de apoio",
    ),
    "congratulations": MessageLookupByLibrary.simpleMessage("Parabéns!"),
    "connect_friends": MessageLookupByLibrary.simpleMessage(
      "Importe facilmente links de notas compartilhadas dos amigos",
    ),
    "connection_fail": MessageLookupByLibrary.simpleMessage(
      "Falha na Conexão!",
    ),
    "connection_timeout": MessageLookupByLibrary.simpleMessage(
      "Tempo limite de conexão. Verifique sua conexão com a internet e tente novamente.",
    ),
    "contact_support": MessageLookupByLibrary.simpleMessage("Contatar Suporte"),
    "content_account_trial": m0,
    "content_button_flashcard": MessageLookupByLibrary.simpleMessage(
      "Criar flashcards",
    ),
    "content_button_mindmap": MessageLookupByLibrary.simpleMessage(
      "Criar mapa mental",
    ),
    "content_button_quiz": MessageLookupByLibrary.simpleMessage(
      "Criar questionários",
    ),
    "content_button_summary": MessageLookupByLibrary.simpleMessage(
      "Gerar Resumo",
    ),
    "content_camera_access": MessageLookupByLibrary.simpleMessage(
      "O NoteX precisa de acesso à sua câmera para capturar, reconhecer e digitalizar textos de imagens",
    ),
    "content_delete_note": MessageLookupByLibrary.simpleMessage(
      "Você não poderá recuperá-la depois",
    ),
    "content_delete_note_detail": MessageLookupByLibrary.simpleMessage(
      "Tem certeza de que deseja remover esta nota?",
    ),
    "content_delete_reminder": MessageLookupByLibrary.simpleMessage(
      "Tem certeza de que deseja remover este lembrete?",
    ),
    "content_discard_changes": MessageLookupByLibrary.simpleMessage(
      "Sair encerrará a gravação e descartará alterações.",
    ),
    "content_discard_changes_image": MessageLookupByLibrary.simpleMessage(
      "Fechar descartará as fotos que você capturou",
    ),
    "content_discard_changes_note": MessageLookupByLibrary.simpleMessage(
      "Esta ação descartará todas as alterações, e elas não poderão ser desfeitas.",
    ),
    "content_discard_changes_reminder": MessageLookupByLibrary.simpleMessage(
      "Sair fechará a notificação de lembrete e descartará todas as alterações.",
    ),
    "content_empty_flashcard": MessageLookupByLibrary.simpleMessage(
      "Resumo automático aparecerá aqui após a reunião.",
    ),
    "content_empty_quiz": MessageLookupByLibrary.simpleMessage(
      "Resumo automático aparecerá aqui após a reunião.",
    ),
    "content_hour": MessageLookupByLibrary.simpleMessage(
      "Horas de conteúdo para",
    ),
    "content_hour_insight": MessageLookupByLibrary.simpleMessage(
      "Horas de conteúdo em insights",
    ),
    "content_minute_left": MessageLookupByLibrary.simpleMessage(
      "Gravações acima do limite grátis serão salvas localmente sem transcrição ou resumo AI. Remova limites indo para Pro.",
    ),
    "content_payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Obrigado pela compra. Sua transação foi processada com sucesso.",
    ),
    "content_quarter_01": MessageLookupByLibrary.simpleMessage(
      "Notas AI Ilimitadas de Gravações, Uploads, Links do YouTube.",
    ),
    "content_quarter_02": MessageLookupByLibrary.simpleMessage(
      "Chat AI, Mapa Mental, Flashcards, Quiz, Compartilhamento de Notas Ilimitados.",
    ),
    "content_save_changes": MessageLookupByLibrary.simpleMessage(
      "Esta ação salvará todas as alterações permanentemente.",
    ),
    "continue_3_day": MessageLookupByLibrary.simpleMessage(
      "Continuar Teste Grátis de 3 Dias",
    ),
    "continue_button": MessageLookupByLibrary.simpleMessage("Continuar"),
    "continue_with_apple": MessageLookupByLibrary.simpleMessage(
      "Continuar com Apple",
    ),
    "continue_with_email": MessageLookupByLibrary.simpleMessage(
      "Continuar com E-mail",
    ),
    "continue_with_google": MessageLookupByLibrary.simpleMessage(
      "Continuar com Google",
    ),
    "copied_to_clipboard": MessageLookupByLibrary.simpleMessage(
      "Copiado para a área de transferência",
    ),
    "copy": MessageLookupByLibrary.simpleMessage("Copiar"),
    "copy_your_referral_code": MessageLookupByLibrary.simpleMessage(
      "Copie seu código de indicação.",
    ),
    "correct": MessageLookupByLibrary.simpleMessage("Correto"),
    "craft_visual_from_every_note": MessageLookupByLibrary.simpleMessage(
      "Transforme suas notas em slides",
    ),
    "craft_visual_stories": MessageLookupByLibrary.simpleMessage(
      "Transforme suas notas",
    ),
    "create": MessageLookupByLibrary.simpleMessage("Criar"),
    "create_folder": MessageLookupByLibrary.simpleMessage("Criar Pasta"),
    "create_lecture": MessageLookupByLibrary.simpleMessage(
      "Crie resumos concisos das suas aulas",
    ),
    "create_new_folder": MessageLookupByLibrary.simpleMessage(
      "Criar Nova Pasta",
    ),
    "create_note_successfully": MessageLookupByLibrary.simpleMessage(
      "Nota criada com sucesso!",
    ),
    "create_notes": MessageLookupByLibrary.simpleMessage("Criando notas AI..."),
    "create_podcast": MessageLookupByLibrary.simpleMessage("Criar podcast"),
    "create_reminder": MessageLookupByLibrary.simpleMessage("Criar Lembrete"),
    "create_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Selecionar idioma",
    ),
    "create_short": MessageLookupByLibrary.simpleMessage("Criar Curto"),
    "create_shorts": MessageLookupByLibrary.simpleMessage("Criar Curtos"),
    "create_slide": MessageLookupByLibrary.simpleMessage("Criar diapositivas"),
    "creating_note": MessageLookupByLibrary.simpleMessage("Criando nota ..."),
    "creating_quiz": MessageLookupByLibrary.simpleMessage(
      "Criando perguntas do quiz",
    ),
    "credit": MessageLookupByLibrary.simpleMessage("Crédito"),
    "credits": MessageLookupByLibrary.simpleMessage("Créditos"),
    "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
        MessageLookupByLibrary.simpleMessage(
          "Créditos podem ser usados para criar notas e acessar seus recursos. Se sua assinatura expirar, use créditos para continuar ações.",
        ),
    "credits_earned": MessageLookupByLibrary.simpleMessage("Créditos Ganhos"),
    "credits_premium_features": MessageLookupByLibrary.simpleMessage(
      "créditos e recursos premium.",
    ),
    "credits_used": MessageLookupByLibrary.simpleMessage("Créditos Usados"),
    "current_plan": MessageLookupByLibrary.simpleMessage("Plano Atual"),
    "custom_note_tabs": MessageLookupByLibrary.simpleMessage(
      "Personalizar abas da nota",
    ),
    "customize_note_tabs": MessageLookupByLibrary.simpleMessage(
      "Personalizar abas da nota",
    ),
    "customize_your_note_view": MessageLookupByLibrary.simpleMessage(
      "Personalize a visualização da sua nota",
    ),
    "daily_10": MessageLookupByLibrary.simpleMessage("10 diárias"),
    "daily_3": MessageLookupByLibrary.simpleMessage("3 diárias"),
    "daily_5": MessageLookupByLibrary.simpleMessage("5 diárias"),
    "daily_rewards_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite diário de recompensas atingido. Tente amanhã!",
    ),
    "daily_shorts_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite Diário de Curtos Atingido (Beta - Acesso Antecipado)",
    ),
    "daily_shorts_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "Você usou todas as gerações de Curtos hoje. Esta função beta tem limites diários para garantir estabilidade. \nVolte amanhã para criar mais vídeos curtos AI!",
    ),
    "daily_slideshow_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite de diapositivas diárias atingido",
    ),
    "daily_slideshow_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
      "Você consumiu todas as gerações de apresentações para hoje. Esta funcionalidade beta tem limites diários para garantir um desempenho estável. Volte amanhã para criar mais apresentações alimentadas por IA!",
    ),
    "dark": MessageLookupByLibrary.simpleMessage("Escuro"),
    "data": MessageLookupByLibrary.simpleMessage("dados"),
    "day_free_trial_access_all_features": MessageLookupByLibrary.simpleMessage(
      "7-dia de teste gratuito para acessar todas as funcionalidades, então apenas ",
    ),
    "days": MessageLookupByLibrary.simpleMessage("Dias"),
    "db_err": MessageLookupByLibrary.simpleMessage(
      "Erro no banco de dados. Tente depois.",
    ),
    "deals_left_at_this_price": MessageLookupByLibrary.simpleMessage(
      "ofertas vitalícias restantes neste preço",
    ),
    "decline_free_trial": MessageLookupByLibrary.simpleMessage(
      "Recusar teste grátis",
    ),
    "default_error": MessageLookupByLibrary.simpleMessage(
      "Algo deu errado! Tente novamente!",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Excluir"),
    "delete_account": MessageLookupByLibrary.simpleMessage("Excluir Conta"),
    "delete_account_detail": MessageLookupByLibrary.simpleMessage(
      "Esta ação é irreversível. Excluir sua conta removerá permanentemente: Todas as suas notas e gravações",
    ),
    "delete_all_note": MessageLookupByLibrary.simpleMessage(
      "Excluir todas as notas na pasta",
    ),
    "delete_folder": MessageLookupByLibrary.simpleMessage("Excluir Pasta"),
    "delete_note": MessageLookupByLibrary.simpleMessage("Excluir esta nota?"),
    "delete_note_item": MessageLookupByLibrary.simpleMessage("Excluir nota"),
    "delete_recording": MessageLookupByLibrary.simpleMessage(
      "Excluir gravação",
    ),
    "delete_recording_confirmation": MessageLookupByLibrary.simpleMessage(
      "Tem certeza de que deseja excluir esta gravação",
    ),
    "delete_recording_setting_confirmation": MessageLookupByLibrary.simpleMessage(
      "O arquivo de áudio será excluído permanentemente do seu dispositivo. Esta ação não pode ser revertida.",
    ),
    "delete_reminder": MessageLookupByLibrary.simpleMessage(
      "Excluir Lembrete?",
    ),
    "delete_success": MessageLookupByLibrary.simpleMessage(
      "A nota foi excluída com sucesso.",
    ),
    "delete_this_folder": MessageLookupByLibrary.simpleMessage(
      "Excluir esta pasta?",
    ),
    "delete_this_item": MessageLookupByLibrary.simpleMessage(
      "Excluir este item?",
    ),
    "deselect": MessageLookupByLibrary.simpleMessage("Desmarcar"),
    "detail_unlimited_ai_summaries": MessageLookupByLibrary.simpleMessage(
      "Notas AI Ilimitadas de Gravações, Arquivos de Áudio, Documentos e Vídeos do YouTube",
    ),
    "developing_quizzes": MessageLookupByLibrary.simpleMessage(
      "Desenvolvendo quizzes...",
    ),
    "discard": MessageLookupByLibrary.simpleMessage("Descartar"),
    "discard_changes": MessageLookupByLibrary.simpleMessage(
      "Descartar alterações?",
    ),
    "dissatisfied": MessageLookupByLibrary.simpleMessage(
      "Obrigado pelo feedback. Sua opinião nos ajuda a melhorar e tornar sua próxima experiência melhor. Muito obrigado!",
    ),
    "doc": MessageLookupByLibrary.simpleMessage("Documento"),
    "document": MessageLookupByLibrary.simpleMessage(
      "Carregar Documento (Em Breve)",
    ),
    "document_available": MessageLookupByLibrary.simpleMessage(
      "O documento estará disponível após a criação da nota com sucesso!",
    ),
    "document_exceed_limit": MessageLookupByLibrary.simpleMessage(
      "O arquivo excede 20MB. Escolha um menor.",
    ),
    "document_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de Upload de Documento",
    ),
    "document_limit_message": MessageLookupByLibrary.simpleMessage(
      "Usuários grátis podem resumir 1 documento por dia.",
    ),
    "document_note": MessageLookupByLibrary.simpleMessage("Nota de Documento"),
    "document_tab": MessageLookupByLibrary.simpleMessage("Documento"),
    "document_to_ai_note": MessageLookupByLibrary.simpleMessage(
      "Documento para Notas AI",
    ),
    "document_type": MessageLookupByLibrary.simpleMessage(
      "Tipos suportados: .pdf, .doc, .docx, .txt, .md",
    ),
    "document_upload_note": MessageLookupByLibrary.simpleMessage(
      "Upload de documento",
    ),
    "document_webview_loading_message": MessageLookupByLibrary.simpleMessage(
      "Carregando conteúdo do documento...",
    ),
    "done_button_label": MessageLookupByLibrary.simpleMessage("Concluído"),
    "donotallow": MessageLookupByLibrary.simpleMessage("Não Permitir"),
    "double_the_benefits": MessageLookupByLibrary.simpleMessage(
      "Duplique os Benefícios!",
    ),
    "download_audio_file": MessageLookupByLibrary.simpleMessage(
      "Baixar arquivo de áudio",
    ),
    "download_sucess": MessageLookupByLibrary.simpleMessage(
      "Download concluído",
    ),
    "duration": MessageLookupByLibrary.simpleMessage("Duração"),
    "each_ai_note_generation_uses_1_credit":
        MessageLookupByLibrary.simpleMessage(
          "Cada geração de nota AI usa 1 crédito",
        ),
    "each_referral_earns": MessageLookupByLibrary.simpleMessage(
      "Cada indicação ganha",
    ),
    "early_access": MessageLookupByLibrary.simpleMessage(
      "Acesso Antecipado a \nRecursos Futuros",
    ),
    "early_supporters_exclusive_offer": MessageLookupByLibrary.simpleMessage(
      "Oferta exclusiva para apoiadores iniciais",
    ),
    "easily_import_shared_note_link": MessageLookupByLibrary.simpleMessage(
      "Importe facilmente links de notas compartilhadas de amigos",
    ),
    "easy": MessageLookupByLibrary.simpleMessage("Fácil"),
    "edit": MessageLookupByLibrary.simpleMessage("Editar"),
    "edit_folder": MessageLookupByLibrary.simpleMessage("Editar Pasta"),
    "edit_folder_name": MessageLookupByLibrary.simpleMessage(
      "Digite o nome da pasta",
    ),
    "edit_name": MessageLookupByLibrary.simpleMessage("Editar Nome"),
    "edit_note": MessageLookupByLibrary.simpleMessage("Editar Nota"),
    "edit_notes": MessageLookupByLibrary.simpleMessage("Editar Nota"),
    "edit_reminder": MessageLookupByLibrary.simpleMessage("Editar Lembrete"),
    "edit_transcript": MessageLookupByLibrary.simpleMessage(
      "Editar Transcrição",
    ),
    "edit_transcript_json_fail": MessageLookupByLibrary.simpleMessage(
      "Falha ao editar transcrição com carimbo de tempo. Tente novamente.",
    ),
    "edit_transcript_json_success": MessageLookupByLibrary.simpleMessage(
      "Transcrição com carimbo de tempo editada com sucesso",
    ),
    "email_invalid": MessageLookupByLibrary.simpleMessage(
      "O endereço de e-mail não é válido.",
    ),
    "email_sent": MessageLookupByLibrary.simpleMessage(
      "Verifique sua caixa de entrada",
    ),
    "email_sent_success": MessageLookupByLibrary.simpleMessage(
      "Enviamos um link mágico para login. Clique no link no seu e-mail para continuar.",
    ),
    "enable_free": MessageLookupByLibrary.simpleMessage("Ativar Teste Grátis"),
    "enables_swap": MessageLookupByLibrary.simpleMessage(
      "Permite reordenar imagens por seleção e troca",
    ),
    "english": MessageLookupByLibrary.simpleMessage("Inglês"),
    "enter_card_count": MessageLookupByLibrary.simpleMessage(
      "Digite a quantidade de cartões",
    ),
    "enter_email": MessageLookupByLibrary.simpleMessage(
      "Digite seu endereço de e-mail...",
    ),
    "enter_feedback": MessageLookupByLibrary.simpleMessage(
      "Digite seu feedback",
    ),
    "enter_folder_name": MessageLookupByLibrary.simpleMessage(
      "Digite o nome da pasta",
    ),
    "enter_new_name": MessageLookupByLibrary.simpleMessage(
      "Insira um novo nome",
    ),
    "enter_quiz_count": MessageLookupByLibrary.simpleMessage(
      "Digite a quantidade de quizzes",
    ),
    "enter_referral_code": MessageLookupByLibrary.simpleMessage(
      "Digite o Código de Indicação",
    ),
    "enter_slide_count": MessageLookupByLibrary.simpleMessage(
      "Digite o número de diapositivas",
    ),
    "enter_title": MessageLookupByLibrary.simpleMessage("Digite o título"),
    "enter_valid_email": MessageLookupByLibrary.simpleMessage(
      "Digite um e-mail válido",
    ),
    "error": MessageLookupByLibrary.simpleMessage("Erro"),
    "error_connection": MessageLookupByLibrary.simpleMessage(
      "Erro na conexão.\nTente novamente",
    ),
    "error_convert_image": MessageLookupByLibrary.simpleMessage(
      "Erro ao converter ui.Image para image.Image",
    ),
    "error_logging_in": MessageLookupByLibrary.simpleMessage(
      "Conectar à internet",
    ),
    "esc": MessageLookupByLibrary.simpleMessage("Esc"),
    "essential": MessageLookupByLibrary.simpleMessage("Essencial"),
    "essential_lifetime": MessageLookupByLibrary.simpleMessage(
      "Essencial Vitalício",
    ),
    "essential_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Acesso Vitalício Essencial",
    ),
    "evening_content": MessageLookupByLibrary.simpleMessage(
      "Reflita, capture, cresça",
    ),
    "evening_content_2": MessageLookupByLibrary.simpleMessage(
      "Insights de hoje preservados",
    ),
    "evening_content_3": MessageLookupByLibrary.simpleMessage(
      "O amanhã começa com as notas de hoje",
    ),
    "evening_content_4": MessageLookupByLibrary.simpleMessage(
      "Pensamentos organizados, mente tranquila",
    ),
    "evening_content_5": MessageLookupByLibrary.simpleMessage(
      "Salve agora, agradeça depois",
    ),
    "evening_content_6": MessageLookupByLibrary.simpleMessage(
      "Progresso preservado",
    ),
    "every_note_you_take": MessageLookupByLibrary.simpleMessage("em slides"),
    "experience": MessageLookupByLibrary.simpleMessage("experiência"),
    "export": MessageLookupByLibrary.simpleMessage("Exportar"),
    "export_as": MessageLookupByLibrary.simpleMessage("Exportar como"),
    "export_audio": MessageLookupByLibrary.simpleMessage(
      "Exportar Arquivo de Áudio",
    ),
    "export_failed": MessageLookupByLibrary.simpleMessage(
      "Exportação falhou. Tente novamente mais tarde.",
    ),
    "export_flashcard": MessageLookupByLibrary.simpleMessage(
      "Exportar Flashcard",
    ),
    "export_mind_map": MessageLookupByLibrary.simpleMessage(
      "Exportar Mapa Mental como",
    ),
    "export_pdf": MessageLookupByLibrary.simpleMessage("Exportar Resumo"),
    "export_quiz": MessageLookupByLibrary.simpleMessage("Exportar Quiz"),
    "export_to_pdf_share_notes": MessageLookupByLibrary.simpleMessage(
      "Exporte para PDF e Compartilhe Notas",
    ),
    "export_transcript": MessageLookupByLibrary.simpleMessage(
      "Exportar Transcrição",
    ),
    "extracting_text_from_document": MessageLookupByLibrary.simpleMessage(
      "Extraindo texto do documento",
    ),
    "fail": MessageLookupByLibrary.simpleMessage("Falha"),
    "fail_create_pdf": MessageLookupByLibrary.simpleMessage(
      "Falha ao criar arquivo PDF",
    ),
    "fail_to_load_document": MessageLookupByLibrary.simpleMessage(
      "Falha ao carregar documento!",
    ),
    "fail_to_load_video": MessageLookupByLibrary.simpleMessage(
      "Falha ao carregar vídeo",
    ),
    "failed_get_anonymous_user": MessageLookupByLibrary.simpleMessage(
      "Falha ao obter usuário anônimo JWT",
    ),
    "failed_to_delete_recording": MessageLookupByLibrary.simpleMessage(
      "Erro ao excluir a gravação",
    ),
    "failed_to_load_slideshow": MessageLookupByLibrary.simpleMessage(
      "Não foi possível carregar o conjunto de slides do sistema. Tente novamente para uma melhor experiência.",
    ),
    "failed_to_save_file": MessageLookupByLibrary.simpleMessage(
      "Falha ao salvar o arquivo",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Feedback"),
    "file_import": MessageLookupByLibrary.simpleMessage(
      "Importação de Arquivo",
    ),
    "file_save_success": MessageLookupByLibrary.simpleMessage(
      "Arquivo salvo com sucesso",
    ),
    "file_size_err": MessageLookupByLibrary.simpleMessage(
      "Tamanho do arquivo excede o limite. Use um menor.",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filtrar"),
    "filter_and_sort": MessageLookupByLibrary.simpleMessage(
      "Filtrar e Ordenar",
    ),
    "finalizing": MessageLookupByLibrary.simpleMessage("Finalizando..."),
    "find_and_replace": MessageLookupByLibrary.simpleMessage(
      "Localizar e Substituir",
    ),
    "flash_card_gen_success": MessageLookupByLibrary.simpleMessage(
      "Flashcard gerado com sucesso",
    ),
    "flash_card_iap": MessageLookupByLibrary.simpleMessage(
      "Conjunto de Flashcards",
    ),
    "flashcard": MessageLookupByLibrary.simpleMessage("Flashcard"),
    "flashcard_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Conjunto de flashcards não encontrado",
    ),
    "flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Conjuntos de cartões",
    ),
    "flashcards": MessageLookupByLibrary.simpleMessage("Flashcards"),
    "flashcards_for": MessageLookupByLibrary.simpleMessage("Flashcards para"),
    "focus_on": MessageLookupByLibrary.simpleMessage("Foco no Que Importa"),
    "folder": MessageLookupByLibrary.simpleMessage("Pastas"),
    "follow_steps_to_get_rewarded": MessageLookupByLibrary.simpleMessage(
      "Siga estes passos para ser recompensado",
    ),
    "for_unlimited_experiences": MessageLookupByLibrary.simpleMessage(
      "para experiências ilimitadas.",
    ),
    "free": MessageLookupByLibrary.simpleMessage("Grátis"),
    "free_30_minutes": MessageLookupByLibrary.simpleMessage(
      "Grátis: 30 minutos por arquivo",
    ),
    "free_messages": MessageLookupByLibrary.simpleMessage("mensagens grátis"),
    "free_recording_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de Gravação Grátis",
    ),
    "free_recording_limit_details": MessageLookupByLibrary.simpleMessage(
      "Restam %s minutos de transcrições e resumos AI grátis esta semana.",
    ),
    "free_trial": MessageLookupByLibrary.simpleMessage("Teste grátis"),
    "free_updates": MessageLookupByLibrary.simpleMessage(
      "Atualizações e melhorias grátis para sempre",
    ),
    "free_usage": MessageLookupByLibrary.simpleMessage("Uso Grátis - Semanal"),
    "free_user_audio": MessageLookupByLibrary.simpleMessage(
      "Usuários grátis podem transcrever e resumir áudio de até 30 minutos",
    ),
    "free_user_can": MessageLookupByLibrary.simpleMessage(
      "Usuários grátis podem resumir 1 vídeo do YouTube (máx. 30 min) por dia.",
    ),
    "friendly": MessageLookupByLibrary.simpleMessage("Amigável"),
    "friendly_description": MessageLookupByLibrary.simpleMessage(
      "Conversacional com emojis",
    ),
    "front_content": MessageLookupByLibrary.simpleMessage("Você conseguiu "),
    "future_features": MessageLookupByLibrary.simpleMessage(
      "Recursos futuros podem ter limites de uso",
    ),
    "gen_ai": MessageLookupByLibrary.simpleMessage("Gerando IA..."),
    "gen_ai_voice": MessageLookupByLibrary.simpleMessage("Gerando voz de IA"),
    "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage(
      "Gerando fundo do quiz",
    ),
    "generate_audio": MessageLookupByLibrary.simpleMessage("Gerar Áudio"),
    "generate_content": MessageLookupByLibrary.simpleMessage(
      "Gere resumos inteligentes de conteúdo do YouTube",
    ),
    "generate_note_fail": MessageLookupByLibrary.simpleMessage(
      "Falha ao gerar notas AI!",
    ),
    "generate_shorts_step_1": MessageLookupByLibrary.simpleMessage(
      "Criando sua história...",
    ),
    "generate_shorts_step_2": MessageLookupByLibrary.simpleMessage(
      "Adicionando a voz perfeita...",
    ),
    "generate_shorts_step_3": MessageLookupByLibrary.simpleMessage(
      "Deixando incrível! Este vídeo será digno de compartilhar #NoteXAI",
    ),
    "generate_shorts_study_guides": MessageLookupByLibrary.simpleMessage(
      "Gere Curtos e Guias de Estudo",
    ),
    "generate_transcript_notes": MessageLookupByLibrary.simpleMessage(
      "Geraremos transcrição, notas e guia de estudo",
    ),
    "generate_video": MessageLookupByLibrary.simpleMessage("Gerar Vídeo"),
    "generating_ai_note": MessageLookupByLibrary.simpleMessage(
      "Gerando notas AI",
    ),
    "generating_summary": MessageLookupByLibrary.simpleMessage(
      "Gerando resumo AI...",
    ),
    "get_fail": MessageLookupByLibrary.simpleMessage(
      "Falha ao obter quiz/flashcards/mapa mental. Tente novamente!",
    ),
    "get_more_done": MessageLookupByLibrary.simpleMessage(
      "Faça mais, mantenha-se",
    ),
    "get_more_done_stay_on_track": MessageLookupByLibrary.simpleMessage(
      "Faça mais, mantenha-se no caminho",
    ),
    "get_now": MessageLookupByLibrary.simpleMessage("OBTER AGORA"),
    "get_offer_now": MessageLookupByLibrary.simpleMessage("Obter oferta agora"),
    "get_start": MessageLookupByLibrary.simpleMessage("Começar"),
    "go_back": MessageLookupByLibrary.simpleMessage("Voltar"),
    "go_email": MessageLookupByLibrary.simpleMessage("Ir para E-mail"),
    "go_pro": MessageLookupByLibrary.simpleMessage("Torne-se PRO"),
    "go_unlimited": MessageLookupByLibrary.simpleMessage("Fique Ilimitado!"),
    "good_afternoon": MessageLookupByLibrary.simpleMessage("Boa tarde!"),
    "good_evening": MessageLookupByLibrary.simpleMessage("Boa noite!"),
    "good_morning": MessageLookupByLibrary.simpleMessage("Bom dia!"),
    "got_it": MessageLookupByLibrary.simpleMessage("Entendido!"),
    "hard": MessageLookupByLibrary.simpleMessage("Difícil"),
    "hello_welcome": MessageLookupByLibrary.simpleMessage(
      "Bem-vindo de volta 👋",
    ),
    "help_legal": MessageLookupByLibrary.simpleMessage("Ajuda e Legal"),
    "help_us_grow": MessageLookupByLibrary.simpleMessage(
      "Ajude-nos a Crescer!",
    ),
    "hi": MessageLookupByLibrary.simpleMessage("Oi"),
    "hope_enjoy_app": MessageLookupByLibrary.simpleMessage(
      "Esperamos que goste do app. Obrigado pelo apoio!",
    ),
    "hours": MessageLookupByLibrary.simpleMessage("Horas"),
    "how_will_you_use_notex": MessageLookupByLibrary.simpleMessage(
      "Como você usará o NoteX?",
    ),
    "http_failed": MessageLookupByLibrary.simpleMessage(
      "Solicitação HTTP falhou. Tente novamente mais tarde.",
    ),
    "idea1": MessageLookupByLibrary.simpleMessage(
      "Faça login com sua conta Google ou Apple, se ainda não fez",
    ),
    "idea2": MessageLookupByLibrary.simpleMessage(
      "Forneça uma breve descrição do ocorrido",
    ),
    "idea3": MessageLookupByLibrary.simpleMessage(
      "Inclua detalhes relevantes (dispositivo, versão do SO)",
    ),
    "idea4": MessageLookupByLibrary.simpleMessage(
      "Mencione quando o problema começou",
    ),
    "idea5": MessageLookupByLibrary.simpleMessage(
      "Envie um e-mail diretamente para",
    ),
    "image": MessageLookupByLibrary.simpleMessage("Imagem"),
    "image_jpeg": MessageLookupByLibrary.simpleMessage("Imagem (.jpeg)"),
    "image_png": MessageLookupByLibrary.simpleMessage("Imagem (.png)"),
    "image_quality_too_low": MessageLookupByLibrary.simpleMessage(
      "Qualidade da imagem baixa. Use uma de maior qualidade!",
    ),
    "image_too_large": MessageLookupByLibrary.simpleMessage(
      "Imagem muito grande. Use uma menor que 10MB.",
    ),
    "images_have_been_uploaded": m1,
    "import_note_links": MessageLookupByLibrary.simpleMessage(
      "Importar links de notas",
    ),
    "import_notes": MessageLookupByLibrary.simpleMessage(
      "Importar Notas Compartilhadas",
    ),
    "improve_responses": MessageLookupByLibrary.simpleMessage(
      "Suas respostas nos ajudam a melhorar",
    ),
    "initializing_camera": MessageLookupByLibrary.simpleMessage(
      "Inicializando câmera...",
    ),
    "insight_instantly": MessageLookupByLibrary.simpleMessage(
      "Horas de conteúdo em insights instantâneos",
    ),
    "insights_instantly": MessageLookupByLibrary.simpleMessage(
      "insights instantâneos",
    ),
    "instant_answers_from_your": MessageLookupByLibrary.simpleMessage(
      "Respostas instantâneas dos seus",
    ),
    "instant_answers_from_your_meeting_data":
        MessageLookupByLibrary.simpleMessage(
          "Respostas instantâneas dos dados da reunião",
        ),
    "instant_answers_meeting": MessageLookupByLibrary.simpleMessage(
      "Respostas instantâneas da sua reunião",
    ),
    "instantly": MessageLookupByLibrary.simpleMessage("instantaneamente"),
    "interactive_ai_flashcards": MessageLookupByLibrary.simpleMessage(
      "Mapa Mental AI Interativo",
    ),
    "interactive_flash": MessageLookupByLibrary.simpleMessage(
      "Flashcards Interativos",
    ),
    "interactive_flashcards": MessageLookupByLibrary.simpleMessage(
      "Flashcards Interativos e Mapas Mentais Ilimitados",
    ),
    "interactive_flashcards_quiz": MessageLookupByLibrary.simpleMessage(
      "Flashcards e Quiz Interativos",
    ),
    "introduce_guidance": MessageLookupByLibrary.simpleMessage(
      "Obrigado por entrar em contato. Para nos ajudar a investigar e resolver seu problema, siga estes passos:",
    ),
    "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
      "Entendemos como pode ser frustrante enfrentar problemas, especialmente com notas ou gravações importantes. Nossa equipe de suporte está pronta para ajudar, geralmente em até 12 horas.",
    ),
    "inv_audio": MessageLookupByLibrary.simpleMessage(
      "Arquivo de áudio inválido. Use um formato suportado.",
    ),
    "inv_yt_url": MessageLookupByLibrary.simpleMessage(
      "URL do YouTube inválida. Forneça um link válido.",
    ),
    "invalid_code": MessageLookupByLibrary.simpleMessage(
      "Código inválido. Tente novamente.",
    ),
    "invalid_file_type": MessageLookupByLibrary.simpleMessage(
      "Formato de arquivo inválido. Carregue novamente.",
    ),
    "invalid_token": MessageLookupByLibrary.simpleMessage("Token inválido"),
    "invite_friends": MessageLookupByLibrary.simpleMessage(
      "Convide amigos para participar - ambos desbloqueiam extras",
    ),
    "join_discord": MessageLookupByLibrary.simpleMessage("Junte-se ao Discord"),
    "join_noteX_ai_lets_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "Junte-se ao NoteX AI e subamos de nível juntos!",
        ),
    "language": MessageLookupByLibrary.simpleMessage("Idioma"),
    "language_tip": MessageLookupByLibrary.simpleMessage(
      "Escolha o idioma principal do áudio para melhores resultados",
    ),
    "language_tip_1": MessageLookupByLibrary.simpleMessage(
      "Selecione o idioma principal para melhores resultados de transcrição",
    ),
    "language_tip_2": MessageLookupByLibrary.simpleMessage(
      "Para conversas mistas, escolha multilíngue",
    ),
    "language_tip_3": MessageLookupByLibrary.simpleMessage(
      "Chamadas pausam a gravação. Volte ao app para retomar",
    ),
    "latest_ai_models": MessageLookupByLibrary.simpleMessage(
      "Modelos AI mais recentes",
    ),
    "learn_faster_through": MessageLookupByLibrary.simpleMessage(
      "Aprenda mais rápido com",
    ),
    "learn_faster_through_active_recall": MessageLookupByLibrary.simpleMessage(
      "Aprenda mais rápido com recall ativo",
    ),
    "learn_smart": MessageLookupByLibrary.simpleMessage(
      "Aprenda de Forma Inteligente",
    ),
    "learn_unlimited": MessageLookupByLibrary.simpleMessage(
      "Aprenda de Forma Inteligente e Ilimitada!",
    ),
    "lecture_notes_study_materials": MessageLookupByLibrary.simpleMessage(
      "Notas de Aula e Materiais de Estudo",
    ),
    "let_ai_handle": MessageLookupByLibrary.simpleMessage(
      "Deixe o AI lidar com os detalhes",
    ),
    "let_note_ai": MessageLookupByLibrary.simpleMessage(
      "Deixe o NoteX AI transformar informações",
    ),
    "let_start": MessageLookupByLibrary.simpleMessage("Vamos começar"),
    "lets_create_your_first_ai_note": MessageLookupByLibrary.simpleMessage(
      "Vamos criar sua primeira nota de IA!",
    ),
    "lifetime": MessageLookupByLibrary.simpleMessage("Plano Vitalício"),
    "lifetime_pro_access_level_up_together":
        MessageLookupByLibrary.simpleMessage(
          "Acesso Pro Vitalício! Subam de nível juntos ✨",
        ),
    "lifetime_setting": MessageLookupByLibrary.simpleMessage("vitalício"),
    "lifetime_spots_remaining": MessageLookupByLibrary.simpleMessage(
      "Vagas vitalícias restantes",
    ),
    "light": MessageLookupByLibrary.simpleMessage("Claro"),
    "limited_notes": MessageLookupByLibrary.simpleMessage(
      "Notas limitadas por dia",
    ),
    "limited_offer": MessageLookupByLibrary.simpleMessage("Oferta Limitada"),
    "limited_time": MessageLookupByLibrary.simpleMessage("Tempo Limitado"),
    "link": MessageLookupByLibrary.simpleMessage("Link"),
    "link_error": MessageLookupByLibrary.simpleMessage("Erro no link"),
    "link_expired": MessageLookupByLibrary.simpleMessage(
      "O link do e-mail expirou.",
    ),
    "link_invalid": MessageLookupByLibrary.simpleMessage(
      "O link usado é inválido, pode ter expirado ou já foi usado. Solicite um novo link e tente novamente.",
    ),
    "loading": MessageLookupByLibrary.simpleMessage("Carregando"),
    "loading_content": MessageLookupByLibrary.simpleMessage(
      "Carregando conteúdo...",
    ),
    "local_recording": MessageLookupByLibrary.simpleMessage(
      "Gravação Inteligente",
    ),
    "login_failed": MessageLookupByLibrary.simpleMessage("Falha no login."),
    "login_info_1": MessageLookupByLibrary.simpleMessage(
      "Acesse suas notas em qualquer dispositivo",
    ),
    "login_info_2": MessageLookupByLibrary.simpleMessage(
      "Segurança de nível empresarial com AWS",
    ),
    "login_info_3": MessageLookupByLibrary.simpleMessage(
      "Seus dados permanecem privados",
    ),
    "login_info_4": MessageLookupByLibrary.simpleMessage(
      "Acesse na web em notexapp.com",
    ),
    "login_success": MessageLookupByLibrary.simpleMessage("Login Concluído!"),
    "login_title": MessageLookupByLibrary.simpleMessage(
      "Maximize a Produtividade, em Todo Lugar!",
    ),
    "login_title_2": MessageLookupByLibrary.simpleMessage(
      "Apresentando NoteX 2.0",
    ),
    "login_unsuccessful": MessageLookupByLibrary.simpleMessage(
      "Login sem sucesso. Tente novamente ou use outro método.",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("Sair"),
    "logout_detail": MessageLookupByLibrary.simpleMessage(
      "Tem certeza que deseja sair?",
    ),
    "logout_question_mark": MessageLookupByLibrary.simpleMessage("Sair?"),
    "loved_by": MessageLookupByLibrary.simpleMessage("Amado por "),
    "make_the_interface_feel_more_like_you": MessageLookupByLibrary.simpleMessage(
      "Faça a interface parecer mais com você — com preferências de tema, fonte e layout ao seu alcance.",
    ),
    "making_amazing": MessageLookupByLibrary.simpleMessage(
      "Deixando tudo incrível! Este vídeo de quiz vale a pena compartilhar #NoteXAI",
    ),
    "manage_recordings": MessageLookupByLibrary.simpleMessage(
      "Gerenciar gravações",
    ),
    "map_all_together": MessageLookupByLibrary.simpleMessage(
      "Mapeando tudo junto",
    ),
    "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
    "max_30_cards_per_set": MessageLookupByLibrary.simpleMessage(
      "Máximo de 30 cartões por conjunto",
    ),
    "max_30_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Máximo de 30 perguntas por conjunto",
    ),
    "max_3_flashcard_sets": MessageLookupByLibrary.simpleMessage(
      "Máximo de 3 conjuntos de cartões",
    ),
    "max_3_quiz_sets": MessageLookupByLibrary.simpleMessage(
      "Máximo de 3 conjuntos de quiz",
    ),
    "max_60_min_per_file": MessageLookupByLibrary.simpleMessage(
      "* máx. 60 min por arquivo",
    ),
    "max_ai": MessageLookupByLibrary.simpleMessage("Máx. Transcrição AI:"),
    "maybe_later": MessageLookupByLibrary.simpleMessage("Talvez mais tarde"),
    "medium": MessageLookupByLibrary.simpleMessage("Médio"),
    "meeting_data": MessageLookupByLibrary.simpleMessage("dados da reunião"),
    "migrating_your_notes": MessageLookupByLibrary.simpleMessage(
      "Migrando suas notas...",
    ),
    "migration_complete": MessageLookupByLibrary.simpleMessage(
      "Migração concluída!",
    ),
    "mind_map": MessageLookupByLibrary.simpleMessage("Mapa Mental"),
    "mind_map_gen_success": MessageLookupByLibrary.simpleMessage(
      "Mapa Mental gerado com sucesso",
    ),
    "mind_map_iap": MessageLookupByLibrary.simpleMessage("Mapa Mental"),
    "mind_map_study": MessageLookupByLibrary.simpleMessage(
      "Mapa Mental, \nGuias de Estudo",
    ),
    "minute_60_per_file": MessageLookupByLibrary.simpleMessage(
      "Essencial: 60 minutos por arquivo",
    ),
    "minute_free": MessageLookupByLibrary.simpleMessage(
      "Você usou todos os 30 minutos grátis de transcrições e resumos AI esta semana. Atualize para Pro ou espere até a próxima semana.",
    ),
    "minutes": MessageLookupByLibrary.simpleMessage("Minutos"),
    "minutes_free_left": MessageLookupByLibrary.simpleMessage(
      "Minutos Grátis Restantes",
    ),
    "minutes_remaining": MessageLookupByLibrary.simpleMessage(
      "minutos restantes",
    ),
    "mixed": MessageLookupByLibrary.simpleMessage("Misto"),
    "month": MessageLookupByLibrary.simpleMessage("mês"),
    "monthly": MessageLookupByLibrary.simpleMessage("Mensal"),
    "more_summarize": MessageLookupByLibrary.simpleMessage(
      "Resuma reuniões, podcasts, tutoriais e mais",
    ),
    "morning_content": MessageLookupByLibrary.simpleMessage(
      "Capture o brilho de hoje",
    ),
    "morning_content_2": MessageLookupByLibrary.simpleMessage(
      "Mente clara, caminho claro",
    ),
    "morning_content_3": MessageLookupByLibrary.simpleMessage(
      "As notas de hoje moldam o amanhã",
    ),
    "morning_content_4": MessageLookupByLibrary.simpleMessage(
      "Primeiro pensamento, melhor pensamento",
    ),
    "morning_content_5": MessageLookupByLibrary.simpleMessage(
      "Comece com clareza",
    ),
    "morning_content_6": MessageLookupByLibrary.simpleMessage(
      "Ideias que valem a pena guardar",
    ),
    "most_popular": MessageLookupByLibrary.simpleMessage("Mais popular"),
    "multi_language": MessageLookupByLibrary.simpleMessage("Multilíngue"),
    "multiple_people_detected": MessageLookupByLibrary.simpleMessage(
      "Várias pessoas detectadas. Use uma imagem de uma só pessoa!",
    ),
    "multiply_knowledge_with_friends": MessageLookupByLibrary.simpleMessage(
      "Multiplique conhecimento com amigos",
    ),
    "my_notes": MessageLookupByLibrary.simpleMessage("Minhas notas"),
    "name": MessageLookupByLibrary.simpleMessage("Nome"),
    "network_error": MessageLookupByLibrary.simpleMessage(
      "Erro de rede. Verifique sua conexão e tente novamente.",
    ),
    "neutral": MessageLookupByLibrary.simpleMessage("Neutro"),
    "neutral_description": MessageLookupByLibrary.simpleMessage(
      "Apresentação direta e factual",
    ),
    "new_new": MessageLookupByLibrary.simpleMessage("Novo"),
    "new_note": MessageLookupByLibrary.simpleMessage("Nova nota"),
    "new_recording": MessageLookupByLibrary.simpleMessage("Nova Gravação - "),
    "newest_first": MessageLookupByLibrary.simpleMessage(
      "Mais recente primeiro",
    ),
    "next_bill_date": m2,
    "no": MessageLookupByLibrary.simpleMessage("Não"),
    "no_generated": MessageLookupByLibrary.simpleMessage(
      "Nenhum quiz/flashcard gerado. Toque para criar agora!",
    ),
    "no_input": MessageLookupByLibrary.simpleMessage(
      "Nenhum dado fornecido. Carregue um áudio ou insira uma URL do YouTube.",
    ),
    "no_internet": MessageLookupByLibrary.simpleMessage(
      "Sem conexão com a internet",
    ),
    "no_internet_connection": MessageLookupByLibrary.simpleMessage(
      "Sem conexão com a internet!",
    ),
    "no_notes_found": MessageLookupByLibrary.simpleMessage(
      "Nenhuma nota encontrada com este filtro.\nRedefina a seleção de filtro",
    ),
    "no_notes_in_folder": MessageLookupByLibrary.simpleMessage(
      "Não há notas nesta pasta.",
    ),
    "no_payment_now": MessageLookupByLibrary.simpleMessage(
      "✓ Sem pagamento agora",
    ),
    "no_person_detected": MessageLookupByLibrary.simpleMessage(
      "Nenhuma pessoa detectada. Use uma imagem com alguém!",
    ),
    "no_recording_credit": MessageLookupByLibrary.simpleMessage(
      "Uso restante de gravação insuficiente. Atualize seu plano.",
    ),
    "no_recordings": MessageLookupByLibrary.simpleMessage("Nenhuma gravação"),
    "no_results_found": MessageLookupByLibrary.simpleMessage(
      "Nenhum resultado encontrado para",
    ),
    "no_speech_detected": MessageLookupByLibrary.simpleMessage(
      "Nenhuma fala detectada",
    ),
    "no_summary": MessageLookupByLibrary.simpleMessage(
      "Nenhum resumo disponível para esta nota.",
    ),
    "no_transcript": MessageLookupByLibrary.simpleMessage(
      "Nenhuma transcrição disponível para esta nota.",
    ),
    "no_upload_credit": MessageLookupByLibrary.simpleMessage(
      "Uso de upload insuficiente. Atualize seu plano.",
    ),
    "no_url_provided": MessageLookupByLibrary.simpleMessage(
      "Nenhum URL de exportação fornecido.",
    ),
    "no_voice_available": MessageLookupByLibrary.simpleMessage(
      "Nenhuma voz disponível",
    ),
    "not_found_audio": MessageLookupByLibrary.simpleMessage(
      "Arquivo de áudio não encontrado",
    ),
    "not_open_mail": MessageLookupByLibrary.simpleMessage(
      "Não foi possível abrir o e-mail!",
    ),
    "not_open_web": MessageLookupByLibrary.simpleMessage(
      "Não foi possível abrir a web!",
    ),
    "not_summarized_note": MessageLookupByLibrary.simpleMessage(
      "Resumo faltando! Clique para ativar o AI 👇",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Nota"),
    "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
    "noteX_lifetime_essential": MessageLookupByLibrary.simpleMessage(
      "NoteX Essencial Vitalício",
    ),
    "noteX_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "NoteX Pro Vitalício",
    ),
    "note_404": MessageLookupByLibrary.simpleMessage(
      "Nota não encontrada. Verifique o ID e tente novamente.",
    ),
    "note_not_ready": MessageLookupByLibrary.simpleMessage(
      "A nota não está pronta para exportação. Aguarde a conclusão do processamento.",
    ),
    "note_reminders": MessageLookupByLibrary.simpleMessage(
      "Lembretes de Notas",
    ),
    "note_sharing": MessageLookupByLibrary.simpleMessage(
      "Compartilhamento de Notas",
    ),
    "note_tabs": MessageLookupByLibrary.simpleMessage("Abas da nota"),
    "note_taker": MessageLookupByLibrary.simpleMessage(
      "#1 Assistente de Notas AI",
    ),
    "notes": MessageLookupByLibrary.simpleMessage("notas"),
    "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX vazio"),
    "notex_experience": MessageLookupByLibrary.simpleMessage(
      "sua experiência com o NoteX",
    ),
    "nothing_restore": MessageLookupByLibrary.simpleMessage(
      "Nada para restaurar",
    ),
    "noti_default_description": MessageLookupByLibrary.simpleMessage(
      "Prepare-se e comece a gravar! 🚀",
    ),
    "noti_default_title": MessageLookupByLibrary.simpleMessage(
      "Hora de gravar",
    ),
    "noti_req_description": MessageLookupByLibrary.simpleMessage(
      "Notificações podem incluir alertas, sons e badges. Configure em Configurações.",
    ),
    "noti_req_title": MessageLookupByLibrary.simpleMessage(
      "‘NoteX’ Gostaria de Enviar Notificações",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("Notificações"),
    "notifications_note_created": MessageLookupByLibrary.simpleMessage(
      "Suas notas foram criadas com sucesso",
    ),
    "notifications_note_ready": MessageLookupByLibrary.simpleMessage(
      "Notificar quando notas estiverem prontas",
    ),
    "nova_ai_assistant_mind_mapping": MessageLookupByLibrary.simpleMessage(
      "Assistente Nova AI & Mapas Mentais",
    ),
    "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Chat Nova AI"),
    "nova_chat": MessageLookupByLibrary.simpleMessage("Chat Nova"),
    "of_index": MessageLookupByLibrary.simpleMessage("de"),
    "of_user": MessageLookupByLibrary.simpleMessage(" de Usuários"),
    "offer_expires": MessageLookupByLibrary.simpleMessage(
      "Oferta limitada no tempo",
    ),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "oldest_first": MessageLookupByLibrary.simpleMessage(
      "Mais antigo primeiro",
    ),
    "on_track": MessageLookupByLibrary.simpleMessage("no caminho"),
    "on_your_android": MessageLookupByLibrary.simpleMessage(
      "No seu celular ou tablet Android, abra a Google Play Store",
    ),
    "onboarding_generate_audio_video_content":
        MessageLookupByLibrary.simpleMessage("Transforme notas em"),
    "onboarding_generate_audio_video_full_content":
        MessageLookupByLibrary.simpleMessage(
          "Transforme notas em conteúdo envolvente",
        ),
    "onboarding_generate_audio_video_sub_content":
        MessageLookupByLibrary.simpleMessage("conteúdo envolvente"),
    "onboarding_generate_audio_video_title":
        MessageLookupByLibrary.simpleMessage("Gerar Áudio e Vídeo"),
    "once_in_a_lifetime_offer": MessageLookupByLibrary.simpleMessage(
      "Oferta única na vida",
    ),
    "one_per_day": MessageLookupByLibrary.simpleMessage("1 por dia"),
    "one_time_payment": MessageLookupByLibrary.simpleMessage("pagamento único"),
    "only": MessageLookupByLibrary.simpleMessage("Apenas"),
    "only_today": MessageLookupByLibrary.simpleMessage("Somente Hoje"),
    "oops_something_went_wrong": MessageLookupByLibrary.simpleMessage(
      "Ops!\nAlgo deu errado",
    ),
    "open_now": MessageLookupByLibrary.simpleMessage("Abrir agora"),
    "open_youtube": MessageLookupByLibrary.simpleMessage("Abrir YouTube"),
    "opportunities": MessageLookupByLibrary.simpleMessage("oportunidades"),
    "or": MessageLookupByLibrary.simpleMessage("ou"),
    "or_upper": MessageLookupByLibrary.simpleMessage("Ou"),
    "organize_assign_action_items": MessageLookupByLibrary.simpleMessage(
      "Organize e Atribua Itens de Ação",
    ),
    "organize_assign_items": MessageLookupByLibrary.simpleMessage(
      "Organize e Atribua Itens de Ação",
    ),
    "others": MessageLookupByLibrary.simpleMessage("Outros"),
    "output_language": MessageLookupByLibrary.simpleMessage("Idioma de Saída"),
    "pace": MessageLookupByLibrary.simpleMessage("ritmo"),
    "paste": MessageLookupByLibrary.simpleMessage("Colar"),
    "paste_url_here": MessageLookupByLibrary.simpleMessage("Cole a URL aqui"),
    "paste_youtube_link": MessageLookupByLibrary.simpleMessage(
      "Cole um link do YouTube",
    ),
    "payment_required": MessageLookupByLibrary.simpleMessage(
      "Você usou todos os créditos grátis",
    ),
    "payment_successfully": MessageLookupByLibrary.simpleMessage(
      "Pagamento Concluído",
    ),
    "pdf_export": MessageLookupByLibrary.simpleMessage("Exportação PDF"),
    "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
    "per_year": MessageLookupByLibrary.simpleMessage(" por ano"),
    "period": MessageLookupByLibrary.simpleMessage(" de %s minutos"),
    "personalized_learning": MessageLookupByLibrary.simpleMessage(
      "Pratique para",
    ),
    "personalized_learning_at": MessageLookupByLibrary.simpleMessage(
      "Pratique para alcançar nota máxima",
    ),
    "photos": MessageLookupByLibrary.simpleMessage("Fotos"),
    "pick_specific_language": MessageLookupByLibrary.simpleMessage(
      "Escolha um idioma específico no áudio para melhor precisão",
    ),
    "plan": MessageLookupByLibrary.simpleMessage("Plano"),
    "please_select_a_language": MessageLookupByLibrary.simpleMessage(
      "Selecione um idioma primeiro para transcrever o áudio com precisão!",
    ),
    "please_select_a_youtube_language": MessageLookupByLibrary.simpleMessage(
      "Por favor, selecione um idioma de resumo. Este é o idioma que você verá na saída do resumo",
    ),
    "please_try_again": MessageLookupByLibrary.simpleMessage("Tente novamente"),
    "please_wait": MessageLookupByLibrary.simpleMessage("Aguarde"),
    "podcast": MessageLookupByLibrary.simpleMessage("Podcast"),
    "podcast_name": MessageLookupByLibrary.simpleMessage("Nome do podcast"),
    "policy": MessageLookupByLibrary.simpleMessage("Política"),
    "premium_features": MessageLookupByLibrary.simpleMessage(
      "Experimente recursos premium para ver a diferença",
    ),
    "preparing_video": MessageLookupByLibrary.simpleMessage(
      "Preparando vídeo...",
    ),
    "press_back_again_to_exit": MessageLookupByLibrary.simpleMessage(
      "Pressione Voltar novamente para sair!",
    ),
    "preview_only": MessageLookupByLibrary.simpleMessage(
      "Apenas pré-visualização. O plano de fundo será gerado por IA com base no conteúdo",
    ),
    "priority_processing": MessageLookupByLibrary.simpleMessage(
      "Processamento prioritário",
    ),
    "privacy_policy": MessageLookupByLibrary.simpleMessage(
      "Política de Privacidade",
    ),
    "private": MessageLookupByLibrary.simpleMessage("Privado"),
    "pro": MessageLookupByLibrary.simpleMessage("Plano PRO"),
    "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
    "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
    "pro_6_hours": MessageLookupByLibrary.simpleMessage(
      "Pro: 6 horas por arquivo",
    ),
    "pro_access_life_time": MessageLookupByLibrary.simpleMessage(
      "ACESSO PRO VITALÍCIO",
    ),
    "pro_lifetime": MessageLookupByLibrary.simpleMessage("PRO Vitalício"),
    "process_your_document": MessageLookupByLibrary.simpleMessage(
      "Processando seu documento...",
    ),
    "process_your_text": MessageLookupByLibrary.simpleMessage(
      "Processando seu texto...",
    ),
    "processing_content": MessageLookupByLibrary.simpleMessage(
      "Processando conteúdo...",
    ),
    "processing_file": MessageLookupByLibrary.simpleMessage(
      "Processando arquivo...",
    ),
    "processing_image": MessageLookupByLibrary.simpleMessage(
      "Processando imagem...",
    ),
    "processing_note_audio_file": MessageLookupByLibrary.simpleMessage(
      "Processando Seu Áudio...",
    ),
    "processing_note_recording": MessageLookupByLibrary.simpleMessage(
      "Processando Sua Gravação...",
    ),
    "processing_note_youtube": MessageLookupByLibrary.simpleMessage(
      "Processando Vídeo do YouTube...",
    ),
    "processing_web_link": MessageLookupByLibrary.simpleMessage(
      "Processando link web",
    ),
    "producing_flashcards": MessageLookupByLibrary.simpleMessage(
      "Produzindo Flashcards AI...",
    ),
    "professional": MessageLookupByLibrary.simpleMessage("Uso Profissional"),
    "professional_description": MessageLookupByLibrary.simpleMessage(
      "Linguagem formal adequada para contexto de trabalho",
    ),
    "professional_style": MessageLookupByLibrary.simpleMessage("Profissional"),
    "public": MessageLookupByLibrary.simpleMessage("Público"),
    "purchase_fail": MessageLookupByLibrary.simpleMessage(
      "Falha na compra! Tente novamente!",
    ),
    "purchase_init_fail": MessageLookupByLibrary.simpleMessage(
      "Ops! Não conseguimos iniciar sua compra. Tente novamente.",
    ),
    "purpose_using": MessageLookupByLibrary.simpleMessage("propósito ao usar "),
    "quarter": MessageLookupByLibrary.simpleMessage("trimestre"),
    "quarterly": MessageLookupByLibrary.simpleMessage("Trimestral"),
    "question": MessageLookupByLibrary.simpleMessage("Pergunta"),
    "quick_access": MessageLookupByLibrary.simpleMessage("Acesso Rápido"),
    "quick_import": MessageLookupByLibrary.simpleMessage(
      "Ou importe rapidamente de",
    ),
    "quickly": MessageLookupByLibrary.simpleMessage("conceitos"),
    "quiz": MessageLookupByLibrary.simpleMessage("Questionário"),
    "quiz_count": MessageLookupByLibrary.simpleMessage("Contagem de quizzes"),
    "quiz_diff": MessageLookupByLibrary.simpleMessage("Dificuldade do quiz"),
    "quiz_gen_success": MessageLookupByLibrary.simpleMessage(
      "Quiz gerado com sucesso",
    ),
    "quiz_iap": MessageLookupByLibrary.simpleMessage("Conjunto de Quiz"),
    "quiz_master": MessageLookupByLibrary.simpleMessage("Mestre de Quiz AI"),
    "quiz_score": MessageLookupByLibrary.simpleMessage("Pontuação do Quiz"),
    "quiz_set": MessageLookupByLibrary.simpleMessage("Conjuntos de quizzes"),
    "quiz_set_not_found": MessageLookupByLibrary.simpleMessage(
      "Conjunto de quiz não encontrado",
    ),
    "quizz_for": MessageLookupByLibrary.simpleMessage("Quiz para"),
    "quizzes": MessageLookupByLibrary.simpleMessage("Questionários"),
    "rate": MessageLookupByLibrary.simpleMessage("Avaliar"),
    "rate_five_stars": MessageLookupByLibrary.simpleMessage(
      "Dê-nos 5 estrelas",
    ),
    "rate_us_on_store": MessageLookupByLibrary.simpleMessage(
      "Avalie-nos na loja",
    ),
    "rating_cmt1": MessageLookupByLibrary.simpleMessage(
      "Este app é incrível e só melhora. Obrigado aos desenvolvedores pelo trabalho e dedicação. Recomendo acima de outros apps de notas AI na App Store.",
    ),
    "rating_cmt2": MessageLookupByLibrary.simpleMessage(
      "Amo este app! O parceiro perfeito para minhas reuniões.",
    ),
    "rating_cmt3": MessageLookupByLibrary.simpleMessage(
      "Reduziu meu tempo de estudo pela metade. Mais tempo para pausas de café!",
    ),
    "rating_cmt4": MessageLookupByLibrary.simpleMessage(
      "Este app é impressionante! Além de transcrições perfeitas, oferece resumos, outlines e itens de ação incríveis. Genial!",
    ),
    "rating_cmt5": MessageLookupByLibrary.simpleMessage(
      "Maravilhoso, poderoso! Tudo que você quer e mais.",
    ),
    "rating_cmt6": MessageLookupByLibrary.simpleMessage(
      "Testei hoje com uma apresentação do YouTube. Em segundos, recebi uma transcrição completa, um mapa mental bem desenhado e flashcards sobre o tema. Superou minhas expectativas. Eficiente, sofisticado e muito útil. Será meu app diário para notas e aprendizado.",
    ),
    "rating_cmt7": MessageLookupByLibrary.simpleMessage(
      "Até agora, o melhor app de notas que encontrei. Tem muitos recursos úteis.",
    ),
    "rating_cmt8": MessageLookupByLibrary.simpleMessage(
      "Captura cada detalhe de aulas longas de biologia. O recurso de resumo é um salva-vidas na preparação para exames.",
    ),
    "rating_sub_context_1": MessageLookupByLibrary.simpleMessage("Incrível!"),
    "rating_sub_context_2": MessageLookupByLibrary.simpleMessage(
      "Pro para Reuniões",
    ),
    "rating_sub_context_3": MessageLookupByLibrary.simpleMessage(
      "Economia de Tempo",
    ),
    "rating_sub_context_4": MessageLookupByLibrary.simpleMessage(
      "Melhor App de Notas AI",
    ),
    "rating_sub_context_5": MessageLookupByLibrary.simpleMessage(
      "Adoro Este App",
    ),
    "rating_sub_context_6": MessageLookupByLibrary.simpleMessage(
      "Melhor App de Notas AI",
    ),
    "rating_sub_context_7": MessageLookupByLibrary.simpleMessage(
      "Melhor App de Notas que Usei",
    ),
    "rating_sub_context_8": MessageLookupByLibrary.simpleMessage(
      "O Melhor Até Agora",
    ),
    "record": MessageLookupByLibrary.simpleMessage("Gravação"),
    "record_audio": MessageLookupByLibrary.simpleMessage("Gravar Áudio"),
    "record_audio_coming_soon": MessageLookupByLibrary.simpleMessage(
      "Gravar Áudio (em breve)",
    ),
    "record_over_x_min": MessageLookupByLibrary.simpleMessage(
      "Gravação Acima de %s Minutos",
    ),
    "record_over_x_min_details": MessageLookupByLibrary.simpleMessage(
      "Suas gravações serão salvas localmente sem transcrição ou resumo AI. Remova limites para processar após concluir.",
    ),
    "record_summarize_lecture": MessageLookupByLibrary.simpleMessage(
      "Grave e Resuma Aulas Universitárias",
    ),
    "recording": MessageLookupByLibrary.simpleMessage("Gravação"),
    "recording_in_progress": MessageLookupByLibrary.simpleMessage(
      "Gravação em andamento",
    ),
    "recording_in_progress_content": MessageLookupByLibrary.simpleMessage(
      "Gravando...",
    ),
    "recording_paused": MessageLookupByLibrary.simpleMessage(
      "Gravação pausada",
    ),
    "recording_paused_content": MessageLookupByLibrary.simpleMessage(
      "Pressione para retomar",
    ),
    "recording_permission_denied": MessageLookupByLibrary.simpleMessage(
      "Permissão de gravação negada!",
    ),
    "recording_permission_denied_details": MessageLookupByLibrary.simpleMessage(
      "Vá para Configurações para permitir",
    ),
    "recording_quality": MessageLookupByLibrary.simpleMessage(
      "Qualidade da gravação",
    ),
    "recording_schedule": MessageLookupByLibrary.simpleMessage(
      "Agendamento de Gravação",
    ),
    "recording_voice_note": MessageLookupByLibrary.simpleMessage(
      "Gravando Nota de Voz",
    ),
    "redeem_7_days_for_0": MessageLookupByLibrary.simpleMessage(
      "Redeem 7 dias por 0",
    ),
    "redeem_credits": MessageLookupByLibrary.simpleMessage("Resgatar"),
    "refer_now": MessageLookupByLibrary.simpleMessage("Indique Agora"),
    "refer_rewards": MessageLookupByLibrary.simpleMessage("Indique e Ganhe"),
    "referral": MessageLookupByLibrary.simpleMessage("Indicação"),
    "referral_already_used": MessageLookupByLibrary.simpleMessage(
      "Código de indicação já foi usado.",
    ),
    "referral_code": MessageLookupByLibrary.simpleMessage(
      "Código de indicação",
    ),
    "referral_credits": MessageLookupByLibrary.simpleMessage(
      "Créditos de Indicação",
    ),
    "referral_not_found": MessageLookupByLibrary.simpleMessage(
      "Código de indicação não encontrado.",
    ),
    "referral_self_use": MessageLookupByLibrary.simpleMessage(
      "Você não pode usar seu próprio código de indicação.",
    ),
    "referral_time_expired": MessageLookupByLibrary.simpleMessage(
      "Código de indicação expirou após 24 horas.",
    ),
    "referral_validation_err": MessageLookupByLibrary.simpleMessage(
      "Erro de validação de indicação.",
    ),
    "reload_tap": MessageLookupByLibrary.simpleMessage(
      "Erro, toque para recarregar",
    ),
    "remain_recording_length": MessageLookupByLibrary.simpleMessage(
      "30s - 5min restantes com base na duração...",
    ),
    "reminders_record_audio": MessageLookupByLibrary.simpleMessage(
      "Configure horários semanais de gravação de áudio",
    ),
    "remove_all_limits": MessageLookupByLibrary.simpleMessage(
      "Remover Todos os Limites",
    ),
    "replace": MessageLookupByLibrary.simpleMessage("Substituir"),
    "replace_all": MessageLookupByLibrary.simpleMessage("Substituir Tudo"),
    "report_issue": MessageLookupByLibrary.simpleMessage(
      "Como Reportar um Problema?",
    ),
    "report_issue2": MessageLookupByLibrary.simpleMessage(
      "Estamos Aqui para Ajudar:",
    ),
    "required": MessageLookupByLibrary.simpleMessage("Ex: Nome_pasta A"),
    "reset": MessageLookupByLibrary.simpleMessage("Redefinir"),
    "restart_now": MessageLookupByLibrary.simpleMessage("Reiniciar agora"),
    "restore": MessageLookupByLibrary.simpleMessage("Restaurar"),
    "restore_fail_message": MessageLookupByLibrary.simpleMessage(
      "Para assistência, contate <EMAIL>",
    ),
    "restore_fail_title": MessageLookupByLibrary.simpleMessage(
      "Nenhum item disponível para restauração",
    ),
    "restore_purchase": MessageLookupByLibrary.simpleMessage(
      "Restaurar compra",
    ),
    "restore_success_title": MessageLookupByLibrary.simpleMessage(
      "Restauração concluída",
    ),
    "retention": MessageLookupByLibrary.simpleMessage("e retenção"),
    "retention_quickly": MessageLookupByLibrary.simpleMessage(
      "entre conceitos",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Tentar novamente"),
    "sale_off": MessageLookupByLibrary.simpleMessage("DESCONTO"),
    "satisfied": MessageLookupByLibrary.simpleMessage(
      "Obrigado pelo feedback!",
    ),
    "satisfied_quality": MessageLookupByLibrary.simpleMessage(
      "Esta nota está clara e útil?",
    ),
    "save": MessageLookupByLibrary.simpleMessage("Salvar"),
    "save_50": MessageLookupByLibrary.simpleMessage("Economize 50%"),
    "save_changes": MessageLookupByLibrary.simpleMessage("Salvar Alterações?"),
    "save_chat": MessageLookupByLibrary.simpleMessage("Salvar chat"),
    "save_file": MessageLookupByLibrary.simpleMessage("Arquivo salvo"),
    "saved_chat": MessageLookupByLibrary.simpleMessage("Chat salvo"),
    "saved_successfully": MessageLookupByLibrary.simpleMessage(
      "Salvo com sucesso",
    ),
    "saving_recording": MessageLookupByLibrary.simpleMessage(
      "Salvando gravação no dispositivo",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Buscar"),
    "search_emoji": MessageLookupByLibrary.simpleMessage("Buscar Emoji"),
    "search_in_files": MessageLookupByLibrary.simpleMessage(
      "Buscar em Arquivos",
    ),
    "searching_all_notes": MessageLookupByLibrary.simpleMessage(
      "Buscando todas as notas",
    ),
    "seconds": MessageLookupByLibrary.simpleMessage("Segundos"),
    "select_a_language": MessageLookupByLibrary.simpleMessage(
      "Selecione um idioma para usar durante o processo de gravação antes de salvar.",
    ),
    "select_all": MessageLookupByLibrary.simpleMessage("Selecionar tudo"),
    "select_and_reorder": MessageLookupByLibrary.simpleMessage(
      "Selecione e reordene seus módulos de notas. É necessário no mínimo 4 abas para continuar.",
    ),
    "select_language": MessageLookupByLibrary.simpleMessage(
      "Selecionar idioma",
    ),
    "select_your_note": MessageLookupByLibrary.simpleMessage(
      "Selecione seus módulos de nota.",
    ),
    "select_your_primary_use_case": MessageLookupByLibrary.simpleMessage(
      "Selecione seu caso de uso principal",
    ),
    "server_err": MessageLookupByLibrary.simpleMessage(
      "Ocorreu um erro desconhecido no servidor.",
    ),
    "server_error": MessageLookupByLibrary.simpleMessage("Algo deu errado"),
    "setting": MessageLookupByLibrary.simpleMessage("Configurações"),
    "settings": MessageLookupByLibrary.simpleMessage("Configurações"),
    "seven_day_free": MessageLookupByLibrary.simpleMessage(
      "7 dias grátis, depois",
    ),
    "share": MessageLookupByLibrary.simpleMessage("Enviar aos Amigos"),
    "share_audio_file": MessageLookupByLibrary.simpleMessage(
      "Compartilhar áudio",
    ),
    "share_code_friends": MessageLookupByLibrary.simpleMessage(
      "Compartilhe o código com amigos por e-mail, redes sociais ou mensagens.",
    ),
    "share_file": MessageLookupByLibrary.simpleMessage(
      "Compartilhar Arquivo de Áudio",
    ),
    "share_note": MessageLookupByLibrary.simpleMessage("Compartilhar Notas"),
    "share_note_link": MessageLookupByLibrary.simpleMessage(
      "Compartilhar nota",
    ),
    "share_only": MessageLookupByLibrary.simpleMessage("Compartilhar"),
    "share_referral_code_start_earning_credits":
        MessageLookupByLibrary.simpleMessage(
          "Compartilhe seu código de indicação para ganhar créditos!",
        ),
    "share_summary": MessageLookupByLibrary.simpleMessage("Copiar Resumo"),
    "share_sync": MessageLookupByLibrary.simpleMessage(
      "Compartilhar e Sincronizar",
    ),
    "share_transcript": MessageLookupByLibrary.simpleMessage(
      "Copiar Transcrição",
    ),
    "share_with_link": MessageLookupByLibrary.simpleMessage(
      "Compartilhar com link:",
    ),
    "shared": MessageLookupByLibrary.simpleMessage("Compartilhado"),
    "sharing_export": MessageLookupByLibrary.simpleMessage(
      "Compartilhamento e Exportação",
    ),
    "short": MessageLookupByLibrary.simpleMessage("Curto"),
    "short_description": MessageLookupByLibrary.simpleMessage(
      "Apenas pontos-chave",
    ),
    "shorts": MessageLookupByLibrary.simpleMessage("Curtos"),
    "show_your_love": MessageLookupByLibrary.simpleMessage(
      "Mostre seu apoio dando-nos uma",
    ),
    "signing_in": MessageLookupByLibrary.simpleMessage("Entrando..."),
    "skip": MessageLookupByLibrary.simpleMessage("Pular"),
    "slide_count": MessageLookupByLibrary.simpleMessage("Criar diapositivas"),
    "slide_count_tooltip": MessageLookupByLibrary.simpleMessage(
      "Máximo de 12 diapositivas por conjunto",
    ),
    "slide_range": MessageLookupByLibrary.simpleMessage("Criar diapositivas"),
    "slide_show": MessageLookupByLibrary.simpleMessage("Apresentação"),
    "smart_learning": MessageLookupByLibrary.simpleMessage(
      "Aprendizado Inteligente",
    ),
    "smart_note_big_ideas": MessageLookupByLibrary.simpleMessage(
      "Notas Smart, Ideias Grandes",
    ),
    "smart_quizzes": MessageLookupByLibrary.simpleMessage(
      "Quizzes Adaptativos Ilimitados",
    ),
    "smart_start": MessageLookupByLibrary.simpleMessage(
      "Pacote inicial inteligente",
    ),
    "sort_by": MessageLookupByLibrary.simpleMessage("Ordenar por"),
    "special_gift": MessageLookupByLibrary.simpleMessage("Presente especial"),
    "special_gift_title": MessageLookupByLibrary.simpleMessage(
      "PRESENTE ESPECIAL",
    ),
    "special_offer": MessageLookupByLibrary.simpleMessage("OFERTA\nESPECIAL"),
    "speech_language": MessageLookupByLibrary.simpleMessage("Idioma da Fala"),
    "start_for_free": MessageLookupByLibrary.simpleMessage("Comece Grátis"),
    "start_free_trial": MessageLookupByLibrary.simpleMessage(
      "Iniciar teste grátis",
    ),
    "start_my_7_day_trial": MessageLookupByLibrary.simpleMessage(
      "Iniciar Meu Teste de 7 Dias",
    ),
    "start_record": MessageLookupByLibrary.simpleMessage("Iniciar Gravação"),
    "start_speaking": MessageLookupByLibrary.simpleMessage("Comece a Falar"),
    "step1": MessageLookupByLibrary.simpleMessage(
      "No app NoteX, vá para Configurações.",
    ),
    "step2": MessageLookupByLibrary.simpleMessage(
      "Localize a versão do app no final (ex.: v1.4.0(6)).",
    ),
    "step3": MessageLookupByLibrary.simpleMessage(
      "Toque na versão do app 5 vezes rapidamente.",
    ),
    "step4": MessageLookupByLibrary.simpleMessage(
      "Seu userID único será copiado para a área de transferência.",
    ),
    "step5": MessageLookupByLibrary.simpleMessage(
      "Na sua mensagem abaixo, inclua:",
    ),
    "step51": MessageLookupByLibrary.simpleMessage(
      "Seu userID (cole da área de transferência).",
    ),
    "step52": MessageLookupByLibrary.simpleMessage(
      "Uma breve descrição do problema.",
    ),
    "step53": MessageLookupByLibrary.simpleMessage(
      "Detalhes relevantes (ex.: modelo do dispositivo, versão do iOS).",
    ),
    "step6": MessageLookupByLibrary.simpleMessage("Envie um e-mail para "),
    "student": MessageLookupByLibrary.simpleMessage("Uso Acadêmico"),
    "style": MessageLookupByLibrary.simpleMessage("Estilo"),
    "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
    "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
      "Usuários assinantes têm uso ilimitado e acesso a recursos premium sem anúncios. Usuários não assinantes usam o app com anúncios e limite nos recursos premium. Pagamento será cobrado na conta Google Play ao confirmar a compra. A assinatura será renovada automaticamente, a menos que cancelada 24 horas antes do fim do período. Sua conta será cobrada pelo plano de renovação 24 horas antes do fim do período atual. Qualquer período de teste grátis será perdido ao comprar uma assinatura, se aplicável. Gerencie ou desative a renovação automática na página de assinaturas do Google Play após a compra. Desinstalar o app não cancela a assinatura.",
    ),
    "sub_will_auto_renew": MessageLookupByLibrary.simpleMessage(
      "Assinaturas renovam automaticamente. Cancele quando quiser.",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Enviar"),
    "submit_button": MessageLookupByLibrary.simpleMessage("Enviar"),
    "subscribe": MessageLookupByLibrary.simpleMessage("Assinar"),
    "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
      "Se inscrito via web, gerencie em notexapp.com/setting",
    ),
    "success": MessageLookupByLibrary.simpleMessage("Sucesso"),
    "successfully": MessageLookupByLibrary.simpleMessage("Com sucesso"),
    "suggest_features": MessageLookupByLibrary.simpleMessage(
      "Sugerir Recursos",
    ),
    "suggested": MessageLookupByLibrary.simpleMessage("Sugerido"),
    "summarize_video": MessageLookupByLibrary.simpleMessage(
      "Resuma Vídeos Longos do YouTube",
    ),
    "summary": MessageLookupByLibrary.simpleMessage("Resumo"),
    "summary_style": MessageLookupByLibrary.simpleMessage("Estilo de Resumo"),
    "summary_successful": MessageLookupByLibrary.simpleMessage(
      "Resumo criado com sucesso!",
    ),
    "summary_usefulness": MessageLookupByLibrary.simpleMessage(
      "Utilidade do resumo",
    ),
    "supercharge": MessageLookupByLibrary.simpleMessage(
      "Alcance Mais, Menos Estresse",
    ),
    "support_audio": MessageLookupByLibrary.simpleMessage(
      "Tipos de arquivo suportados: .mp3, .wav, .ogg, .m4a",
    ),
    "support_for_up_to_10_images": MessageLookupByLibrary.simpleMessage(
      "Suporte para até 10 imagens",
    ),
    "support_image": MessageLookupByLibrary.simpleMessage(
      "Tipos de imagem suportados: .png, .jpg, .heif, .heic",
    ),
    "support_over_onehundred_languages": MessageLookupByLibrary.simpleMessage(
      "Suporte a mais de 100 idiomas",
    ),
    "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
      "Suporta YouTube, Web, TikTok, Instagram, Facebook e mais",
    ),
    "switch_mode": MessageLookupByLibrary.simpleMessage("Mudar Modo"),
    "sync_from_watch": MessageLookupByLibrary.simpleMessage(
      "Sincronizar do relógio",
    ),
    "sync_notes": MessageLookupByLibrary.simpleMessage(
      "Sincronize notas no navegador do computador",
    ),
    "system": MessageLookupByLibrary.simpleMessage("Sistema"),
    "tap_cancel": MessageLookupByLibrary.simpleMessage(
      "Toque em Cancelar assinatura",
    ),
    "tap_menu": MessageLookupByLibrary.simpleMessage(
      "Toque em Menu Assinaturas e selecione a assinatura a cancelar",
    ),
    "tap_the": MessageLookupByLibrary.simpleMessage("Toque no"),
    "tap_the_record": MessageLookupByLibrary.simpleMessage("Toque em Gravar"),
    "tap_to_record": MessageLookupByLibrary.simpleMessage(
      "Toque para gravar seus pensamentos",
    ),
    "task_create_err": MessageLookupByLibrary.simpleMessage(
      "Erro ao criar tarefa. Tente depois.",
    ),
    "templates": MessageLookupByLibrary.simpleMessage("Modelos"),
    "term_and_cond": MessageLookupByLibrary.simpleMessage("Termos e Condições"),
    "terms": MessageLookupByLibrary.simpleMessage("Termos"),
    "terms_of_sub": MessageLookupByLibrary.simpleMessage(
      "Termos das Assinaturas",
    ),
    "terms_of_use": MessageLookupByLibrary.simpleMessage("Termos de Uso"),
    "text": MessageLookupByLibrary.simpleMessage("Adicionar Texto"),
    "text_must_not_exceed_50_chars": MessageLookupByLibrary.simpleMessage(
      "O texto deve ter até 50 caracteres",
    ),
    "thank_feedback": MessageLookupByLibrary.simpleMessage(
      "Obrigado pelo Feedback!",
    ),
    "thinking": MessageLookupByLibrary.simpleMessage("Pensando..."),
    "thirty_min_per": MessageLookupByLibrary.simpleMessage(
      "30 min por \n semana",
    ),
    "this_folder_empty": MessageLookupByLibrary.simpleMessage(
      "Hora de criar sua primeira nota AI! ✨",
    ),
    "this_free_trial": MessageLookupByLibrary.simpleMessage(
      "Este teste grátis é uma oferta inicial apenas para novos usuários. Experimente todos os recursos Pro por uma semana antes de decidir.",
    ),
    "this_is_the_language": MessageLookupByLibrary.simpleMessage(
      "Este é o idioma que você verá na saída do resumo",
    ),
    "thousands_trusted": MessageLookupByLibrary.simpleMessage(
      "Nota 4.8/5: Confiável por milhares",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Hora"),
    "time_black_friday": MessageLookupByLibrary.simpleMessage("22 - 30 Nov"),
    "time_black_friday_2": MessageLookupByLibrary.simpleMessage(
      "22 - 30 Novembro",
    ),
    "time_out": MessageLookupByLibrary.simpleMessage(
      "Tempo esgotado. Tente novamente.",
    ),
    "title": MessageLookupByLibrary.simpleMessage("Título"),
    "title_error_note": MessageLookupByLibrary.simpleMessage(
      "Criação de Nota Sem Sucesso",
    ),
    "title_success_note": MessageLookupByLibrary.simpleMessage(
      "Notas AI Criadas com Sucesso",
    ),
    "to": MessageLookupByLibrary.simpleMessage("para"),
    "to_day": MessageLookupByLibrary.simpleMessage("Hoje"),
    "token_expired": MessageLookupByLibrary.simpleMessage("Token expirado!"),
    "tolower_credits": MessageLookupByLibrary.simpleMessage("créditos"),
    "tool_tip_language": MessageLookupByLibrary.simpleMessage(
      "Selecione o idioma principal da fala antes de salvar esta gravação",
    ),
    "topic_option": MessageLookupByLibrary.simpleMessage("Tema (opcional)"),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "transcribing": MessageLookupByLibrary.simpleMessage(
      "Transcrevendo com o melhor AI",
    ),
    "transcribing_audio": MessageLookupByLibrary.simpleMessage(
      "Transcrevendo áudio...",
    ),
    "transcript": MessageLookupByLibrary.simpleMessage("Transcrição"),
    "transcript_context": MessageLookupByLibrary.simpleMessage(
      "Contexto da Transcrição",
    ),
    "transcript_language": MessageLookupByLibrary.simpleMessage(
      "Idioma da Transcrição",
    ),
    "transcript_line_cannot_be_empty": MessageLookupByLibrary.simpleMessage(
      "Linha de transcrição não pode estar vazia",
    ),
    "transcript_line_tool_tip": MessageLookupByLibrary.simpleMessage(
      "Clique no item da transcrição para editar",
    ),
    "transcription_precision": MessageLookupByLibrary.simpleMessage(
      "Precisão da transcrição",
    ),
    "transform_meetings": MessageLookupByLibrary.simpleMessage(
      "Transforme reuniões em",
    ),
    "transform_meetings_into_actionable_intelligence":
        MessageLookupByLibrary.simpleMessage(
          "Transforme reuniões em inteligência acionável",
        ),
    "translate_note": MessageLookupByLibrary.simpleMessage("Traduzir Nota"),
    "translating_note": MessageLookupByLibrary.simpleMessage(
      "Traduzindo Nota...",
    ),
    "translation_completed": MessageLookupByLibrary.simpleMessage(
      "Tradução Concluída",
    ),
    "translation_failed": MessageLookupByLibrary.simpleMessage(
      "Falha na Tradução",
    ),
    "trouble_connecting_to_server": MessageLookupByLibrary.simpleMessage(
      "Estamos tendo problemas para conectar ao servidor. Por favor, tente novamente em um momento.",
    ),
    "try_3_day": MessageLookupByLibrary.simpleMessage("Teste 3 Dias Grátis"),
    "try_7_day": MessageLookupByLibrary.simpleMessage(
      "Iniciar Meu Teste de 7 Dias",
    ),
    "try_again": MessageLookupByLibrary.simpleMessage(
      "Houve um problema ao gerar suas notas. Tente novamente!",
    ),
    "try_again_button": MessageLookupByLibrary.simpleMessage(
      "Tentar Novamente",
    ),
    "try_pro_free_7_day": MessageLookupByLibrary.simpleMessage(
      "Experimente o Pro Grátis por 7 Dias",
    ),
    "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
      "Digite ou cole texto aqui. O AI o transformará em um resumo claro e estruturado com destaques.",
    ),
    "uidCopied": m3,
    "unable_download_file": MessageLookupByLibrary.simpleMessage(
      "Não foi possível baixar arquivo",
    ),
    "unable_load_audio": MessageLookupByLibrary.simpleMessage(
      "Não foi possível carregar áudio:",
    ),
    "unable_share_audio": MessageLookupByLibrary.simpleMessage(
      "Não foi possível compartilhar arquivo de áudio",
    ),
    "unable_to_connect_to_server": MessageLookupByLibrary.simpleMessage(
      "Certifique-se de que seu telefone esteja conectado à internet",
    ),
    "unable_to_extract_web_url": MessageLookupByLibrary.simpleMessage(
      "Não foi possível extrair conteúdo da URL web",
    ),
    "unable_to_open_store": MessageLookupByLibrary.simpleMessage(
      "Não foi possível abrir a loja",
    ),
    "uncover_opportunities": MessageLookupByLibrary.simpleMessage(
      "descubra oportunidades",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage(
      "O aplicativo encontrou um erro desconhecido",
    ),
    "unknown_server_error": MessageLookupByLibrary.simpleMessage(
      "Ops! Nossos servidores falharam. Tente novamente.",
    ),
    "unlimited_ai_chat": MessageLookupByLibrary.simpleMessage(
      "Chat de IA ilimitado, Mapa mental de IA, Flashcards, Quiz",
    ),
    "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
        MessageLookupByLibrary.simpleMessage(
          "Chat AI, Mapa Mental, Flashcard, Quiz Ilimitados",
        ),
    "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
      "Geração ilimitada de notas AI de todas as fontes (YouTube, Documentos, Gravação, Áudio)",
    ),
    "unlimited_ai_notes_from_youtube_and_document":
        MessageLookupByLibrary.simpleMessage(
          "Notas AI Ilimitadas de YouTube e Documentos",
        ),
    "unlimited_audio_youtube_website_to_ai_notes":
        MessageLookupByLibrary.simpleMessage(
          "Áudio, YouTube, Documentos e Sites em Notas AI Ilimitadas",
        ),
    "unlimited_everything": MessageLookupByLibrary.simpleMessage(
      "Experimente notas AI ilimitadas, serviço prioritário e recursos premium",
    ),
    "unlimited_youtube_document_ai_notes": MessageLookupByLibrary.simpleMessage(
      "Notas AI Ilimitadas de YouTube e Documentos",
    ),
    "unlock_all_features": MessageLookupByLibrary.simpleMessage(
      "Desbloquear todas as funcionalidades",
    ),
    "unlock_essential_life_time": MessageLookupByLibrary.simpleMessage(
      "Desbloqueie Essencial Vitalício",
    ),
    "unlock_lifetime_access": MessageLookupByLibrary.simpleMessage(
      "Desbloqueie Acesso Vitalício",
    ),
    "unlock_pro_lifetime": MessageLookupByLibrary.simpleMessage(
      "Desbloqueie PRO Vitalício",
    ),
    "unlock_the_most_ipad": MessageLookupByLibrary.simpleMessage(
      "Desbloqueie o assistente de notas AI mais poderoso",
    ),
    "unlock_the_most_powerful_ai_note_taking_assistant":
        MessageLookupByLibrary.simpleMessage(
          "Desbloqueie o assistente de notas AI mais poderoso",
        ),
    "unlock_toge": MessageLookupByLibrary.simpleMessage("DESBLOQUEAR JUNTOS"),
    "unlock_together": MessageLookupByLibrary.simpleMessage(
      "Desbloqueie Juntos",
    ),
    "unlock_unlimited_access_to_all_ai_features":
        MessageLookupByLibrary.simpleMessage(
          "Desbloqueie acesso ilimitado a todos os recursos de IA",
        ),
    "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage(
      "Desbloqueie experiência AI ilimitada",
    ),
    "unsynced_notes": MessageLookupByLibrary.simpleMessage(
      "Notas Não Sincronizadas",
    ),
    "update_available": MessageLookupByLibrary.simpleMessage(
      "Nova atualização disponível! Atualize para a versão mais recente para a melhor experiência.",
    ),
    "update_failed": MessageLookupByLibrary.simpleMessage(
      "Falha ao atualizar notas da comunidade. Tente novamente.",
    ),
    "update_later": MessageLookupByLibrary.simpleMessage("Mais tarde"),
    "update_now": MessageLookupByLibrary.simpleMessage("Atualizar Agora!"),
    "update_pro": MessageLookupByLibrary.simpleMessage(
      "Atualize para Experiência Pro",
    ),
    "update_to_pro": MessageLookupByLibrary.simpleMessage("Atualizar para PRO"),
    "upgrade": MessageLookupByLibrary.simpleMessage("ATUALIZAR"),
    "upgrade_now": MessageLookupByLibrary.simpleMessage("Atualizar Agora!"),
    "upgrade_plan": MessageLookupByLibrary.simpleMessage("Atualizar Plano"),
    "upgrade_to_full_pro_access": MessageLookupByLibrary.simpleMessage(
      "Atualizar para Acesso Pro Completo",
    ),
    "upgrade_to_pro_tier_at_a_special_price":
        MessageLookupByLibrary.simpleMessage(
          "Atualize para o Nível Pro por um Preço Especial",
        ),
    "upload": MessageLookupByLibrary.simpleMessage("Carregar"),
    "upload_audio": MessageLookupByLibrary.simpleMessage("Carregar Áudio"),
    "upload_audio_file": MessageLookupByLibrary.simpleMessage(
      "Carregar arquivo de áudio",
    ),
    "upload_file": MessageLookupByLibrary.simpleMessage("Carregar Arquivo"),
    "upload_image": MessageLookupByLibrary.simpleMessage("Carregar imagem"),
    "upload_in_progress": MessageLookupByLibrary.simpleMessage(
      "Upload em andamento. Mantenha a tela aberta.\nDesative VPN para upload mais rápido.",
    ),
    "uploading_to_server": MessageLookupByLibrary.simpleMessage(
      "Enviando para servidor seguro",
    ),
    "user_disabled": MessageLookupByLibrary.simpleMessage(
      "O usuário deste e-mail foi desativado.",
    ),
    "user_not_found": MessageLookupByLibrary.simpleMessage(
      "Informações do usuário não encontradas.",
    ),
    "verifying_your_credentials": MessageLookupByLibrary.simpleMessage(
      "Verificando suas credenciais",
    ),
    "video": MessageLookupByLibrary.simpleMessage("Vídeo"),
    "video_audio": MessageLookupByLibrary.simpleMessage(
      "Gravações, Vídeos e Docs em Notas AI",
    ),
    "video_captions": MessageLookupByLibrary.simpleMessage("Legendas de Vídeo"),
    "video_is_temporary": MessageLookupByLibrary.simpleMessage(
      "*Vídeo é temporário - Salve antes de fechar",
    ),
    "visualize_strategies": MessageLookupByLibrary.simpleMessage(
      "Visualize estratégias e",
    ),
    "visualize_strategies_opportunities": MessageLookupByLibrary.simpleMessage(
      "Visualize estratégias e descubra oportunidades",
    ),
    "visualize_strategies_uncover": MessageLookupByLibrary.simpleMessage(
      "Visualize estratégias e descubra",
    ),
    "visualize_strategies_uncover_opportunities":
        MessageLookupByLibrary.simpleMessage(
          "Visualize estratégias e descubra oportunidades",
        ),
    "voice": MessageLookupByLibrary.simpleMessage("Voz"),
    "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
        MessageLookupByLibrary.simpleMessage(
          "Aviso: Este app de notas AI pode causar produtividade excessiva! 🚀 Use meu código e ambos ganharemos uso extra. Código: ",
        ),
    "watch_sync_empty_message": MessageLookupByLibrary.simpleMessage(
      "As gravações do seu relógio Apple aparecerão aqui",
    ),
    "web": MessageLookupByLibrary.simpleMessage("Web"),
    "web_link": MessageLookupByLibrary.simpleMessage("Link Web"),
    "web_sync": MessageLookupByLibrary.simpleMessage("Sincronização Web"),
    "website_import": MessageLookupByLibrary.simpleMessage(
      "Importação de Site",
    ),
    "week": MessageLookupByLibrary.simpleMessage("semana"),
    "week_free_limit": MessageLookupByLibrary.simpleMessage(
      "Limite Semanal Grátis Atingido",
    ),
    "weekly": MessageLookupByLibrary.simpleMessage("Semanal"),
    "weekly_free_limit_reached": MessageLookupByLibrary.simpleMessage(
      "Limite Semanal Grátis Atingido",
    ),
    "weekly_free_limit_reached_details": MessageLookupByLibrary.simpleMessage(
      "Você usou todas as transcrições e resumos AI grátis esta semana! Atualize para Pro ou espere até a próxima semana.",
    ),
    "welcome_notex": MessageLookupByLibrary.simpleMessage(
      "Bem-vindo ao NoteX!",
    ),
    "welcome_title": MessageLookupByLibrary.simpleMessage(
      "Vamos criar sua\nprimeira nota AI",
    ),
    "what_improve": MessageLookupByLibrary.simpleMessage(
      "O que precisa melhorar",
    ),
    "whats_new": MessageLookupByLibrary.simpleMessage("Novidades"),
    "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
    "work_notes_projects": MessageLookupByLibrary.simpleMessage(
      "Notas e Projetos de Trabalho",
    ),
    "writing_style": MessageLookupByLibrary.simpleMessage("Estilo de Escrita"),
    "wrong": MessageLookupByLibrary.simpleMessage("Errado"),
    "x": MessageLookupByLibrary.simpleMessage("X"),
    "x_skip": MessageLookupByLibrary.simpleMessage("X?"),
    "year": MessageLookupByLibrary.simpleMessage("ano"),
    "yearly": MessageLookupByLibrary.simpleMessage("Anual"),
    "yes": MessageLookupByLibrary.simpleMessage("Sim"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Ontem"),
    "you_are_given_a_special_gift_today": MessageLookupByLibrary.simpleMessage(
      "Você recebeu um presente especial hoje 🎁",
    ),
    "you_are_pro": MessageLookupByLibrary.simpleMessage("Acesso PRO"),
    "you_can_update_setting": MessageLookupByLibrary.simpleMessage(
      "Você pode atualizar a qualquer momento nas Configurações.",
    ),
    "you_have_received": MessageLookupByLibrary.simpleMessage("Você recebeu"),
    "you_have_received2": MessageLookupByLibrary.simpleMessage(
      "Você terá uma chance de ganhar o NoteX Acesso Pro Vitalício! 3 vencedores sorteados no dia 30 de cada mês 🎁",
    ),
    "you_will_get_one_entry_to_win_noteX": MessageLookupByLibrary.simpleMessage(
      "Você terá uma chance de ganhar o NoteX",
    ),
    "you_will_not_be": MessageLookupByLibrary.simpleMessage(
      "Você não poderá recuperá-lo depois",
    ),
    "your_learning": MessageLookupByLibrary.simpleMessage(
      "Turbine Seu Aprendizado!",
    ),
    "your_learning_device": MessageLookupByLibrary.simpleMessage(
      "Acesso NoteX Pro",
    ),
    "your_note_are_ready": MessageLookupByLibrary.simpleMessage(
      "Suas notas estão prontas.",
    ),
    "your_personal_study": MessageLookupByLibrary.simpleMessage(
      "Seu estudo pessoal",
    ),
    "your_personal_study_assistant": MessageLookupByLibrary.simpleMessage(
      "Seu assistente de estudo pessoal",
    ),
    "your_plan": MessageLookupByLibrary.simpleMessage("Seu plano"),
    "your_primary": MessageLookupByLibrary.simpleMessage(
      "Qual é o seu principal",
    ),
    "your_product": MessageLookupByLibrary.simpleMessage("SUA PRODUTIVIDADE"),
    "your_recording_will_save": MessageLookupByLibrary.simpleMessage(
      "Suas gravações serão salvas localmente sem transcrição ou resumo AI. Remova limites para processar após concluir.",
    ),
    "your_referrals": MessageLookupByLibrary.simpleMessage("Suas Indicações"),
    "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
    "youtube_import": MessageLookupByLibrary.simpleMessage(
      "Importação do YouTube",
    ),
    "youtube_link": MessageLookupByLibrary.simpleMessage("Link do YouTube"),
    "youtube_transcript_language_guidance": MessageLookupByLibrary.simpleMessage(
      "Selecione o idioma da transcrição do YouTube - Este idioma será usado para gerar suas notas AI",
    ),
    "youtube_video": MessageLookupByLibrary.simpleMessage("Vídeo do YouTube"),
    "youtube_video_note": MessageLookupByLibrary.simpleMessage(
      "Vídeo do YouTube",
    ),
    "yt_credit_err": MessageLookupByLibrary.simpleMessage(
      "Uso grátis do YouTube insuficiente. Atualize seu plano.",
    ),
    "yt_credit_use_err": MessageLookupByLibrary.simpleMessage(
      "Erro ao usar crédito grátis do YouTube. Tente depois.",
    ),
    "yt_length_err": MessageLookupByLibrary.simpleMessage(
      "Vídeo do YouTube excede 10 horas. Escolha um mais curto.",
    ),
    "yt_process_err": MessageLookupByLibrary.simpleMessage(
      "Erro ao processar vídeo do YouTube. Verifique a URL e tente novamente.",
    ),
    "yt_sum_limit": MessageLookupByLibrary.simpleMessage(
      "Limite de Resumo do YouTube",
    ),
    "z_to_a": MessageLookupByLibrary.simpleMessage("Z a A"),
  };
}
