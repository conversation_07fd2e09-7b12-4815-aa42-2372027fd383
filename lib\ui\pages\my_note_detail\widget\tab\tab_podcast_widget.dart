import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:note_x/lib.dart';

class TabPodcastWidget extends StatelessWidget {
  const TabPodcastWidget({
    super.key,
    required this.noteModel,
  });

  final NoteModel noteModel;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().noteBox.listenable(),
      builder: (context, box, child) {
        final currentNote = box.get(noteModel.id) ?? noteModel;

        return EmptyPage(
          image: Assets.images.imgDetailPodcast,
          title: S.current.click_create_podcast,
          noteModel: currentNote,
          isLoading: currentNote.noteStatus == NoteStatus.loading,
          contentButton: Text(
            S.current.create_podcast,
            style: TextStyle(
              fontSize: context.isTablet ? 16 : 14.sp,
              fontWeight: context.isTablet ? FontWeight.w600 : FontWeight.w500,
              color: currentNote.noteStatus == NoteStatus.error ||
                      currentNote.noteStatus == NoteStatus.loading
                  ? context.colorScheme.mainPrimary.withOpacity(0.38)
                  : context.colorScheme.themeWhite,
            ),
          ),
          onTap: () {
            AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.podcast_scr_create_clicked);
            if (currentNote.noteStatus != NoteStatus.loading &&
                currentNote.noteStatus != NoteStatus.error) {
              CreateShortsBottomSheet.show(
                context,
                currentNote,
                CreateShortsType.audio,
              );
            }
          },
        );
      },
    );
  }
}
