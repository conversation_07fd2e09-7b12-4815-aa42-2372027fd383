// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$HomeState {
  HomePageCommunityNoteViewState get homePageCommunityNoteViewState =>
      throw _privateConstructorUsedError;
  HomePageAllNoteViewState get homePageAllNoteViewState =>
      throw _privateConstructorUsedError;
  HomePageFolderViewState get homePageFolderViewState =>
      throw _privateConstructorUsedError;
  int get selectedFilterIndex => throw _privateConstructorUsedError;
  int get selectedSortIndex => throw _privateConstructorUsedError;
  List<NoteModel> get filteredNotes => throw _privateConstructorUsedError;
  bool get isSyncing => throw _privateConstructorUsedError;
  bool get showEventOverlay => throw _privateConstructorUsedError;
  SharingTypeState get sharingTypeState => throw _privateConstructorUsedError;
  dynamic get currentSelectedTabBarIndex => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call(
      {HomePageCommunityNoteViewState homePageCommunityNoteViewState,
      HomePageAllNoteViewState homePageAllNoteViewState,
      HomePageFolderViewState homePageFolderViewState,
      int selectedFilterIndex,
      int selectedSortIndex,
      List<NoteModel> filteredNotes,
      bool isSyncing,
      bool showEventOverlay,
      SharingTypeState sharingTypeState,
      dynamic currentSelectedTabBarIndex});
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? homePageCommunityNoteViewState = null,
    Object? homePageAllNoteViewState = null,
    Object? homePageFolderViewState = null,
    Object? selectedFilterIndex = null,
    Object? selectedSortIndex = null,
    Object? filteredNotes = null,
    Object? isSyncing = null,
    Object? showEventOverlay = null,
    Object? sharingTypeState = null,
    Object? currentSelectedTabBarIndex = freezed,
  }) {
    return _then(_value.copyWith(
      homePageCommunityNoteViewState: null == homePageCommunityNoteViewState
          ? _value.homePageCommunityNoteViewState
          : homePageCommunityNoteViewState // ignore: cast_nullable_to_non_nullable
              as HomePageCommunityNoteViewState,
      homePageAllNoteViewState: null == homePageAllNoteViewState
          ? _value.homePageAllNoteViewState
          : homePageAllNoteViewState // ignore: cast_nullable_to_non_nullable
              as HomePageAllNoteViewState,
      homePageFolderViewState: null == homePageFolderViewState
          ? _value.homePageFolderViewState
          : homePageFolderViewState // ignore: cast_nullable_to_non_nullable
              as HomePageFolderViewState,
      selectedFilterIndex: null == selectedFilterIndex
          ? _value.selectedFilterIndex
          : selectedFilterIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedSortIndex: null == selectedSortIndex
          ? _value.selectedSortIndex
          : selectedSortIndex // ignore: cast_nullable_to_non_nullable
              as int,
      filteredNotes: null == filteredNotes
          ? _value.filteredNotes
          : filteredNotes // ignore: cast_nullable_to_non_nullable
              as List<NoteModel>,
      isSyncing: null == isSyncing
          ? _value.isSyncing
          : isSyncing // ignore: cast_nullable_to_non_nullable
              as bool,
      showEventOverlay: null == showEventOverlay
          ? _value.showEventOverlay
          : showEventOverlay // ignore: cast_nullable_to_non_nullable
              as bool,
      sharingTypeState: null == sharingTypeState
          ? _value.sharingTypeState
          : sharingTypeState // ignore: cast_nullable_to_non_nullable
              as SharingTypeState,
      currentSelectedTabBarIndex: freezed == currentSelectedTabBarIndex
          ? _value.currentSelectedTabBarIndex
          : currentSelectedTabBarIndex // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HomeStateImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeStateImplCopyWith(
          _$HomeStateImpl value, $Res Function(_$HomeStateImpl) then) =
      __$$HomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {HomePageCommunityNoteViewState homePageCommunityNoteViewState,
      HomePageAllNoteViewState homePageAllNoteViewState,
      HomePageFolderViewState homePageFolderViewState,
      int selectedFilterIndex,
      int selectedSortIndex,
      List<NoteModel> filteredNotes,
      bool isSyncing,
      bool showEventOverlay,
      SharingTypeState sharingTypeState,
      dynamic currentSelectedTabBarIndex});
}

/// @nodoc
class __$$HomeStateImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeStateImpl>
    implements _$$HomeStateImplCopyWith<$Res> {
  __$$HomeStateImplCopyWithImpl(
      _$HomeStateImpl _value, $Res Function(_$HomeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? homePageCommunityNoteViewState = null,
    Object? homePageAllNoteViewState = null,
    Object? homePageFolderViewState = null,
    Object? selectedFilterIndex = null,
    Object? selectedSortIndex = null,
    Object? filteredNotes = null,
    Object? isSyncing = null,
    Object? showEventOverlay = null,
    Object? sharingTypeState = null,
    Object? currentSelectedTabBarIndex = freezed,
  }) {
    return _then(_$HomeStateImpl(
      homePageCommunityNoteViewState: null == homePageCommunityNoteViewState
          ? _value.homePageCommunityNoteViewState
          : homePageCommunityNoteViewState // ignore: cast_nullable_to_non_nullable
              as HomePageCommunityNoteViewState,
      homePageAllNoteViewState: null == homePageAllNoteViewState
          ? _value.homePageAllNoteViewState
          : homePageAllNoteViewState // ignore: cast_nullable_to_non_nullable
              as HomePageAllNoteViewState,
      homePageFolderViewState: null == homePageFolderViewState
          ? _value.homePageFolderViewState
          : homePageFolderViewState // ignore: cast_nullable_to_non_nullable
              as HomePageFolderViewState,
      selectedFilterIndex: null == selectedFilterIndex
          ? _value.selectedFilterIndex
          : selectedFilterIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedSortIndex: null == selectedSortIndex
          ? _value.selectedSortIndex
          : selectedSortIndex // ignore: cast_nullable_to_non_nullable
              as int,
      filteredNotes: null == filteredNotes
          ? _value._filteredNotes
          : filteredNotes // ignore: cast_nullable_to_non_nullable
              as List<NoteModel>,
      isSyncing: null == isSyncing
          ? _value.isSyncing
          : isSyncing // ignore: cast_nullable_to_non_nullable
              as bool,
      showEventOverlay: null == showEventOverlay
          ? _value.showEventOverlay
          : showEventOverlay // ignore: cast_nullable_to_non_nullable
              as bool,
      sharingTypeState: null == sharingTypeState
          ? _value.sharingTypeState
          : sharingTypeState // ignore: cast_nullable_to_non_nullable
              as SharingTypeState,
      currentSelectedTabBarIndex: freezed == currentSelectedTabBarIndex
          ? _value.currentSelectedTabBarIndex!
          : currentSelectedTabBarIndex,
    ));
  }
}

/// @nodoc

class _$HomeStateImpl implements _HomeState {
  const _$HomeStateImpl(
      {this.homePageCommunityNoteViewState =
          HomePageCommunityNoteViewState.initial,
      this.homePageAllNoteViewState = HomePageAllNoteViewState.initial,
      this.homePageFolderViewState = HomePageFolderViewState.initial,
      this.selectedFilterIndex = 0,
      this.selectedSortIndex = 0,
      final List<NoteModel> filteredNotes = const [],
      this.isSyncing = false,
      this.showEventOverlay = true,
      this.sharingTypeState = SharingTypeState.initial,
      this.currentSelectedTabBarIndex = 0})
      : _filteredNotes = filteredNotes;

  @override
  @JsonKey()
  final HomePageCommunityNoteViewState homePageCommunityNoteViewState;
  @override
  @JsonKey()
  final HomePageAllNoteViewState homePageAllNoteViewState;
  @override
  @JsonKey()
  final HomePageFolderViewState homePageFolderViewState;
  @override
  @JsonKey()
  final int selectedFilterIndex;
  @override
  @JsonKey()
  final int selectedSortIndex;
  final List<NoteModel> _filteredNotes;
  @override
  @JsonKey()
  List<NoteModel> get filteredNotes {
    if (_filteredNotes is EqualUnmodifiableListView) return _filteredNotes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredNotes);
  }

  @override
  @JsonKey()
  final bool isSyncing;
  @override
  @JsonKey()
  final bool showEventOverlay;
  @override
  @JsonKey()
  final SharingTypeState sharingTypeState;
  @override
  @JsonKey()
  final dynamic currentSelectedTabBarIndex;

  @override
  String toString() {
    return 'HomeState(homePageCommunityNoteViewState: $homePageCommunityNoteViewState, homePageAllNoteViewState: $homePageAllNoteViewState, homePageFolderViewState: $homePageFolderViewState, selectedFilterIndex: $selectedFilterIndex, selectedSortIndex: $selectedSortIndex, filteredNotes: $filteredNotes, isSyncing: $isSyncing, showEventOverlay: $showEventOverlay, sharingTypeState: $sharingTypeState, currentSelectedTabBarIndex: $currentSelectedTabBarIndex)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeStateImpl &&
            (identical(other.homePageCommunityNoteViewState,
                    homePageCommunityNoteViewState) ||
                other.homePageCommunityNoteViewState ==
                    homePageCommunityNoteViewState) &&
            (identical(
                    other.homePageAllNoteViewState, homePageAllNoteViewState) ||
                other.homePageAllNoteViewState == homePageAllNoteViewState) &&
            (identical(
                    other.homePageFolderViewState, homePageFolderViewState) ||
                other.homePageFolderViewState == homePageFolderViewState) &&
            (identical(other.selectedFilterIndex, selectedFilterIndex) ||
                other.selectedFilterIndex == selectedFilterIndex) &&
            (identical(other.selectedSortIndex, selectedSortIndex) ||
                other.selectedSortIndex == selectedSortIndex) &&
            const DeepCollectionEquality()
                .equals(other._filteredNotes, _filteredNotes) &&
            (identical(other.isSyncing, isSyncing) ||
                other.isSyncing == isSyncing) &&
            (identical(other.showEventOverlay, showEventOverlay) ||
                other.showEventOverlay == showEventOverlay) &&
            (identical(other.sharingTypeState, sharingTypeState) ||
                other.sharingTypeState == sharingTypeState) &&
            const DeepCollectionEquality().equals(
                other.currentSelectedTabBarIndex, currentSelectedTabBarIndex));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      homePageCommunityNoteViewState,
      homePageAllNoteViewState,
      homePageFolderViewState,
      selectedFilterIndex,
      selectedSortIndex,
      const DeepCollectionEquality().hash(_filteredNotes),
      isSyncing,
      showEventOverlay,
      sharingTypeState,
      const DeepCollectionEquality().hash(currentSelectedTabBarIndex));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      __$$HomeStateImplCopyWithImpl<_$HomeStateImpl>(this, _$identity);
}

abstract class _HomeState implements HomeState {
  const factory _HomeState(
      {final HomePageCommunityNoteViewState homePageCommunityNoteViewState,
      final HomePageAllNoteViewState homePageAllNoteViewState,
      final HomePageFolderViewState homePageFolderViewState,
      final int selectedFilterIndex,
      final int selectedSortIndex,
      final List<NoteModel> filteredNotes,
      final bool isSyncing,
      final bool showEventOverlay,
      final SharingTypeState sharingTypeState,
      final dynamic currentSelectedTabBarIndex}) = _$HomeStateImpl;

  @override
  HomePageCommunityNoteViewState get homePageCommunityNoteViewState;
  @override
  HomePageAllNoteViewState get homePageAllNoteViewState;
  @override
  HomePageFolderViewState get homePageFolderViewState;
  @override
  int get selectedFilterIndex;
  @override
  int get selectedSortIndex;
  @override
  List<NoteModel> get filteredNotes;
  @override
  bool get isSyncing;
  @override
  bool get showEventOverlay;
  @override
  SharingTypeState get sharingTypeState;
  @override
  dynamic get currentSelectedTabBarIndex;
  @override
  @JsonKey(ignore: true)
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
