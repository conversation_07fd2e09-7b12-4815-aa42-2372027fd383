// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'package_product_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PackageProductDto _$PackageProductDtoFromJson(Map<String, dynamic> json) {
  return _PackageProductDto.fromJson(json);
}

/// @nodoc
mixin _$PackageProductDto {
  String get id => throw _privateConstructorUsedError;
  String get namePackage => throw _privateConstructorUsedError;
  String get price => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError;
  int get discountPercent => throw _privateConstructorUsedError;
  double get priceNumber => throw _privateConstructorUsedError;
  String get currencyCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PackageProductDtoCopyWith<PackageProductDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PackageProductDtoCopyWith<$Res> {
  factory $PackageProductDtoCopyWith(
          PackageProductDto value, $Res Function(PackageProductDto) then) =
      _$PackageProductDtoCopyWithImpl<$Res, PackageProductDto>;
  @useResult
  $Res call(
      {String id,
      String namePackage,
      String price,
      String title,
      String description,
      bool isSelected,
      int discountPercent,
      double priceNumber,
      String currencyCode});
}

/// @nodoc
class _$PackageProductDtoCopyWithImpl<$Res, $Val extends PackageProductDto>
    implements $PackageProductDtoCopyWith<$Res> {
  _$PackageProductDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? namePackage = null,
    Object? price = null,
    Object? title = null,
    Object? description = null,
    Object? isSelected = null,
    Object? discountPercent = null,
    Object? priceNumber = null,
    Object? currencyCode = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      namePackage: null == namePackage
          ? _value.namePackage
          : namePackage // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      discountPercent: null == discountPercent
          ? _value.discountPercent
          : discountPercent // ignore: cast_nullable_to_non_nullable
              as int,
      priceNumber: null == priceNumber
          ? _value.priceNumber
          : priceNumber // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PackageProductDtoImplCopyWith<$Res>
    implements $PackageProductDtoCopyWith<$Res> {
  factory _$$PackageProductDtoImplCopyWith(_$PackageProductDtoImpl value,
          $Res Function(_$PackageProductDtoImpl) then) =
      __$$PackageProductDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String namePackage,
      String price,
      String title,
      String description,
      bool isSelected,
      int discountPercent,
      double priceNumber,
      String currencyCode});
}

/// @nodoc
class __$$PackageProductDtoImplCopyWithImpl<$Res>
    extends _$PackageProductDtoCopyWithImpl<$Res, _$PackageProductDtoImpl>
    implements _$$PackageProductDtoImplCopyWith<$Res> {
  __$$PackageProductDtoImplCopyWithImpl(_$PackageProductDtoImpl _value,
      $Res Function(_$PackageProductDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? namePackage = null,
    Object? price = null,
    Object? title = null,
    Object? description = null,
    Object? isSelected = null,
    Object? discountPercent = null,
    Object? priceNumber = null,
    Object? currencyCode = null,
  }) {
    return _then(_$PackageProductDtoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      namePackage: null == namePackage
          ? _value.namePackage
          : namePackage // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      discountPercent: null == discountPercent
          ? _value.discountPercent
          : discountPercent // ignore: cast_nullable_to_non_nullable
              as int,
      priceNumber: null == priceNumber
          ? _value.priceNumber
          : priceNumber // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PackageProductDtoImpl implements _PackageProductDto {
  const _$PackageProductDtoImpl(
      {required this.id,
      required this.namePackage,
      required this.price,
      required this.title,
      required this.description,
      required this.isSelected,
      required this.discountPercent,
      required this.priceNumber,
      required this.currencyCode});

  factory _$PackageProductDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PackageProductDtoImplFromJson(json);

  @override
  final String id;
  @override
  final String namePackage;
  @override
  final String price;
  @override
  final String title;
  @override
  final String description;
  @override
  final bool isSelected;
  @override
  final int discountPercent;
  @override
  final double priceNumber;
  @override
  final String currencyCode;

  @override
  String toString() {
    return 'PackageProductDto(id: $id, namePackage: $namePackage, price: $price, title: $title, description: $description, isSelected: $isSelected, discountPercent: $discountPercent, priceNumber: $priceNumber, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PackageProductDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.namePackage, namePackage) ||
                other.namePackage == namePackage) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            (identical(other.discountPercent, discountPercent) ||
                other.discountPercent == discountPercent) &&
            (identical(other.priceNumber, priceNumber) ||
                other.priceNumber == priceNumber) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, namePackage, price, title,
      description, isSelected, discountPercent, priceNumber, currencyCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PackageProductDtoImplCopyWith<_$PackageProductDtoImpl> get copyWith =>
      __$$PackageProductDtoImplCopyWithImpl<_$PackageProductDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PackageProductDtoImplToJson(
      this,
    );
  }
}

abstract class _PackageProductDto implements PackageProductDto {
  const factory _PackageProductDto(
      {required final String id,
      required final String namePackage,
      required final String price,
      required final String title,
      required final String description,
      required final bool isSelected,
      required final int discountPercent,
      required final double priceNumber,
      required final String currencyCode}) = _$PackageProductDtoImpl;

  factory _PackageProductDto.fromJson(Map<String, dynamic> json) =
      _$PackageProductDtoImpl.fromJson;

  @override
  String get id;
  @override
  String get namePackage;
  @override
  String get price;
  @override
  String get title;
  @override
  String get description;
  @override
  bool get isSelected;
  @override
  int get discountPercent;
  @override
  double get priceNumber;
  @override
  String get currencyCode;
  @override
  @JsonKey(ignore: true)
  _$$PackageProductDtoImplCopyWith<_$PackageProductDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
