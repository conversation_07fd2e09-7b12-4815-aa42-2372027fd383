// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flash_card_detail_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FlashCarDetailDtoImpl _$$FlashCarDetailDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$FlashCarDetailDtoImpl(
      question: json['question'] as String? ?? '',
      answer: json['answer'] as String? ?? '',
      difficulty: json['difficulty'] as String? ?? '',
      context: json['context'] as String? ?? '',
      transcriptJson: (json['transcript_json'] as List<dynamic>?)
              ?.map((e) => TranscriptDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$FlashCarDetailDtoImplToJson(
        _$FlashCarDetailDtoImpl instance) =>
    <String, dynamic>{
      'question': instance.question,
      'answer': instance.answer,
      'difficulty': instance.difficulty,
      'context': instance.context,
      'transcript_json': instance.transcriptJson,
    };
