import 'package:camera/camera.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';

part 'camera_state.freezed.dart';

@freezed
class CameraState extends BaseState with _$CameraState {
  const factory CameraState({
    @Default(CreateNoteWithCameraOneShotEvent.none)
    CreateNoteWithCameraOneShotEvent oneShotEvent,
    @Default([]) List<String> selectedImagePaths,
    @Default(false) bool hasSelectedImages,
    @Default(false) bool isMergingImages,
    @Default('') String summaryStyle,
    @Default('') String writingStyle,
    @Default('') String additionalInstructions,
    @Default(false) bool isCameraInitialized,
    @Default(false) bool isCameraActive,
    @Default(FlashMode.off) FlashMode flashMode,
  }) = _CameraState;

  factory CameraState.initial() => const CameraState();
}

enum CreateNoteWithCameraOneShotEvent {
  none,
  createNoteSuccessfully,
  onShowIAPFromCamera,
  maxImagesReached,
  mergePdfCompleted,
  mergePdfFailed,
  cameraInitialized,
  cameraInitFailed,
}
