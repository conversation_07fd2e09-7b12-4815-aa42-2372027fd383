import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/slide_show/cubit/slide_show_cubit_state.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:pdfx/pdfx.dart';

enum ViewMode { horizontal, vertical, grid }

class SlideShowPage extends StatefulWidget {
  final NoteModel note;
  const SlideShowPage({Key? key, required this.note}) : super(key: key);

  @override
  State<SlideShowPage> createState() => _SlideShowPageState();
}

class _SlideShowPageState
    extends BasePageStateDelegate<SlideShowPage, SlideShowCubit> {
  String? _localPdfPath;
  final ValueNotifier<int> _downloadProgress = ValueNotifier(0);

  PdfController? _horizontalController;
  PdfDocument? _verticalDocument;
  final ScrollController _verticalScrollController = ScrollController();
  final ScrollController _previewScrollController = ScrollController();
  final ScrollController _gridScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _downloadPdf(widget.note.slidePdfUrl);
    _verticalScrollController.addListener(_onVerticalScroll);
  }

  @override
  void dispose() {
    _downloadProgress.dispose();
    _previewScrollController.dispose();
    _verticalScrollController.dispose();
    _gridScrollController.dispose();
    _horizontalController?.dispose();
    _verticalDocument?.close();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<SlideShowCubit, SlideShowCubitState>(
      buildWhen: (previous, current) => previous.isLoading != current.isLoading,
      builder: (context, state) {
        if (state.isLoading) {
          return LoadingView(downloadProgress: _downloadProgress);
        }
        if (_localPdfPath == null || _horizontalController == null) {
          return ErrorView(
            onRetry: () {
              AnalyticsService.logAnalyticsEventNoParam(
                eventName: EventName.slide_show_retry_slide_show_clicked,
              );
              _downloadPdf(widget.note.slidePdfUrl);
            },
          );
        }
        return Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  BlocBuilder<SlideShowCubit, SlideShowCubitState>(
                    buildWhen: (previous, current) =>
                        previous.viewMode != current.viewMode ||
                        previous.horizontalTotalPages !=
                            current.horizontalTotalPages ||
                        previous.verticalTotalPages !=
                            current.verticalTotalPages,
                    builder: (context, state) {
                      switch (state.viewMode) {
                        case ViewMode.horizontal:
                          return PdfView(
                            controller: _horizontalController!,
                            scrollDirection: Axis.horizontal,
                            builders: PdfViewBuilders<DefaultBuilderOptions>(
                              options: const DefaultBuilderOptions(),
                              pageBuilder:
                                  (context, pageImage, index, document) {
                                return PhotoViewGalleryPageOptions.customChild(
                                  basePosition: Alignment.bottomCenter,
                                  childSize: Size(
                                    MediaQuery.of(context).size.width,
                                    (MediaQuery.of(context).size.width) *
                                        9 /
                                        16,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                      vertical: 12.0,
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(16),
                                      child: Image(
                                        image: PdfPageImageProvider(
                                          pageImage,
                                          index,
                                          document.id,
                                        ),
                                        fit: BoxFit.cover,
                                        alignment: Alignment.topCenter,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                            onDocumentLoaded: (doc) {
                              if (state.horizontalTotalPages == 0) {
                                cubit.setHorizontalTotalPages(
                                    _horizontalController!.pagesCount ?? 0);
                              }
                              cubit.setHorizontalCurrentPage(1);
                              cubit.setPdfLoaded(true);
                            },
                            onPageChanged: (page) {
                              cubit.setHorizontalCurrentPage(page);
                              _scrollToPreview(page - 1);
                            },
                          );
                        case ViewMode.vertical:
                          return PdfVerticalPreview(
                            pdfPath: _localPdfPath!,
                            onPageChanged: (page) {
                              cubit.setVerticalCurrentPage(page);
                            },
                            scrollController: _verticalScrollController,
                          );
                        case ViewMode.grid:
                          return PdfGridPreview(
                            pdfPath: _localPdfPath!,
                            totalPages: state.verticalTotalPages,
                            currentPage: state.verticalCurrentPage,
                            onPageTap: (page) {
                              // _horizontalController?.jumpToPage(page);
                            },
                          );
                      }
                    },
                  ),
                  // Nút share và chuyển đổi chế độ xem
                  BlocBuilder<SlideShowCubit, SlideShowCubitState>(
                    buildWhen: (previous, current) =>
                        previous.viewMode != current.viewMode ||
                        previous.isPdfLoaded != current.isPdfLoaded,
                    builder: (context, state) {
                      return Positioned(
                        top: 20,
                        right: 16,
                        child: Container(
                          decoration: BoxDecoration(
                            color: context.colorScheme.mainNeutral,
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                padding: EdgeInsets.zero,
                                icon: SvgPicture.asset(
                                  state.viewMode == ViewMode.horizontal
                                      ? Assets.icons.icScrollVertical
                                      : state.viewMode == ViewMode.vertical
                                          ? Assets.icons.icScrollGrid
                                          : Assets.icons.icScrollHorizontal,
                                  colorFilter: ColorFilter.mode(
                                    context.colorScheme.mainPrimary,
                                    BlendMode.srcIn,
                                  ),
                                ),
                                onPressed: cubit.state.isPdfLoaded
                                    ? _toggleViewMode
                                    : null,
                              ),
                              Container(
                                height: 0.5,
                                width: 20,
                                decoration: BoxDecoration(
                                  color: context.colorScheme.mainPrimary
                                      .withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              IconButton(
                                padding: EdgeInsets.zero,
                                icon: SvgPicture.asset(
                                  Assets.icons.icDowloadSlide,
                                  width: 20,
                                  height: 20,
                                  colorFilter: ColorFilter.mode(
                                    context.colorScheme.mainPrimary,
                                    BlendMode.srcIn,
                                  ),
                                ),
                                onPressed: () {
                                  AnalyticsService.logAnalyticsEventNoParam(
                                    eventName: EventName
                                        .slide_show_save_slide_show_clicked,
                                  );
                                  _showModalBottomSheetExportSlide(
                                      context, widget.note);
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  BlocBuilder<SlideShowCubit, SlideShowCubitState>(
                    buildWhen: (previous, current) =>
                        previous.viewMode != current.viewMode ||
                        previous.horizontalCurrentPage !=
                            current.horizontalCurrentPage ||
                        previous.verticalCurrentPage !=
                            current.verticalCurrentPage ||
                        previous.verticalTotalPages !=
                            current.verticalTotalPages,
                    builder: (context, state) {
                      if (state.viewMode != ViewMode.grid &&
                          state.verticalTotalPages > 0) {
                        return Positioned(
                          top: 20,
                          left: 16,
                          child: PageIndicator(
                            currentPage: state.viewMode == ViewMode.horizontal
                                ? state.horizontalCurrentPage
                                : state.verticalCurrentPage,
                            totalPages: state.verticalTotalPages,
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
            BlocBuilder<SlideShowCubit, SlideShowCubitState>(
              buildWhen: (previous, current) =>
                  previous.viewMode != current.viewMode ||
                  previous.horizontalCurrentPage !=
                      current.horizontalCurrentPage ||
                  previous.verticalCurrentPage != current.verticalCurrentPage ||
                  previous.verticalTotalPages != current.verticalTotalPages,
              builder: (context, state) {
                if (state.verticalTotalPages > 0 &&
                    state.viewMode == ViewMode.horizontal) {
                  return Column(
                    children: [
                      context.isTablet
                          ? AppConstants.kSpacingItem40
                          : AppConstants.kSpacingItem60,
                      PreviewBar(
                        localPdfPath: _localPdfPath!,
                        totalPages: state.verticalTotalPages,
                        currentPage: state.horizontalCurrentPage,
                        onPageTap: (page) {
                          if (!cubit.state.isPdfLoaded) return;
                          _horizontalController?.jumpToPage(page);
                        },
                        previewScrollController: _previewScrollController,
                      ),
                      context.isTablet
                          ? AppConstants.kSpacingItem130
                          : AppConstants.kSpacingItem150,
                    ],
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _downloadPdf(String url) async {
    const fileName = 'slide_generated_by_NoteXAI.pdf';
    if (!mounted) return;
    cubit.setLoading(true);
    _downloadProgress.value = 0;
    cubit.setPdfLoaded(false);
    _horizontalController?.dispose();
    _horizontalController = null;
    _verticalDocument = null;

    final dio = Dio();
    try {
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/$fileName';
      final response = await dio.download(
        url,
        filePath,
        options: Options(
            responseType: ResponseType.bytes,
            followRedirects: false,
            validateStatus: (status) {
              return status != null && status < 500;
            }),
        onReceiveProgress: (received, total) {
          if (!mounted) return;
          if (total != -1) {
            final percent = (received / total * 100).toInt();
            _downloadProgress.value = percent;
          }
        },
      );
      if (!mounted) return;
      if (response.statusCode == 200) {
        _localPdfPath = filePath;

        // Create controllers
        _horizontalController = PdfController(
          document: PdfDocument.openFile(_localPdfPath!),
        );
        _verticalDocument = await PdfDocument.openFile(_localPdfPath!);
        cubit.setVerticalTotalPages(_verticalDocument?.pagesCount ?? 0);
      } else {
        _localPdfPath = null;
      }
      cubit.setLoading(false);
    } catch (e) {
      if (!mounted) return;
      cubit.setLoading(false);
    }
  }

  void _showModalBottomSheetExportSlide(
    BuildContext context,
    NoteModel note,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.colorScheme.mainNeutral,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      builder: (BuildContext context) {
        return ExportSlideBottomSheet(
          note: note,
          localPdfPath: _localPdfPath,
          onSharePDF: () async {
            final screenSize = MediaQuery.of(context).size;
            final sharePosition = Rect.fromCenter(
              center: Offset(screenSize.width / 2, screenSize.height / 2),
              width: 100,
              height: 100,
            );
            if (_localPdfPath != null) {
              await ShareService().shareFile(
                filePath: _localPdfPath!,
                sharePositionOrigin: sharePosition,
                eventName: EventName.slide_show_save_pdf_success,
              );
            } else {
              await ShareService().shareLink(
                link: widget.note.slideUrl,
                sharePositionOrigin: sharePosition,
                eventName: EventName.slide_show_save_pdf_success,
              );
            }
          },
          onSharePPTX: () async {
            final screenSize = MediaQuery.of(context).size;
            final sharePosition = Rect.fromCenter(
              center: Offset(screenSize.width / 2, screenSize.height / 2),
              width: 100,
              height: 100,
            );
            try {
              final dio = Dio();
              final tempDir = await getTemporaryDirectory();
              const fileName = 'slide_generated_by_NoteXAI.pptx';
              final filePath = '${tempDir.path}/$fileName';
              final response = await dio.download(
                widget.note.slideUrl,
                filePath,
                options: Options(
                    responseType: ResponseType.bytes,
                    followRedirects: false,
                    validateStatus: (status) {
                      return status != null && status < 500;
                    }),
              );
              if (!mounted) return;
              if (response.statusCode == 200) {
                await ShareService().shareFile(
                  filePath: filePath,
                  sharePositionOrigin: sharePosition,
                  eventName: EventName.slide_show_save_ppt_success,
                );

                final file = File(filePath);
                if (await file.exists()) {
                  await file.delete();
                }
              } else {
                CommonDialogs.showToast(
                  S.current.export_failed,
                );
              }
            } catch (e) {
              debugPrint('Failed to export PPTX: $e');
              CommonDialogs.showToast(
                S.current.export_failed,
              );
            }
          },
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
    );
  }

  void _scrollToPreview(int pageIndex) {
    if (!_previewScrollController.hasClients) return;
    const itemWidth = 150;
    final screenWidth = MediaQuery.of(context).size.width;
    final totalItems = cubit.state.verticalTotalPages;
    final totalContentWidth = totalItems * itemWidth;
    if (totalContentWidth <= screenWidth) {
      _previewScrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return;
    }
    final offset = (pageIndex + 0.5) * itemWidth - (screenWidth / 2);
    final maxScroll = _previewScrollController.position.maxScrollExtent;
    final targetOffset = offset.clamp(0.0, maxScroll);

    _previewScrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _toggleViewMode() {
    if (!mounted || !cubit.state.isPdfLoaded) return;

    cubit.setViewMode(
      switch (cubit.state.viewMode) {
        ViewMode.horizontal => ViewMode.vertical,
        ViewMode.vertical => ViewMode.grid,
        ViewMode.grid => ViewMode.horizontal,
      },
    );
    switch (cubit.state.viewMode) {
      case ViewMode.horizontal:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.slide_show_horizontal_slide_clicked,
        );
      case ViewMode.vertical:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.slide_show_vertical_slide_clicked,
        );
      case ViewMode.grid:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.slide_show_show_grid_slide_clicked,
        );
    }
  }

  void _onVerticalScroll() {
    const pageHeight = 200.0 + 16.0;
    final currentPage =
        (_verticalScrollController.offset / pageHeight).round() + 1;
    if (cubit.state.verticalCurrentPage != currentPage) {
      cubit.setVerticalCurrentPage(
          currentPage.clamp(1, cubit.state.verticalTotalPages));
    }
  }
}
