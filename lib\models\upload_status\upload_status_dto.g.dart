// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_status_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UploadStatusDtoImpl _$$UploadStatusDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$UploadStatusDtoImpl(
      uploadId: json['upload_id'] as String? ?? '',
      fileType: json['file_type'] as String? ?? '',
      totalChunks: (json['total_chunks'] as num?)?.toInt() ?? 0,
      uploadedChunks: (json['uploaded_chunks'] as num?)?.toInt() ?? 0,
      uploadedChunkNumbers: (json['uploaded_chunk_numbers'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      isCompleted: json['is_completed'] as bool? ?? false,
      createdAt: (json['created_at'] as num?)?.toDouble() ?? 0.0,
      missingChunks: (json['missing_chunks'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$UploadStatusDtoImplToJson(
        _$UploadStatusDtoImpl instance) =>
    <String, dynamic>{
      'upload_id': instance.uploadId,
      'file_type': instance.fileType,
      'total_chunks': instance.totalChunks,
      'uploaded_chunks': instance.uploadedChunks,
      'uploaded_chunk_numbers': instance.uploadedChunkNumbers,
      'is_completed': instance.isCompleted,
      'created_at': instance.createdAt,
      'missing_chunks': instance.missingChunks,
    };
