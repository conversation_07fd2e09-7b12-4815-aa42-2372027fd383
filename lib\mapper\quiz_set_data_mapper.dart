import 'package:note_x/base/base.dart';
import 'base_data_mapper.dart';

class QuizSetDataMapper
    extends BaseDataMapper<QuizSetDataDto, QuizSetDataModel> {
  @override
  QuizSetDataModel mapToEntity(QuizSetDataDto data) {
    return QuizSetDataModel(
      setId: data.setId,
      name: data.name,
      difficulty: data.difficulty,
      questionsCount: data.questionsCount,
    );
  }

  @override
  QuizSetDataDto mapToDto(QuizSetDataModel entity) {
    return QuizSetDataDto(
      setId: entity.setId,
      name: entity.name,
      difficulty: entity.difficulty,
      questionsCount: entity.questionsCount,
    );
  }
}
