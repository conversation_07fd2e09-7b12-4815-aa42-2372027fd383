import 'package:note_x/base/base.dart';
import 'base_data_mapper.dart';

class ChatMessageDataMapper
    extends BaseDataMapper<ChatMessageDto, ChatMessageModel> {
  @override
  ChatMessageModel mapToEntity(ChatMessageDto data) {
    return ChatMessageModel(
      id: data.id,
      noteId: data.noteId,
      userId: data.userId,
      title: data.title,
      answer: data.answer,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    );
  }

  @override
  ChatMessageDto mapToDto(ChatMessageModel entity) {
    return ChatMessageDto(
      id: entity.id,
      noteId: entity.noteId,
      userId: entity.userId,
      title: entity.title,
      answer: entity.answer,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
