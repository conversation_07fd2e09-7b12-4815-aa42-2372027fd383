import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/document/cubit/cubit/document_cubit_state.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:pdfx/pdfx.dart';

class DocumentPage extends StatefulWidget {
  final NoteModel note;
  final bool isPdfFile;
  const DocumentPage({
    Key? key,
    required this.note,
    this.isPdfFile = true,
  }) : super(key: key);

  @override
  State<DocumentPage> createState() => _DocumentPageState();
}

class _DocumentPageState
    extends BasePageStateDelegate<DocumentPage, DocumentCubit> {
  String? _localPdfPath;
  final ValueNotifier<int> _downloadProgress = ValueNotifier(0);

  PdfControllerPinch? _pdfControllerPinch;
  @override
  void initState() {
    super.initState();
    if (widget.isPdfFile) {
      _downloadPdf(widget.note.documentUrl);
    }
  }

  @override
  void dispose() {
    _downloadProgress.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 60),
      child: !widget.isPdfFile
          ? CustomWebView(
              url: widget.note.documentUrl,
            )
          : BlocBuilder<DocumentCubit, DocumentCubitState>(
              buildWhen: (previous, current) =>
                  previous.isLoading != current.isLoading,
              builder: (context, state) {
                if (state.isLoading) {
                  return LoadingView(downloadProgress: _downloadProgress);
                }
                if (_localPdfPath == null) {
                  return ErrorView(
                      onRetry: () => _downloadPdf(widget.note.slidePdfUrl));
                }
                return Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: PdfViewPinch(
                                padding: 0,
                                controller: _pdfControllerPinch!,
                              ),
                            ),
                          ),
                          BlocBuilder<DocumentCubit, DocumentCubitState>(
                            buildWhen: (previous, current) =>
                                previous.isPdfLoaded != current.isPdfLoaded,
                            builder: (context, state) {
                              return Positioned(
                                top: 8,
                                right: 16,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: context.colorScheme.mainNeutral,
                                    borderRadius: BorderRadius.circular(24),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        padding: EdgeInsets.zero,
                                        icon: SvgPicture.asset(
                                          Assets.icons.icDowloadSlide,
                                          width: 20,
                                          height: 20,
                                          colorFilter: ColorFilter.mode(
                                            context.colorScheme.mainPrimary,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                        onPressed: () {
                                          _saveFilePdf(context);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
    );
  }

  Future<void> _downloadPdf(String url) async {
    const fileName = 'NoteXPDF.pdf';
    if (!mounted) return;
    cubit.setLoading(true);
    _downloadProgress.value = 0;
    cubit.setPdfLoaded(false);
    final dio = Dio();
    try {
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/$fileName';
      final response = await dio.download(
        url,
        filePath,
        options: Options(
            responseType: ResponseType.bytes,
            followRedirects: false,
            validateStatus: (status) {
              return status != null && status < 500;
            }),
        onReceiveProgress: (received, total) {
          if (!mounted) return;
          if (total != -1) {
            final percent = (received / total * 100).toInt();
            _downloadProgress.value = percent;
          }
        },
      );
      if (!mounted) return;
      if (response.statusCode == 200) {
        _localPdfPath = filePath;
        _pdfControllerPinch = PdfControllerPinch(
          document: PdfDocument.openFile(_localPdfPath!),
        );
      } else {
        _localPdfPath = null;
      }
      cubit.setLoading(false);
    } catch (e) {
      if (!mounted) return;
      cubit.setLoading(false);
    }
  }

  void _saveFilePdf(
    BuildContext context,
  ) async {
    final screenSize = MediaQuery.of(context).size;
    final sharePosition = Rect.fromCenter(
      center: Offset(screenSize.width / 2, screenSize.height / 2),
      width: 100,
      height: 100,
    );
    if (_localPdfPath != null) {
      await ShareService().shareFile(
        filePath: _localPdfPath!,
        sharePositionOrigin: sharePosition,
      );
    } else {
      await ShareService().shareLink(
        link: widget.note.slideUrl,
        sharePositionOrigin: sharePosition,
        eventName: EventName.document_save_pdf_success,
      );
    }
  }
}
