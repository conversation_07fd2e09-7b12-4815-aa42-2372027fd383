import 'package:flutter/material.dart' as flutter;
import 'package:intl/intl.dart';

/// Utility class for handling Right-to-Left (RTL) text direction
class RtlUtils {
  /// List of language codes that use RTL text direction
  static const List<String> rtlLanguages = [
    'ar', // Arabic
    'fa', // Farsi/Persian
    'he', // Hebrew
    'ps', // Pashto
    'ur', // Urdu
    'sd', // Sindhi
    'ug', // Uyghur
  ];

  /// Checks if a language code is RTL
  static bool isRtlLanguage(String languageCode) {
    return rtlLanguages.contains(languageCode.toLowerCase());
  }

  /// Gets the appropriate text direction for a language code
  static flutter.TextDirection getTextDirectionForLanguage(String languageCode) {
    return isRtlLanguage(languageCode) 
        ? flutter.TextDirection.rtl 
        : flutter.TextDirection.ltr;
  }

  /// Gets the appropriate text direction for a text content
  /// This uses the Unicode Bidirectional Algorithm through the intl package's Bidi class
  static flutter.TextDirection getTextDirectionForContent(String text) {
    if (text.isEmpty) return flutter.TextDirection.ltr;
    
    try {
      // Use the Bidi class to detect RTL directionality according to the Unicode Bidirectional Algorithm
      bool isRtl = Bidi.detectRtlDirectionality(text);
      return isRtl ? flutter.TextDirection.rtl : flutter.TextDirection.ltr;
    } catch (e) {
      // Fallback to our simple heuristic in case of issues with Bidi
      final rtlPattern = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
      final ltrPattern = RegExp(r'[a-zA-Z0-9]');
      
      final rtlCount = rtlPattern.allMatches(text).length;
      final ltrCount = ltrPattern.allMatches(text).length;
      
      return rtlCount > ltrCount ? flutter.TextDirection.rtl : flutter.TextDirection.ltr;
    }
  }
}
