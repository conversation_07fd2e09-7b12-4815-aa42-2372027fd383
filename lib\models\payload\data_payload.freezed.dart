// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'data_payload.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

DataPayload _$DataPayloadFromJson(Map<String, dynamic> json) {
  return _DataPayload.fromJson(json);
}

/// @nodoc
mixin _$DataPayload {
  String? get id => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DataPayloadCopyWith<DataPayload> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DataPayloadCopyWith<$Res> {
  factory $DataPayloadCopyWith(
          DataPayload value, $Res Function(DataPayload) then) =
      _$DataPayloadCopyWithImpl<$Res, DataPayload>;
  @useResult
  $Res call({String? id, String? type, String? title, String? content});
}

/// @nodoc
class _$DataPayloadCopyWithImpl<$Res, $Val extends DataPayload>
    implements $DataPayloadCopyWith<$Res> {
  _$DataPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? title = freezed,
    Object? content = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DataPayloadImplCopyWith<$Res>
    implements $DataPayloadCopyWith<$Res> {
  factory _$$DataPayloadImplCopyWith(
          _$DataPayloadImpl value, $Res Function(_$DataPayloadImpl) then) =
      __$$DataPayloadImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, String? type, String? title, String? content});
}

/// @nodoc
class __$$DataPayloadImplCopyWithImpl<$Res>
    extends _$DataPayloadCopyWithImpl<$Res, _$DataPayloadImpl>
    implements _$$DataPayloadImplCopyWith<$Res> {
  __$$DataPayloadImplCopyWithImpl(
      _$DataPayloadImpl _value, $Res Function(_$DataPayloadImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? title = freezed,
    Object? content = freezed,
  }) {
    return _then(_$DataPayloadImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DataPayloadImpl implements _DataPayload {
  const _$DataPayloadImpl(
      {this.id, required this.type, required this.title, this.content});

  factory _$DataPayloadImpl.fromJson(Map<String, dynamic> json) =>
      _$$DataPayloadImplFromJson(json);

  @override
  final String? id;
  @override
  final String? type;
  @override
  final String? title;
  @override
  final String? content;

  @override
  String toString() {
    return 'DataPayload(id: $id, type: $type, title: $title, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DataPayloadImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, title, content);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DataPayloadImplCopyWith<_$DataPayloadImpl> get copyWith =>
      __$$DataPayloadImplCopyWithImpl<_$DataPayloadImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DataPayloadImplToJson(
      this,
    );
  }
}

abstract class _DataPayload implements DataPayload {
  const factory _DataPayload(
      {final String? id,
      required final String? type,
      required final String? title,
      final String? content}) = _$DataPayloadImpl;

  factory _DataPayload.fromJson(Map<String, dynamic> json) =
      _$DataPayloadImpl.fromJson;

  @override
  String? get id;
  @override
  String? get type;
  @override
  String? get title;
  @override
  String? get content;
  @override
  @JsonKey(ignore: true)
  _$$DataPayloadImplCopyWith<_$DataPayloadImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
