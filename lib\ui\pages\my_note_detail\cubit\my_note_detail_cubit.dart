import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_review/in_app_review.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:markdown_quill/markdown_quill.dart';
import 'package:note_x/lib.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'my_note_detail_state.dart';

class MyNoteDetailCubit extends BaseCubit<MyNoteDetailState> {
  MyNoteDetailCubit(super.initialState) {
    _initHiveBox();
  }

  final GetNoteApiServiceImpl _getNoteApiService =
      GetIt.instance.get<GetNoteApiServiceImpl>();
  final QuizApiService _quizApiService = GetIt.instance.get<QuizApiService>();
  final _taskResultApiService = GetIt.instance.get<ResultTaskApiServiceImpl>();
  final FlashCardApiService _flashCardApiService =
      GetIt.instance.get<FlashCardApiService>();
  final MindMapApiService _mindMapApiService =
      GetIt.instance.get<MindMapApiService>();
  final _noteApiService = GetIt.instance.get<CreateNoteApiServiceImpl>();
  final _creditApiServiceImpl = GetIt.instance.get<CreditApiServiceImpl>();
  final _localService = GetIt.instance.get<LocalService>();
  final _folderService = FolderService();
  final _observerBoxNote = HiveService().noteBox.listenable();
  final ValueNotifier<Map<ExportNoteFormat, bool>> loadingNoteStates =
      ValueNotifier({
    ExportNoteFormat.pdf: false,
    ExportNoteFormat.docx: false,
  });
  final TextEditingController _titleController = TextEditingController();

  TextEditingController get getTitleController => _titleController;

  ValueListenable<Box<NoteModel>> get observerBoxNote => _observerBoxNote;
  static const String allNotesId = 'all_notes';
  String pdfUrl = '';

  static const String _mindMapCacheBoxName = 'mindmap_cache';
  static const String _exportPdfNoteCache = 'note_pdf_cache';

  late Box<MindMapCacheModel> _mindMapCacheBox;
  late Box<NoteExportPdfModel> _noteExportPdfCacheBox;

  List<QuizModel> _quizzes = [];
  List<FlashCardModel> _flashCards = [];
  FlashcardSetDataModel flashcardSetDataModel = FlashcardSetDataModel(
    setId: '',
    name: '',
    difficulty: AppConstants.kDefaultDifficulty,
  );

  QuizSetDataModel quizSetDataModel = QuizSetDataModel(
    setId: '',
    name: '',
    difficulty: AppConstants.kDefaultDifficulty,
  );

  FlashcardSetDataModel get getFlashCardData => flashcardSetDataModel;

  QuizSetDataModel get getQuizData => quizSetDataModel;

  bool _isReadyMindMap = false;

  bool get isReadyMindMap => _isReadyMindMap;
  bool _isGeneratingMindMap = false;
  late String _noteLocalId;
  late String _noteBackendId;
  // ignore: unused_field
  String _noteTitle = '';
  bool didFetchNoteDataFail = false;

  // Parameters for quiz and flashcard generation
  String _cardCount = AppConstants.kDefaultCardCount;
  String _difficulty = AppConstants.kDefaultDifficulty;
  String _topic = AppConstants.kDefaultTopic;

  String get noteBackendId => _noteBackendId;
  Timer? timerApiPulling;

  String get allNoteId => allNotesId;

  String get getPdfUrl => pdfUrl;

  List<QuizModel> get getQuizzes => _quizzes;

  List<FlashCardModel> get getFlashCards => _flashCards;
  QuillController _quillSummaryController = QuillController.basic();
  QuillController _quillTranscriptController = QuillController.basic();

  QuillController get getQuillSummaryController => _quillSummaryController;

  QuillController get getQuillTranscriptController =>
      _quillTranscriptController;

  Delta? _lastSavedDelta;
  Delta? _lastSavedDeltaTranscript;
  late NoteModel _noteModel;

  NoteModel get noteModel => _noteModel;

  late final VoidCallback _noteBoxListener;

  late final GlobalKey<NavigatorState> bottomSheetNavigatorKey =
      GlobalKey<NavigatorState>();
  final GlobalKey buttonKey = GlobalKey();

  late final List<FlashcardSetDto> _listFlashcardSet = [];

  List<FlashcardSetDto> get listFlashcardSets => _listFlashcardSet;

  late final List<QuizSetDto> _listQuizSets = [];

  List<QuizSetDto> get listQuizSets => _listQuizSets;

  void initNoteBoxListener({required NoteModel noteModel}) {
    _noteBoxListener = () {
      final note = observerBoxNote.value.get(noteModel.id);
      if (note != null && getTitleController.text != note.title) {
        getTitleController.text = note.title;
      }
      if (noteModel.summary.isEmpty == true && note?.summary != null) {
        setQuillControllerFromSummary(note?.summary ?? '');
        setQuillTranscriptController(note?.transcript ?? '',
            transcriptJson: note?.transcriptJson);
        emit(
          state.copyWith(
              isRefreshingSummary: !state.isRefreshingSummary,
              isRefreshingTranscript: !state.isRefreshingTranscript),
        );
      }

      //save updated note
      note != null ? _noteModel = note : ();
    };
    observerBoxNote.addListener(_noteBoxListener);
    _noteLocalId = noteModel.id;
    _noteModel = noteModel;
  }

  Future<void> createFolderNoteDetail(String folderName, NoteModel note,
      ValueNotifier<String> choosenFolderName) async {
    emit(state.copyWith(createFolderEvent: CreateFolderEvent.loading));
    await runCubitCatching(action: () async {
      await _folderService.createFolderAndAddNote(
          folderName, note, choosenFolderName);
      emit(state.copyWith(createFolderEvent: CreateFolderEvent.success));
    });
  }

  // PlayerController getAudioPlayerController() => _audioController;

  Future<void> setNoteInfo(NoteModel noteModel) async {
    _noteLocalId = noteModel.id;
    _noteBackendId = noteModel.backendNoteId;
    _noteTitle = noteModel.title;
    _noteModel = noteModel;
  }

  Future<void> fetchNoteDetail(String backendNoteId, NoteModel noteModel,
      {bool isCommunityNote = false}) async {
    didFetchNoteDataFail = false;
    return runCubitCatching(
        handleLoading: false,
        action: () async {
          if (backendNoteId.isEmpty || noteModel.id.isEmpty) return;
          if (isCommunityNote) {
            setQuillTranscriptController(noteModel.transcript,
                transcriptJson: noteModel.transcriptJson);
            return;
          }

          // Check network connection
          if (!await checkInternetConnection()) {
            CommonDialogs.showToast(S.current.no_internet,
                gravity: ToastGravity.CENTER, length: Toast.LENGTH_LONG);
            return;
          }

          // Check if note already exists and has a summary
          final existingNote = HiveService().noteBox.get(noteModel.id);
          final shouldFetchNoteDetail = existingNote == null ||
              (existingNote.summary.isEmpty && existingNote.transcript.isEmpty);

          if (shouldFetchNoteDetail == false &&
              existingNote?.noteStatus == NoteStatus.success) {
            setQuillControllerFromSummary(existingNote?.summary ?? '');
            setQuillTranscriptController(existingNote?.transcript ?? '',
                transcriptJson: existingNote?.transcriptJson);
            emit(
              state.copyWith(
                isRefreshingSummary: !state.isRefreshingSummary,
                isRefreshingTranscript: !state.isRefreshingTranscript,
              ),
            );
            return;
          }

          try {
            emit(state.copyWith(isFetchingNoteDataFromServer: true));

            final data = await _getNoteApiService.getNoteById(backendNoteId);
            final finalNote = data.copyWith(
              id: noteModel.id,
              noteStatus: NoteStatus.success,
              summary: data.summary.isNotEmpty
                  ? data.summary
                  : existingNote?.summary ?? '',
              transcript: data.transcript.isNotEmpty
                  ? data.transcript
                  : existingNote?.transcript ?? '',
              backendNoteId: backendNoteId,
              flashCard: data.flashcardSets.isNotEmpty
                  ? data.flashcardSets.last.cards
                  : existingNote?.flashCard ?? [],
              flashcardSets: data.flashcardSets.isNotEmpty
                  ? data.flashcardSets
                  : existingNote?.flashcardSets ?? [],
              quiz: data.quizSets.isNotEmpty
                  ? data.quizSets.last.questions
                  : existingNote?.quiz ?? [],
              quizSets: data.quizSets.isNotEmpty
                  ? data.quizSets
                  : existingNote?.quizSets ?? [],
              transcriptJson: data.transcriptJson.isNotEmpty
                  ? data.transcriptJson
                  : existingNote?.transcriptJson ?? [],
            );
            setQuillControllerFromSummary(finalNote.summary);
            setQuillTranscriptController(finalNote.transcript,
                transcriptJson: finalNote.transcriptJson);
            await HiveService().createOrUpdateNote(finalNote.id, finalNote);
          } catch (e) {
            debugPrint('Error fetching note detail: $e');
            debugPrint('Error details: ${e.toString()}');
            didFetchNoteDataFail = true;
            // Update note status to error if it exists
            if (existingNote != null) {
              await HiveService().createOrUpdateNote(noteModel.id,
                  existingNote.copyWith(noteStatus: NoteStatus.error));
            }

            rethrow;
          } finally {
            // Always reset loading state
            emit(state.copyWith(isFetchingNoteDataFromServer: false));
          }
        });
  }

  void setQuillControllerFromSummary(String summaryNote) {
    try {
      // Ensure we have non-empty content with at least a newline character
      String preservedNewlines = summaryNote.isNotEmpty ? summaryNote : ' \n';

      Delta delta;
      try {
        final converter = MarkdownToDelta(
          markdownDocument: md.Document(
            encodeHtml: false,
            extensionSet: md.ExtensionSet.gitHubFlavored,
          ),
        );
        delta = converter.convert(preservedNewlines);
      } catch (conversionError) {
        debugPrint('Error in markdown conversion: $conversionError');
        // Create a simple default delta with a space and newline if conversion fails
        delta = Delta()..insert(' \n');
      }

      // Ensure delta is not empty
      if (delta.isEmpty) {
        delta = Delta()..insert(' \n');
      }

      _quillSummaryController = QuillController(
        document: Document.fromDelta(delta),
        selection: const TextSelection.collapsed(offset: 0),
      );

      // Store initial delta
      _lastSavedDelta = delta;

      // Add change listener
      _quillSummaryController.document.changes.listen((event) {
        if (event.source == ChangeSource.local) {
          final hasUnsavedChanges =
              _lastSavedDelta != _quillSummaryController.document.toDelta();
          emit(state.copyWith(hasEdits: hasUnsavedChanges));
        }
      });
    } catch (e) {
      debugPrint('Error setting up QuillController: $e');
      // Create a basic controller with empty content as fallback
      _quillSummaryController = QuillController.basic();
      _lastSavedDelta = _quillSummaryController.document.toDelta();
    }
  }

  void setQuillTranscriptController(String transcript,
      {List<TranscriptModel>? transcriptJson}) {
    try {
      Delta delta;

      if (transcriptJson != null && transcriptJson.isNotEmpty) {
        delta = Delta();
        final sortedTranscript = List<TranscriptModel>.from(transcriptJson)
          ..sort((a, b) => a.start.compareTo(b.start));

        for (int i = 0; i < sortedTranscript.length; i++) {
          final entry = sortedTranscript[i];
          final minutes = (entry.start / 60).floor();
          final seconds = entry.start % 60;
          final formattedTime = '$minutes:${seconds.toStringAsFixed(2)}';

          delta.insert('$formattedTime: ', {'bold': true, 'color': '#3366FF'});
          delta.insert(entry.text);
          delta.insert(i < sortedTranscript.length - 1 ? '\n\n' : '\n');
        }
      } else {
        final String safeTranscript = transcript.isNotEmpty ? transcript : ' ';
        delta = Delta()
          ..insert(safeTranscript.endsWith('\n')
              ? safeTranscript
              : '$safeTranscript\n');
      }

      _quillTranscriptController = QuillController(
        document: Document.fromDelta(delta),
        selection: const TextSelection.collapsed(offset: 0),
      );

      _lastSavedDeltaTranscript = delta;

      _quillTranscriptController.document.changes.listen((event) {
        if (event.source == ChangeSource.local) {
          emit(state.copyWith(
              hasEdits: _lastSavedDeltaTranscript !=
                  _quillTranscriptController.document.toDelta()));
        }
      });
    } catch (e) {
      debugPrint('Error setting up transcript QuillController: $e');
      _quillTranscriptController = QuillController.basic();
      _lastSavedDeltaTranscript = _quillTranscriptController.document.toDelta();
    }
  }

  void onQuiz({
    bool communityNote = false,
    String? customNumQuestions,
    String? customDifficulty,
    String? customTopic,
  }) async {
    if (_listQuizSets.length >= 3) {
      CommonDialogs.showToast(
        S.current.max_3_quiz_sets,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
      return;
    }
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.quizzes_btmsht_create_clicked,
    );
    final note = HiveService().noteBox.get(_noteLocalId);
    if (note == null) return;
    emit(state.copyWith(isGeneratingQuiz: true));
    final updateNote = note.copyWith(isGeneratingQuiz: true);
    await HiveService().createOrUpdateNote(_noteLocalId, updateNote);

    // Save parameters to class fields if provided
    if (customNumQuestions != null) _cardCount = customNumQuestions;
    if (customDifficulty != null) _difficulty = customDifficulty;
    if (customTopic != null) _topic = customTopic;

    final numQuestion = customNumQuestions ?? _cardCount;
    final difficulty = customDifficulty ?? _difficulty;
    final topic = customTopic ?? _topic;

    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final taskId = await _quizApiService.getTaskIdForQuiz(
          note.backendNoteId.isNotEmpty ? note.backendNoteId : _noteLocalId,
          numQuestion,
          'Quiz Set ${_listQuizSets.length}',
          difficulty,
          topic,
          communityNote,
        );
        final result = await _taskResultApiService.getTaskResultV2(
          taskId: taskId,
          localNoteId: _noteLocalId,
          isCommunityNote: communityNote,
          isQuiz: true,
        );
        _quizzes = result!.quiz;
        quizSetDataModel = result.quizSetDataModel!;

        // Save quiz set info to local storage
        await _localService.setQuizSetInfo(
          _noteBackendId,
          quizSetDataModel.setId,
          quizSetDataModel.name,
        );

        final finalNote = updateNote.copyWith(
          quiz: _quizzes,
          statusMindMap:
              updateNote.statusMindMap == false ? _isReadyMindMap : true,
          flashCard:
              updateNote.flashCard.isEmpty ? _flashCards : updateNote.flashCard,
          quizSetDataModel: quizSetDataModel,
          isGeneratingQuiz: false,
          isGeneratingFlashCard: state.isGeneratingFlashCard,
          isGeneratingMindMap: _isGeneratingMindMap,
        );
        await HiveService().createOrUpdateNote(_noteLocalId, finalNote);
        emit(state.copyWith(isGeneratingQuiz: false));
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.quizzes_btmsht_create_success,
        );
      },
      doOnError: (exception) => Future(() async {
        final failedNote = updateNote.copyWith(
          isGeneratingQuiz: false,
        );
        await HiveService().createOrUpdateNote(_noteLocalId, failedNote);
        emit(state.copyWith(isGeneratingQuiz: false));
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.quizzes_btmsht_create_fail,
        );
        _onGetTaskResultFail();
      }),
    );
  }

  void onFlashCard({
    bool communityNote = false,
    String? customCardCount,
    String? customDifficulty,
    String? customTopic,
  }) async {
    if (_listFlashcardSet.length >= 3) {
      CommonDialogs.showToast(
        S.current.max_3_flashcard_sets,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
      return;
    }
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.flashcard_btmsht_create_clicked,
    );
    final note = HiveService().noteBox.get(_noteLocalId);
    if (note == null) return;
    emit(state.copyWith(isGeneratingFlashCard: true));

    final updateNote = note.copyWith(isGeneratingFlashCard: true);
    await HiveService().createOrUpdateNote(_noteLocalId, updateNote);

    // Save parameters to class fields if provided
    if (customCardCount != null) _cardCount = customCardCount;
    if (customDifficulty != null) _difficulty = customDifficulty;
    if (customTopic != null) _topic = customTopic;

    final cardCount = customCardCount ?? _cardCount;
    final difficulty = customDifficulty ?? _difficulty;
    final topic = customTopic ?? _topic;

    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final taskId = await _flashCardApiService.getTaskIdForFlashCards(
          note.backendNoteId.isNotEmpty ? note.backendNoteId : _noteLocalId,
          cardCount,
          'Flashcard Set ${_listFlashcardSet.length}',
          difficulty,
          topic,
          communityNote,
        );
        final result = await _taskResultApiService.getTaskResultV2(
          taskId: taskId,
          localNoteId: _noteLocalId,
          isCommunityNote: communityNote,
          isFlashCard: true,
        );

        _flashCards = result!.flashCard;
        flashcardSetDataModel = result.flashcardSetDataModel!;

        // Save flashcard set info to local storage
        await _localService.setFlashcardSetInfo(
          _noteBackendId,
          flashcardSetDataModel.setId,
          flashcardSetDataModel.name,
        );

        final finalNote = updateNote.copyWith(
          isGeneratingFlashCard: false,
          quiz: updateNote.quiz.isEmpty ? _quizzes : updateNote.quiz,
          statusMindMap:
              updateNote.statusMindMap == false ? _isReadyMindMap : true,
          flashCard: _flashCards,
          flashcardSetDataModel: flashcardSetDataModel,
          isGeneratingQuiz: state.isGeneratingQuiz,
          isGeneratingMindMap: _isGeneratingMindMap,
        );
        await HiveService().createOrUpdateNote(_noteLocalId, finalNote);
        emit(state.copyWith(isGeneratingFlashCard: false));
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.flashcard_btmsht_create_success,
        );
      },
      doOnError: (exception) => Future(() async {
        final failedNote = updateNote.copyWith(
          isGeneratingFlashCard: false,
        );
        await HiveService().createOrUpdateNote(_noteLocalId, failedNote);
        emit(state.copyWith(isGeneratingFlashCard: false));
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.flashcard_btmsht_create_fail,
        );
        _onGetTaskResultFail();
      }),
    );
  }

  void onMindMap({bool communityNote = false}) async {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.mind_map_scr_create_click,
    );
    final note = HiveService().noteBox.get(_noteLocalId);
    if (note == null) return;
    _isGeneratingMindMap = true;
    final updateIsGeneratingMindMap = note.copyWith(isGeneratingMindMap: true);
    await HiveService()
        .createOrUpdateNote(_noteLocalId, updateIsGeneratingMindMap);

    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final taskId = await _mindMapApiService.getTaskIdForMindMaps(
          note.backendNoteId.isNotEmpty ? note.backendNoteId : _noteLocalId,
          communityNote,
        );
        await _taskResultApiService.getTaskResultV2(
          taskId: taskId,
          localNoteId: _noteLocalId,
          isCommunityNote: communityNote,
          isMindMap: true,
        );
        final finalNote = updateIsGeneratingMindMap.copyWith(
          isGeneratingMindMap: false,
          isGeneratingQuiz: state.isGeneratingQuiz,
          isGeneratingFlashCard: state.isGeneratingFlashCard,
          quiz: updateIsGeneratingMindMap.quiz.isEmpty
              ? _quizzes
              : updateIsGeneratingMindMap.quiz,
          statusMindMap: true,
          flashCard: updateIsGeneratingMindMap.flashCard.isEmpty
              ? _flashCards
              : updateIsGeneratingMindMap.flashCard,
        );
        await HiveService().createOrUpdateNote(_noteLocalId, finalNote);
        _isReadyMindMap = true;
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.mind_map_scr_create_success,
        );
      },
      doOnError: (exception) => Future(() async {
        final failedNote = updateIsGeneratingMindMap.copyWith(
          isGeneratingMindMap: false,
        );
        await HiveService().createOrUpdateNote(_noteLocalId, failedNote);
        _isGeneratingMindMap = false;
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.mind_map_scr_create_fail,
        );
        _onGetTaskResultFail();
      }),
    );
  }

  Future<void> onDeleteNote({bool isNoteFailed = false}) async {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_detail_delete_success,
    );
    emit(state.copyWith(deleteNoteEvent: DeleteNoteEvent.loading));
    if (isNoteFailed) {
      try {
        await HiveService().noteFailedBox.delete(_noteLocalId);
        CommonDialogs.showToast(
            gravity: ToastGravity.BOTTOM, S.current.delete_success);
        emit(state.copyWith(deleteNoteEvent: DeleteNoteEvent.success));
      } catch (e) {
        debugPrint('Error deleting note: $e');
      }
    } else {
      runCubitCatching(action: () async {
        if (_noteBackendId.isNotEmpty) {
          final deleteSuccess =
              await _noteApiService.deleteNote(noteId: _noteBackendId);
          if (deleteSuccess) {
            await HiveService().deleteNoteById(_noteLocalId);
            CommonDialogs.showToast(
                gravity: ToastGravity.BOTTOM, S.current.delete_success);
            emit(state.copyWith(deleteNoteEvent: DeleteNoteEvent.success));
          } else {
            CommonDialogs.showToast('Delete fail! Please try again!');
          }
        }
      });
    }
  }

  void _onGetTaskResultFail() {
    CommonDialogs.closeLoading();
    CommonDialogs.showToast(S.current.get_fail,
        gravity: ToastGravity.BOTTOM, length: Toast.LENGTH_LONG);
  }

  Future<void> addNoteToFolder(FolderModel folder, NoteModel note,
      ValueNotifier<String> choosenFolderName) async {
    await _folderService.addNoteToFolder(folder, note, choosenFolderName);
  }

  // ignore: always_declare_return_types
  resetState() {
    emit(state.copyWith(
        quizEvent: QuizEvent.initial,
        flashCardEvent: FlashCardEvent.initial,
        translateNoteEvent: TranslateNoteEvent.none,
        oneShotEvent: MyNoteDetailOneShotEvent.none));
  }

  void onTranslateNote() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_detail_translate_note,
    );
    if (appCubit.isUserProOrProlite()) {
      emit(state.copyWith(translateNoteEvent: TranslateNoteEvent.initial));
      return;
    }
    emit(
      state.copyWith(
        oneShotEvent: MyNoteDetailOneShotEvent.onShowIAPFromTranslateNote,
      ),
    );
  }

  void onTranslateToLanguageWith(String languageCode) {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.note_detail_translate_note_submit,
    );
    if (_noteBackendId.isEmpty) {
      return;
    }
    emit(state.copyWith(translateNoteEvent: TranslateNoteEvent.loading));

    var idNoteLocal = '';
    runCubitCatching(
      handleLoading: false,
      action: () async {
        final taskDto = await _noteApiService.translateNote(
            noteId: _noteBackendId, languageCode: languageCode);

        idNoteLocal = MyUtils.generateUniqueId();
        final noteModel = await NoteBuilder.createTranslatingNote(
          id: idNoteLocal,
          userId: appCubit.getAppUser().id,
        );

        await HiveService().createOrUpdateNote(idNoteLocal, noteModel);
        await _taskResultApiService.getTaskResult(
          taskId: taskDto.taskId,
          localNoteId: idNoteLocal,
          onStatusSuccess: () => _refreshUserCredits(),
        );
        emit(state.copyWith(translateNoteEvent: TranslateNoteEvent.success));
      },
      doOnError: (exception) => Future(() async {
        if (idNoteLocal.isNotEmpty) {
          await HiveService().deleteNoteById(idNoteLocal);
        }
        emit(state.copyWith(translateNoteEvent: TranslateNoteEvent.error));
      }),
    );
  }

  void _refreshUserCredits() async {
    final creditInfo = await _creditApiServiceImpl.fetchDataCredit();
    final user = appCubit.getAppUser().copyWith(
        youtubeCredit: creditInfo.youtubeCredit,
        webCredit: creditInfo.webCredit,
        audioCredit: creditInfo.audioCredit,
        rewardCredits: creditInfo.rewardCredits,
        shortsCredit: creditInfo.shortsCredit,
        documentCredit: creditInfo.documentCredit,
        slideCredit: creditInfo.slideCredit,
        userType: mapUserTypeStringToEnum(creditInfo.userType));
    appCubit.updateAppUser(user);
    _localService.saveAppUser(user);
  }

  Future<MindMapExportResult?> exportMindMap(String format) async {
    try {
      if (_noteBackendId.isEmpty) return null;

      // Get URL from cache
      String mindMapUrl =
          _mindMapCacheBox.get(_noteBackendId)?.formatUrls[format] ?? '';
      if (mindMapUrl.isEmpty) {
        final mindMapDto = await _noteApiService.exportMindMap(
          _noteBackendId,
          format,
        );
        mindMapUrl = mindMapDto.url ?? '';
        if (mindMapUrl.isNotEmpty) {
          await _saveMindMapToCache(_noteBackendId, format, mindMapUrl);
        }
      }
      // Download file nếu cần
      String? localPath;
      final tempDir = await getTemporaryDirectory();
      final safeFileName =
          'mindmap_${DateTime.now().millisecondsSinceEpoch}.$format';
      final tempFilePath = path.join(tempDir.path, safeFileName);
      final file = File(tempFilePath);

      final response = await http.get(Uri.parse(mindMapUrl));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        localPath = file.path;
      }

      return MindMapExportResult(
        url: mindMapUrl,
        format: format,
        localPath: localPath,
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> handleExportNote(
    ExportNoteFormat format,
    ExportType type,
    Rect? sharePosition,
  ) async {
    loadingNoteStates.value = Map.from(loadingNoteStates.value)
      ..[format] = true;

    try {
      if (_noteBackendId.isEmpty) return;

      NoteExportDto? noteDto;
      noteDto = await _noteApiService.exportNote(
        type.displayType,
        _noteBackendId,
        format.displayFormat,
      );

      final noteUrl = noteDto.url ?? '';
      if (noteUrl.isEmpty) {
        CommonDialogs.showToast(
          S.current.no_url_provided,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      // Download and share file

      final tempDir = await getTemporaryDirectory();
      final saveFileName = '$noteBackendId.${format.displayFormat}';
      final tempFilePath = path.join(tempDir.path, saveFileName);
      final file = File(tempFilePath);

      final response = await http
          .get(Uri.parse(noteUrl))
          .timeout(const Duration(seconds: 30));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);

        // Share file
        await ShareService().shareFile(
            filePath: file.path,
            sharePositionOrigin: sharePosition,
            eventName: type.getAnalyticsEventName(format),
            onSuccess: () {
              final InAppReview inAppReview = InAppReview.instance;
              inAppReview.requestReview();
            });
        // Clean up temporary file
        await file.delete();
      }
    } on AppErrorException catch (error) {
      CommonDialogs.showToast(error.message ?? S.current.no_generated,
          gravity: ToastGravity.BOTTOM, length: Toast.LENGTH_LONG);
    } on HttpException {
      CommonDialogs.showToast(S.current.http_failed,
          gravity: ToastGravity.BOTTOM, length: Toast.LENGTH_LONG);
    } catch (e) {
      CommonDialogs.showToast(S.current.export_failed,
          gravity: ToastGravity.BOTTOM, length: Toast.LENGTH_LONG);
    } finally {
      bottomSheetNavigatorKey.currentState?.pop();
      loadingNoteStates.value = Map.from(loadingNoteStates.value)
        ..[format] = false;
    }
  }

  Future<void> handleExportFlashcardSet(
    ExportNoteFormat format,
    Rect? sharePosition,
    String titleName,
    String setId,
    ExportType exportType,
  ) async {
    loadingNoteStates.value = Map.from(loadingNoteStates.value)
      ..[format] = true;
    try {
      // Get setId from noteModel if empty
      if (setId.isEmpty && _noteModel.flashcardSets.isNotEmpty) {
        setId = _noteModel.flashcardSets.last.setId;
        titleName = _noteModel.flashcardSets.last.name;
      }

      if (setId.isEmpty) {
        CommonDialogs.showToast(
          S.current.flashcard_set_not_found,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      ExportSetDto? exportSetDto;
      exportSetDto = await _noteApiService.exportFlashcardSet(
        setId,
        format.displayFormat,
        '',
      );

      final setUrl = exportSetDto.url ?? '';
      if (setUrl.isEmpty) {
        CommonDialogs.showToast(
          S.current.no_url_provided,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      // Download and share file
      final tempDir = await getTemporaryDirectory();
      final saveFileName = '$setId.${format.displayFormat}';
      final tempFilePath = path.join(tempDir.path, saveFileName);
      final file = File(tempFilePath);

      final response = await http
          .get(Uri.parse(setUrl))
          .timeout(const Duration(seconds: 30));
      if (response.statusCode == 200) {
        try {
          // Write file
          await file.writeAsBytes(response.bodyBytes);

          // Verify file exists and is readable
          if (!await file.exists()) {
            throw FileSystemException(S.current.failed_to_save_file);
          }

          // Share file
          await ShareService().shareFile(
            filePath: file.path,
            sharePositionOrigin: sharePosition,
            eventName: exportType.getAnalyticsEventName(format),
          );
        } catch (e) {
          debugPrint(e.toString());
          CommonDialogs.showToast(
            S.current.export_failed,
            gravity: ToastGravity.BOTTOM,
            length: Toast.LENGTH_LONG,
          );
        } finally {
          // Clean up temp file if it exists
          try {
            if (await file.exists()) {
              await file.delete();
            }
          } catch (e) {
            debugPrint('Failed to delete temporary file: $e');
          }
        }
      } else {
        CommonDialogs.showToast(
          S.current.export_failed,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
      }
    } on AppErrorException catch (error) {
      CommonDialogs.showToast(
        error.message ?? S.current.no_generated,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } on HttpException {
      CommonDialogs.showToast(
        S.current.http_failed,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } catch (e) {
      CommonDialogs.showToast(
        S.current.export_failed,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } finally {
      bottomSheetNavigatorKey.currentState?.pop();
      loadingNoteStates.value = Map.from(loadingNoteStates.value)
        ..[format] = false;
    }
  }

  Future<void> handleExportQuizSet(
    ExportNoteFormat format,
    Rect? sharePosition,
    String titleName,
    String setId,
    ExportType exportType,
  ) async {
    loadingNoteStates.value = Map.from(loadingNoteStates.value)
      ..[format] = true;

    try {
      if (setId.isEmpty) {
        CommonDialogs.showToast(
          S.current.quiz_set_not_found,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      ExportSetDto? exportSetDto;
      exportSetDto = await _noteApiService.exportQuizSet(
        setId,
        format.displayFormat,
        '',
      );

      final setUrl = exportSetDto.url ?? '';
      if (setUrl.isEmpty) {
        CommonDialogs.showToast(
          S.current.no_url_provided,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      // Download and share file
      final tempDir = await getTemporaryDirectory();
      final saveFileName = '$setId.${format.displayFormat}';
      final tempFilePath = path.join(tempDir.path, saveFileName);
      final file = File(tempFilePath);

      final response = await http
          .get(Uri.parse(setUrl))
          .timeout(const Duration(seconds: 30));
      if (response.statusCode == 200) {
        try {
          // Write file
          await file.writeAsBytes(response.bodyBytes);

          // Verify file exists and is readable
          if (!await file.exists()) {
            throw FileSystemException(S.current.failed_to_save_file);
          }

          // Share file
          await ShareService().shareFile(
            filePath: file.path,
            sharePositionOrigin: sharePosition,
            eventName: exportType.getAnalyticsEventName(format),
          );
        } catch (e) {
          CommonDialogs.showToast(
            S.current.export_failed,
            gravity: ToastGravity.BOTTOM,
            length: Toast.LENGTH_LONG,
          );
        } finally {
          // Clean up temp file if it exists
          try {
            if (await file.exists()) {
              await file.delete();
            }
          } catch (e) {
            debugPrint('Failed to delete temporary file: $e');
          }
        }
      } else {
        CommonDialogs.showToast(
          S.current.export_failed,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
      }
    } on AppErrorException catch (error) {
      CommonDialogs.showToast(
        error.message ?? S.current.no_generated,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } on HttpException {
      CommonDialogs.showToast(
        S.current.http_failed,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } catch (e) {
      CommonDialogs.showToast(
        S.current.export_failed,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } finally {
      bottomSheetNavigatorKey.currentState?.pop();
      loadingNoteStates.value = Map.from(loadingNoteStates.value)
        ..[format] = false;
    }
  }

  Future<void> _initHiveBox() async {
    _mindMapCacheBox =
        await Hive.openBox<MindMapCacheModel>(_mindMapCacheBoxName);
    _noteExportPdfCacheBox =
        await Hive.openBox<NoteExportPdfModel>(_exportPdfNoteCache);
  }

  Future<void> _saveMindMapToCache(
    String noteId,
    String format,
    String url,
  ) async {
    final existingCache = _mindMapCacheBox.get(noteId);
    final Map<String, String> formatUrls = {
      ...existingCache?.formatUrls ?? {},
      format: url,
    };

    await _mindMapCacheBox.put(
      noteId,
      MindMapCacheModel(
        noteId: noteId,
        formatUrls: formatUrls,
      ),
    );
  }

  Future<void> onEditNote(
      {String? summary,
      String? title,
      String? transcript,
      List<TranscriptModel>? transcriptJson,
      required NoteModel note}) async {
    await runCubitCatching(action: () async {
      // Process transcript if edited and transcriptJson not provided
      List<TranscriptModel>? updatedTranscriptJson = transcriptJson;
      if (transcript != null && updatedTranscriptJson == null) {
        updatedTranscriptJson = convertDeltaToTranscriptJson(
            _quillTranscriptController.document.toDelta(), note.transcriptJson);

        // Use original if conversion failed
        if (updatedTranscriptJson.isEmpty) {
          updatedTranscriptJson = note.transcriptJson;
        }
      }

      // Update note via API
      await _noteApiService.updateNote(
        backendNoteId: note.backendNoteId,
        summary: summary,
        title: title,
        transcript: transcript,
        transcriptJson: updatedTranscriptJson,
      );

      // Update local note
      final updatedNote = note.copyWith(
        summary: summary ?? note.summary,
        title: title ?? note.title,
        transcript: transcript ?? note.transcript,
        transcriptJson: updatedTranscriptJson ?? note.transcriptJson,
      );

      await HiveService().noteBox.put(note.id, updatedNote);
      getTitleController.value = TextEditingValue(text: updatedNote.title);

      // Update saved deltas
      _lastSavedDelta = _quillSummaryController.document.toDelta();
      _lastSavedDeltaTranscript = _quillTranscriptController.document.toDelta();
      emit(state.copyWith(hasEdits: false));

      // Emit a refresh event to force UI update for transcript
      if (updatedTranscriptJson != null) {
        emit(state.copyWith(
            isRefreshingTranscript: !state.isRefreshingTranscript));
      }
    });
  }

  List<TranscriptModel> convertDeltaToTranscriptJson(
      Delta delta, List<TranscriptModel>? originalTranscriptJson) {
    if (originalTranscriptJson == null || originalTranscriptJson.isEmpty) {
      return [];
    }

    try {
      // Extract text and parse segments
      final String plainText = Document.fromDelta(delta).toPlainText();
      final List<String> lines =
          plainText.replaceAll('\r\n', '\n').trim().split('\n');

      // Extract timestamp segments
      final RegExp timestampPattern =
          RegExp(r'^\s*\d+:\d+(?::\d+)?(?:\.\d+)?:?.*$');
      final List<String> segments = [];
      String currentSegment = '';

      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isEmpty) continue;

        if (timestampPattern.hasMatch(trimmedLine)) {
          if (currentSegment.trim().isNotEmpty) {
            segments.add(currentSegment.trim());
          }
          currentSegment = trimmedLine;
        } else {
          currentSegment = currentSegment.isEmpty
              ? trimmedLine
              : '$currentSegment $trimmedLine';
        }
      }

      if (currentSegment.trim().isNotEmpty) {
        segments.add(currentSegment.trim());
      }

      // Parse timestamps and content
      final RegExp extractPattern =
          RegExp(r'^\s*(\d+:\d+(?::\d+)?(?:\.\d+)?):?\s*(.*)$', dotAll: true);
      final Map<double, String> newEntries = {};

      for (final segment in segments) {
        final match = extractPattern.firstMatch(segment);
        if (match != null) {
          final timeString = match.group(1) ?? '';
          final text = match.group(2)?.trim() ?? segment.trim();
          final timeInSeconds = parseTimeToSeconds(timeString);

          if (timeInSeconds > 0) {
            newEntries[timeInSeconds] = text;
          }
        }
      }

      // Create reference map from original transcript
      final Map<double, TranscriptModel> originalByTime = {
        for (var entry in originalTranscriptJson) entry.start: entry
      };

      // Build new transcript models
      final List<double> timePoints = newEntries.keys.toList()..sort();
      final List<TranscriptModel> result = [];

      for (int i = 0; i < timePoints.length; i++) {
        final time = timePoints[i];
        final text = newEntries[time]!;
        double duration = 1.0;
        String speaker = '';

        // Get metadata from original entry if available
        if (originalByTime.containsKey(time)) {
          duration = originalByTime[time]!.duration;
          speaker = originalByTime[time]!.speaker;
        } else if (i < timePoints.length - 1) {
          // Calculate duration from next timestamp
          duration = max(timePoints[i + 1] - time, 1.0);

          // Find nearest speaker
          TranscriptModel? nearestEntry;
          double minDiff = double.infinity;

          for (final entry in originalTranscriptJson) {
            final diff = (entry.start - time).abs();
            if (diff < minDiff) {
              minDiff = diff;
              nearestEntry = entry;
            }
          }

          speaker = nearestEntry?.speaker ?? '';
        }

        result.add(TranscriptModel(
          text: text,
          start: time,
          duration: duration,
          speaker: speaker,
        ));
      }

      result.sort((a, b) => a.start.compareTo(b.start));
      return result.isEmpty ? originalTranscriptJson : result;
    } catch (e) {
      debugPrint('Error converting delta to transcriptJson: $e');
      return originalTranscriptJson;
    }
  }

  double parseTimeToSeconds(String timeString) {
    try {
      final parts = timeString.split(':');

      if (parts.length == 2) {
        // MM:SS or MM:SS.ms format
        return int.parse(parts[0].trim()) * 60 + double.parse(parts[1].trim());
      } else if (parts.length == 3) {
        // HH:MM:SS or HH:MM:SS.ms format
        return int.parse(parts[0].trim()) * 3600 +
            int.parse(parts[1].trim()) * 60 +
            double.parse(parts[2].trim());
      }
    } catch (e) {
      // Silent failure, will return 0
    }
    return 0.0;
  }

  void discardChanges() {
    if (_lastSavedDelta != null) {
      _quillSummaryController.document = Document.fromDelta(_lastSavedDelta!);
    }
    if (_lastSavedDeltaTranscript != null) {
      _quillTranscriptController.document =
          Document.fromDelta(_lastSavedDeltaTranscript!);
    }
    emit(state.copyWith(hasEdits: false));
  }

  @override
  Future<void> close() async {
    await _mindMapCacheBox.close();
    await _noteExportPdfCacheBox.close();
    _titleController.dispose();
    _quillSummaryController.dispose();
    observerBoxNote.removeListener(_noteBoxListener);
    return super.close();
  }

  void onTapDeleteNoteEvent() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_delete,
      );

  void onAddFolderEvent() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_add_folder,
      );

  void onEditSummary() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_edit,
      );

  void onShareSummaryEvent() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_share_summary,
      );

  void onShareTranscriptEvent() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_share_transcript,
      );

  void onShareNoteLink() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_share_note_link,
      );

  void onShowMenu() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_menu,
      );

  void onNoteSummaryBack() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_back,
      );

  void onCancelDeleteNote() => AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_delete_cancel,
      );

  bool get hasUnsavedChanges {
    final note = HiveService().noteBox.get(_noteLocalId);
    final hasTitleChanges =
        note != null && getTitleController.text != note.title;
    final hasContentChanges = _lastSavedDelta != null &&
        _lastSavedDelta != _quillSummaryController.document.toDelta();
    final hasTranscriptChanges = _lastSavedDeltaTranscript != null &&
        _lastSavedDeltaTranscript !=
            _quillTranscriptController.document.toDelta();

    return hasTitleChanges || hasContentChanges || hasTranscriptChanges;
  }

  Future<void> updateNoteSharingSetting({
    bool? isPublic,
    bool? isPasswordProtected,
  }) async {
    await runCubitCatching(action: () async {
      final noteShareDto = await _noteApiService.updateNoteSharing(
        backendNoteId: _noteBackendId,
        isUpdatePublicShare: isPublic != null,
        udpatedValue: isPublic ?? isPasswordProtected ?? false,
      );
      await HiveService().createOrUpdateNote(
        _noteLocalId,
        _noteModel.copyWith(
          isPublic: noteShareDto.isPublic,
          isPasswordProtected: noteShareDto.isPasswordProtected,
          shareLink: noteShareDto.shareLink,
          sharePassword: noteShareDto.sharePassword,
        ),
      );
    });
  }

  /// -------- CRUD FOR FlashCard SETS -------- ////
  /// -------- CRUD FOR FlashCard SETS -------- ////
  /// -------- CRUD FOR FlashCard SETS -------- ////

  void getAllFlashcardSets(String noteId) async {
    emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.loading));
    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final data = await _getNoteApiService.getAllFlashcardSets(noteId);
        _listFlashcardSet.clear();
        if (data.isNotEmpty) {
          _listFlashcardSet.addAll(data);
        }
        emit(
          state.copyWith(
            flashcardEvent: FlashcardOneShotEvent.success,
          ),
        );
      },
      doOnError: (exception) => Future(() {
        debugPrint('Error getting flashcard sets: $exception');
        emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.error));
      }),
    );
  }

  Future<bool> deleteFlashcardSet(String setId) async {
    emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.loading));
    bool success = false;
    await runCubitCatching(
      handleLoading: false,
      action: () async {
        success = await _getNoteApiService.deleteFlashcardSet(setId: setId);
        if (success) {
          // Remove the deleted set from the local list
          _listFlashcardSet.removeWhere((set) => set.setId == setId);

          // If there are remaining sets, get the cards of the last set
          if (_listFlashcardSet.isNotEmpty) {
            final lastSetId = _listFlashcardSet.last.setId;
            final result =
                await _getNoteApiService.chooseFlashcardSet(lastSetId);
            _flashCards = result.cards;

            // Update quiz set info in local service
            await _localService.setFlashcardSetInfo(
              _noteBackendId,
              result.setId,
              result.name,
            );
          } else {
            _flashCards = [];
          }

          // Update the local note with the new flashcard data
          final note = HiveService().noteBox.get(_noteLocalId);
          if (note != null) {
            final updatedNote = note.copyWith(
              flashCard: _flashCards,
              flashcardSetDataModel: _listFlashcardSet.isNotEmpty
                  ? FlashcardSetDataModel(
                      setId: _listFlashcardSet.last.setId,
                      name: _listFlashcardSet.last.name,
                      difficulty: _listFlashcardSet.last.difficulty,
                      cardsCount: _listFlashcardSet.last.cardsCount,
                    )
                  : FlashcardSetDataModel(
                      setId: '',
                      name: '',
                      difficulty: AppConstants.kDefaultDifficulty,
                    ),
            );
            await HiveService().createOrUpdateNote(_noteLocalId, updatedNote);
          }

          emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.success));
        } else {
          emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.error));
        }
      },
      doOnError: (exception) => Future(() {
        debugPrint('Error deleting flashcard set: $exception');
        emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.error));
        success = false;
      }),
    );
    return success;
  }

  Future<bool> editFlashcardSet(String setId, String newName) async {
    emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.loading));
    bool success = false;
    await runCubitCatching(
      handleLoading: false,
      action: () async {
        final updatedSet =
            await _getNoteApiService.editFlashcardSet(setId, newName);

        // Update the set in the local list
        final index = _listFlashcardSet.indexWhere((set) => set.setId == setId);
        if (index != -1) {
          _listFlashcardSet[index] = updatedSet;
        }

        // Save to local service
        await _localService.setFlashcardSetInfo(
          _noteBackendId,
          setId,
          newName,
        );

        emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.success));
        success = true;
      },
      doOnError: (exception) => Future(() {
        debugPrint('Error editing flashcard set: $exception');
        emit(state.copyWith(flashcardEvent: FlashcardOneShotEvent.error));
        success = false;
      }),
    );
    return success;
  }

  /// -------- CRUD FOR QUIZ SETS -------- ////
  /// -------- CRUD FOR QUIZ SETS -------- ////
  /// -------- CRUD FOR QUIZ SETS -------- ////

  void getAllQuizSets(String noteId) async {
    emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.loading));
    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final data = await _getNoteApiService.getAllQuizSets(noteId);
        _listQuizSets.clear();
        if (data.isNotEmpty) {
          _listQuizSets.addAll(data);
        }
        emit(
          state.copyWith(
            quizOneShotEvent: QuizOneShotEvent.success,
          ),
        );
      },
      doOnError: (exception) => Future(() {
        debugPrint('Error getting quiz sets: $exception');
        emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.error));
      }),
    );
  }

  Future<bool> deleteQuizSet(String setId) async {
    emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.loading));
    bool success = false;
    await runCubitCatching(
      handleLoading: false,
      action: () async {
        success = await _getNoteApiService.deleteQuizSet(setId: setId);
        if (success) {
          // Remove the deleted set from the local list
          _listQuizSets.removeWhere((set) => set.setId == setId);

          // If there are remaining sets, get the questions of the last set
          if (_listQuizSets.isNotEmpty) {
            final lastSetId = _listQuizSets.last.setId;
            final result = await _getNoteApiService.chooseQuizSet(lastSetId);
            _quizzes = result.questions;

            // Update quiz set info in local service
            await _localService.setQuizSetInfo(
              _noteBackendId,
              result.setId,
              result.name,
            );
          } else {
            _quizzes = [];
          }

          // Update the local note with the new quiz data
          final note = HiveService().noteBox.get(_noteLocalId);
          if (note != null) {
            final updatedNote = note.copyWith(
              quiz: _quizzes,
              // Also update quizSetDataModel
              quizSetDataModel: _listQuizSets.isNotEmpty
                  ? QuizSetDataModel(
                      setId: _listQuizSets.last.setId,
                      name: _listQuizSets.last.name,
                      difficulty: _listQuizSets.last.difficulty,
                      questionsCount: _listQuizSets.last.questionCount,
                    )
                  : QuizSetDataModel(
                      setId: '',
                      name: '',
                      difficulty: AppConstants.kDefaultDifficulty,
                    ),
            );
            await HiveService().createOrUpdateNote(_noteLocalId, updatedNote);
          }

          emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.success));
        } else {
          emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.error));
        }
      },
      doOnError: (exception) => Future(() {
        debugPrint('Error deleting quiz set: $exception');
        emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.error));
        success = false;
      }),
    );
    return success;
  }

  Future<bool> editQuizSet(String setId, String newName) async {
    emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.loading));
    bool success = false;
    await runCubitCatching(
      handleLoading: false,
      action: () async {
        final updatedSet = await _getNoteApiService.editQuizSet(setId, newName);

        // Update the set in the local list
        final index = _listQuizSets.indexWhere((set) => set.setId == setId);
        if (index != -1) {
          _listQuizSets[index] = updatedSet;
        }

        // Save to local service
        await _localService.setQuizSetInfo(
          _noteBackendId,
          setId,
          newName,
        );

        emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.success));
        success = true;
      },
      doOnError: (exception) => Future(() {
        debugPrint('Error editing quiz set: $exception');
        emit(state.copyWith(quizOneShotEvent: QuizOneShotEvent.error));
        success = false;
      }),
    );
    return success;
  }

  Future<bool> chooseFlashcardSet(String setId) async {
    emit(state.copyWith(chooseFlashcard: ChooseFlashcardOneShotEvent.loading));
    bool success = false;

    try {
      await runCubitCatching(
        handleLoading: false,
        action: () async {
          final result = await _getNoteApiService.chooseFlashcardSet(setId);
          _flashCards = result.cards;
          success = true;

          final note = HiveService().noteBox.get(_noteLocalId);
          if (note != null) {
            await _localService.setFlashcardSetInfo(
              _noteBackendId,
              result.setId,
              result.name,
            );

            final updatedNote = note.copyWith(
              flashCard: _flashCards,
              flashcardSetDataModel: FlashcardSetDataModel(
                setId: setId,
                name: result.name,
                difficulty: result.difficulty,
                cardsCount: result.cardsCount,
              ),
            );
            await HiveService().createOrUpdateNote(note.id, updatedNote);
          }
          emit(state.copyWith(
              chooseFlashcard: ChooseFlashcardOneShotEvent.success));
        },
        doOnError: (exception) => Future(() {
          debugPrint('Error choosing flashcard set: $exception');
          emit(state.copyWith(
              chooseFlashcard: ChooseFlashcardOneShotEvent.error));
          success = false;
        }),
      );
    } catch (e) {
      debugPrint('Unexpected error: $e');
      emit(state.copyWith(chooseFlashcard: ChooseFlashcardOneShotEvent.error));
      success = false;
    }

    return success;
  }

  Future<bool> chooseQuizSet(String setId) async {
    emit(state.copyWith(chooseQuiz: ChooseQuizOneShotEvent.loading));
    bool success = false;

    try {
      await runCubitCatching(
        handleLoading: false,
        action: () async {
          final result = await _getNoteApiService.chooseQuizSet(setId);
          _quizzes = result.questions;
          success = true;

          final note = HiveService().noteBox.get(_noteLocalId);
          if (note != null) {
            await _localService.setQuizSetInfo(
              _noteBackendId,
              result.setId,
              result.name,
            );

            final updatedNote = note.copyWith(
              quiz: _quizzes,
              quizSetDataModel: QuizSetDataModel(
                setId: setId,
                name: result.name,
                difficulty: result.difficulty,
                questionsCount: result.questionsCount,
              ),
            );
            await HiveService().createOrUpdateNote(note.id, updatedNote);
          }
          emit(state.copyWith(chooseQuiz: ChooseQuizOneShotEvent.success));
        },
        doOnError: (exception) => Future(() {
          debugPrint('Error choosing quiz set: $exception');
          emit(state.copyWith(chooseQuiz: ChooseQuizOneShotEvent.error));
          success = false;
        }),
      );
    } catch (e) {
      debugPrint('Unexpected error: $e');
      emit(state.copyWith(chooseQuiz: ChooseQuizOneShotEvent.error));
      success = false;
    }

    return success;
  }

  void setHasEdits(bool hasEdits) {
    emit(state.copyWith(hasEdits: hasEdits));
  }
}
