import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

/// Service class for handling folder-related operations
class FolderService {
  final CreateNoteApiServiceImpl _noteApiService;
  final FolderApiServiceImpl _folderApiService;
  static const String allNotesId = 'all_notes';

  /// Constructor with dependency injection
  FolderService({
    CreateNoteApiServiceImpl? noteApiService,
    FolderApiServiceImpl? folderApiService,
  })  : _noteApiService =
            noteApiService ?? GetIt.instance.get<CreateNoteApiServiceImpl>(),
        _folderApiService =
            folderApiService ?? GetIt.instance.get<FolderApiServiceImpl>();

  /// Creates a new folder and adds a note to it
  ///
  /// [folderName] - The name of the folder to create
  /// [note] - The note to add to the folder
  /// [choosenFolderName] - A ValueNotifier to update with the folder name
  Future<void> createFolderAndAddNote(String folderName, NoteModel note,
      ValueNotifier<String> choosenFolderName) async {
    // Create the folder on the backend
    final folder = await _folderApiService.createFolder(folderName);

    // Create the folder in local storage
    await HiveFolderService.createFolder(
        name: folderName, folderBackendId: folder.backendId);

    // Update the note with the new folder ID
    final updatedNote = note.copyWith(folderId: folder.backendId);

    // Update the note on the backend
    await _noteApiService.updateNote(
        backendNoteId: updatedNote.backendNoteId,
        folderId: updatedNote.folderId);

    // Update the note in local storage
    await HiveFolderService.addNoteToFolder(
        note: updatedNote, folderBackendId: folder.backendId, folder: folder);

    // Update the folder name in the UI
    choosenFolderName.value = folder.folderName;
  }

  /// Adds a note to a folder
  ///
  /// [folder] - The folder to add the note to
  /// [note] - The note to add to the folder
  /// [choosenFolderName] - A ValueNotifier to update with the folder name
  Future<void> addNoteToFolder(FolderModel folder, NoteModel note,
      ValueNotifier<String> choosenFolderName) async {
    try {
      // Show loading
      CommonDialogs.showLoadingDialog();

      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.note_detail_add_folder_success,
      );

      final bool isAllNotesFolder = folder.id == allNotesId;
      final String newFolderId = isAllNotesFolder ? '' : folder.backendId;

      if (newFolderId == note.folderId) {
        CommonDialogs.closeLoading();
        return;
      }

      // Update note's folder ID on the backend - BaseApiService will handle internet connection check
      await _noteApiService.updateNote(
        backendNoteId: note.backendNoteId,
        folderId: newFolderId,
      );

      // Update note's folder in local storage
      await HiveFolderService.addNoteToFolder(
        note: note.copyWith(folderId: newFolderId),
        folderBackendId: newFolderId,
        folder: folder,
      );

      // Update the folder name in the UI
      choosenFolderName.value =
          isAllNotesFolder ? S.current.add_folder : folder.folderName;

      CommonDialogs.closeLoading();
    } catch (e) {
      CommonDialogs.closeLoading();
      debugPrint('Error adding note to folder: $e');
      if (e is AppErrorException) {
        GetIt.instance.get<AppCubit>().emitError(e.error_key);
      }
      rethrow; // Rethrow to allow calling code to handle the error
    }
  }
}
