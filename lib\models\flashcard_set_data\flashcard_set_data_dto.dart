// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'flashcard_set_data_dto.freezed.dart';
part 'flashcard_set_data_dto.g.dart';

@freezed
class FlashcardSetDataDto with _$FlashcardSetDataDto {
  const factory FlashcardSetDataDto({
    @JsonKey(name: 'set_id') @Default('') String setId,
    @JsonKey(name: 'name') @Default('') String name,
    @Json<PERSON>ey(name: 'cards_count') @Default(0) int cardsCount,
    @Json<PERSON>ey(name: 'difficulty') @Default('') String difficulty,
  }) = _FlashcardSetDataDto;

  factory FlashcardSetDataDto.fromJson(Map<String, dynamic> json) =>
      _$FlashcardSetDataDtoFromJson(json);
}
