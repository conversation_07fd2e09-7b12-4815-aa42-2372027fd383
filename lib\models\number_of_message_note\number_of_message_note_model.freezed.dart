// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'number_of_message_note_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

NumberOfMessageNoteModel _$NumberOfMessageNoteModelFromJson(
    Map<String, dynamic> json) {
  return _NumberOfMessageNoteModel.fromJson(json);
}

/// @nodoc
mixin _$NumberOfMessageNoteModel {
  String get noteId => throw _privateConstructorUsedError;
  int get numberOfMessages => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NumberOfMessageNoteModelCopyWith<NumberOfMessageNoteModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NumberOfMessageNoteModelCopyWith<$Res> {
  factory $NumberOfMessageNoteModelCopyWith(NumberOfMessageNoteModel value,
          $Res Function(NumberOfMessageNoteModel) then) =
      _$NumberOfMessageNoteModelCopyWithImpl<$Res, NumberOfMessageNoteModel>;
  @useResult
  $Res call({String noteId, int numberOfMessages});
}

/// @nodoc
class _$NumberOfMessageNoteModelCopyWithImpl<$Res,
        $Val extends NumberOfMessageNoteModel>
    implements $NumberOfMessageNoteModelCopyWith<$Res> {
  _$NumberOfMessageNoteModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noteId = null,
    Object? numberOfMessages = null,
  }) {
    return _then(_value.copyWith(
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfMessages: null == numberOfMessages
          ? _value.numberOfMessages
          : numberOfMessages // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NumberOfMessageNoteModelImplCopyWith<$Res>
    implements $NumberOfMessageNoteModelCopyWith<$Res> {
  factory _$$NumberOfMessageNoteModelImplCopyWith(
          _$NumberOfMessageNoteModelImpl value,
          $Res Function(_$NumberOfMessageNoteModelImpl) then) =
      __$$NumberOfMessageNoteModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String noteId, int numberOfMessages});
}

/// @nodoc
class __$$NumberOfMessageNoteModelImplCopyWithImpl<$Res>
    extends _$NumberOfMessageNoteModelCopyWithImpl<$Res,
        _$NumberOfMessageNoteModelImpl>
    implements _$$NumberOfMessageNoteModelImplCopyWith<$Res> {
  __$$NumberOfMessageNoteModelImplCopyWithImpl(
      _$NumberOfMessageNoteModelImpl _value,
      $Res Function(_$NumberOfMessageNoteModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noteId = null,
    Object? numberOfMessages = null,
  }) {
    return _then(_$NumberOfMessageNoteModelImpl(
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfMessages: null == numberOfMessages
          ? _value.numberOfMessages
          : numberOfMessages // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NumberOfMessageNoteModelImpl implements _NumberOfMessageNoteModel {
  const _$NumberOfMessageNoteModelImpl(
      {this.noteId = '', this.numberOfMessages = 0});

  factory _$NumberOfMessageNoteModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$NumberOfMessageNoteModelImplFromJson(json);

  @override
  @JsonKey()
  final String noteId;
  @override
  @JsonKey()
  final int numberOfMessages;

  @override
  String toString() {
    return 'NumberOfMessageNoteModel(noteId: $noteId, numberOfMessages: $numberOfMessages)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NumberOfMessageNoteModelImpl &&
            (identical(other.noteId, noteId) || other.noteId == noteId) &&
            (identical(other.numberOfMessages, numberOfMessages) ||
                other.numberOfMessages == numberOfMessages));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, noteId, numberOfMessages);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NumberOfMessageNoteModelImplCopyWith<_$NumberOfMessageNoteModelImpl>
      get copyWith => __$$NumberOfMessageNoteModelImplCopyWithImpl<
          _$NumberOfMessageNoteModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NumberOfMessageNoteModelImplToJson(
      this,
    );
  }
}

abstract class _NumberOfMessageNoteModel implements NumberOfMessageNoteModel {
  const factory _NumberOfMessageNoteModel(
      {final String noteId,
      final int numberOfMessages}) = _$NumberOfMessageNoteModelImpl;

  factory _NumberOfMessageNoteModel.fromJson(Map<String, dynamic> json) =
      _$NumberOfMessageNoteModelImpl.fromJson;

  @override
  String get noteId;
  @override
  int get numberOfMessages;
  @override
  @JsonKey(ignore: true)
  _$$NumberOfMessageNoteModelImplCopyWith<_$NumberOfMessageNoteModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
