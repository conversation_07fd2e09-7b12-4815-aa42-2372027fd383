// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flashcard_set_data_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FlashcardSetDataDto _$FlashcardSetDataDtoFromJson(Map<String, dynamic> json) {
  return _FlashcardSetDataDto.fromJson(json);
}

/// @nodoc
mixin _$FlashcardSetDataDto {
  @JsonKey(name: 'set_id')
  String get setId => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'cards_count')
  int get cardsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'difficulty')
  String get difficulty => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FlashcardSetDataDtoCopyWith<FlashcardSetDataDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlashcardSetDataDtoCopyWith<$Res> {
  factory $FlashcardSetDataDtoCopyWith(
          FlashcardSetDataDto value, $Res Function(FlashcardSetDataDto) then) =
      _$FlashcardSetDataDtoCopyWithImpl<$Res, FlashcardSetDataDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'cards_count') int cardsCount,
      @JsonKey(name: 'difficulty') String difficulty});
}

/// @nodoc
class _$FlashcardSetDataDtoCopyWithImpl<$Res, $Val extends FlashcardSetDataDto>
    implements $FlashcardSetDataDtoCopyWith<$Res> {
  _$FlashcardSetDataDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? name = null,
    Object? cardsCount = null,
    Object? difficulty = null,
  }) {
    return _then(_value.copyWith(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      cardsCount: null == cardsCount
          ? _value.cardsCount
          : cardsCount // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlashcardSetDataDtoImplCopyWith<$Res>
    implements $FlashcardSetDataDtoCopyWith<$Res> {
  factory _$$FlashcardSetDataDtoImplCopyWith(_$FlashcardSetDataDtoImpl value,
          $Res Function(_$FlashcardSetDataDtoImpl) then) =
      __$$FlashcardSetDataDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'cards_count') int cardsCount,
      @JsonKey(name: 'difficulty') String difficulty});
}

/// @nodoc
class __$$FlashcardSetDataDtoImplCopyWithImpl<$Res>
    extends _$FlashcardSetDataDtoCopyWithImpl<$Res, _$FlashcardSetDataDtoImpl>
    implements _$$FlashcardSetDataDtoImplCopyWith<$Res> {
  __$$FlashcardSetDataDtoImplCopyWithImpl(_$FlashcardSetDataDtoImpl _value,
      $Res Function(_$FlashcardSetDataDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? name = null,
    Object? cardsCount = null,
    Object? difficulty = null,
  }) {
    return _then(_$FlashcardSetDataDtoImpl(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      cardsCount: null == cardsCount
          ? _value.cardsCount
          : cardsCount // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FlashcardSetDataDtoImpl implements _FlashcardSetDataDto {
  const _$FlashcardSetDataDtoImpl(
      {@JsonKey(name: 'set_id') this.setId = '',
      @JsonKey(name: 'name') this.name = '',
      @JsonKey(name: 'cards_count') this.cardsCount = 0,
      @JsonKey(name: 'difficulty') this.difficulty = ''});

  factory _$FlashcardSetDataDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FlashcardSetDataDtoImplFromJson(json);

  @override
  @JsonKey(name: 'set_id')
  final String setId;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'cards_count')
  final int cardsCount;
  @override
  @JsonKey(name: 'difficulty')
  final String difficulty;

  @override
  String toString() {
    return 'FlashcardSetDataDto(setId: $setId, name: $name, cardsCount: $cardsCount, difficulty: $difficulty)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlashcardSetDataDtoImpl &&
            (identical(other.setId, setId) || other.setId == setId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.cardsCount, cardsCount) ||
                other.cardsCount == cardsCount) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, setId, name, cardsCount, difficulty);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FlashcardSetDataDtoImplCopyWith<_$FlashcardSetDataDtoImpl> get copyWith =>
      __$$FlashcardSetDataDtoImplCopyWithImpl<_$FlashcardSetDataDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FlashcardSetDataDtoImplToJson(
      this,
    );
  }
}

abstract class _FlashcardSetDataDto implements FlashcardSetDataDto {
  const factory _FlashcardSetDataDto(
          {@JsonKey(name: 'set_id') final String setId,
          @JsonKey(name: 'name') final String name,
          @JsonKey(name: 'cards_count') final int cardsCount,
          @JsonKey(name: 'difficulty') final String difficulty}) =
      _$FlashcardSetDataDtoImpl;

  factory _FlashcardSetDataDto.fromJson(Map<String, dynamic> json) =
      _$FlashcardSetDataDtoImpl.fromJson;

  @override
  @JsonKey(name: 'set_id')
  String get setId;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'cards_count')
  int get cardsCount;
  @override
  @JsonKey(name: 'difficulty')
  String get difficulty;
  @override
  @JsonKey(ignore: true)
  _$$FlashcardSetDataDtoImplCopyWith<_$FlashcardSetDataDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
