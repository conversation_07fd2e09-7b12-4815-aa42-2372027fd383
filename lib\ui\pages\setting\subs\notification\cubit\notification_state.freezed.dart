// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$NotificationState {
  NotificationOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  List<ReminderModel> get reminderModels => throw _privateConstructorUsedError;
  bool get isNotificationEnabled => throw _privateConstructorUsedError;
  bool get isNoteReadyNotificationEnabled => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $NotificationStateCopyWith<NotificationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationStateCopyWith<$Res> {
  factory $NotificationStateCopyWith(
          NotificationState value, $Res Function(NotificationState) then) =
      _$NotificationStateCopyWithImpl<$Res, NotificationState>;
  @useResult
  $Res call(
      {NotificationOneShotEvent oneShotEvent,
      List<ReminderModel> reminderModels,
      bool isNotificationEnabled,
      bool isNoteReadyNotificationEnabled});
}

/// @nodoc
class _$NotificationStateCopyWithImpl<$Res, $Val extends NotificationState>
    implements $NotificationStateCopyWith<$Res> {
  _$NotificationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? reminderModels = null,
    Object? isNotificationEnabled = null,
    Object? isNoteReadyNotificationEnabled = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as NotificationOneShotEvent,
      reminderModels: null == reminderModels
          ? _value.reminderModels
          : reminderModels // ignore: cast_nullable_to_non_nullable
              as List<ReminderModel>,
      isNotificationEnabled: null == isNotificationEnabled
          ? _value.isNotificationEnabled
          : isNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isNoteReadyNotificationEnabled: null == isNoteReadyNotificationEnabled
          ? _value.isNoteReadyNotificationEnabled
          : isNoteReadyNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationStateImplCopyWith<$Res>
    implements $NotificationStateCopyWith<$Res> {
  factory _$$NotificationStateImplCopyWith(_$NotificationStateImpl value,
          $Res Function(_$NotificationStateImpl) then) =
      __$$NotificationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {NotificationOneShotEvent oneShotEvent,
      List<ReminderModel> reminderModels,
      bool isNotificationEnabled,
      bool isNoteReadyNotificationEnabled});
}

/// @nodoc
class __$$NotificationStateImplCopyWithImpl<$Res>
    extends _$NotificationStateCopyWithImpl<$Res, _$NotificationStateImpl>
    implements _$$NotificationStateImplCopyWith<$Res> {
  __$$NotificationStateImplCopyWithImpl(_$NotificationStateImpl _value,
      $Res Function(_$NotificationStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? reminderModels = null,
    Object? isNotificationEnabled = null,
    Object? isNoteReadyNotificationEnabled = null,
  }) {
    return _then(_$NotificationStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as NotificationOneShotEvent,
      reminderModels: null == reminderModels
          ? _value._reminderModels
          : reminderModels // ignore: cast_nullable_to_non_nullable
              as List<ReminderModel>,
      isNotificationEnabled: null == isNotificationEnabled
          ? _value.isNotificationEnabled
          : isNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isNoteReadyNotificationEnabled: null == isNoteReadyNotificationEnabled
          ? _value.isNoteReadyNotificationEnabled
          : isNoteReadyNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$NotificationStateImpl implements _NotificationState {
  const _$NotificationStateImpl(
      {this.oneShotEvent = NotificationOneShotEvent.none,
      final List<ReminderModel> reminderModels = const [],
      this.isNotificationEnabled = false,
      this.isNoteReadyNotificationEnabled = false})
      : _reminderModels = reminderModels;

  @override
  @JsonKey()
  final NotificationOneShotEvent oneShotEvent;
  final List<ReminderModel> _reminderModels;
  @override
  @JsonKey()
  List<ReminderModel> get reminderModels {
    if (_reminderModels is EqualUnmodifiableListView) return _reminderModels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reminderModels);
  }

  @override
  @JsonKey()
  final bool isNotificationEnabled;
  @override
  @JsonKey()
  final bool isNoteReadyNotificationEnabled;

  @override
  String toString() {
    return 'NotificationState(oneShotEvent: $oneShotEvent, reminderModels: $reminderModels, isNotificationEnabled: $isNotificationEnabled, isNoteReadyNotificationEnabled: $isNoteReadyNotificationEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._reminderModels, _reminderModels) &&
            (identical(other.isNotificationEnabled, isNotificationEnabled) ||
                other.isNotificationEnabled == isNotificationEnabled) &&
            (identical(other.isNoteReadyNotificationEnabled,
                    isNoteReadyNotificationEnabled) ||
                other.isNoteReadyNotificationEnabled ==
                    isNoteReadyNotificationEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      const DeepCollectionEquality().hash(_reminderModels),
      isNotificationEnabled,
      isNoteReadyNotificationEnabled);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationStateImplCopyWith<_$NotificationStateImpl> get copyWith =>
      __$$NotificationStateImplCopyWithImpl<_$NotificationStateImpl>(
          this, _$identity);
}

abstract class _NotificationState implements NotificationState {
  const factory _NotificationState(
      {final NotificationOneShotEvent oneShotEvent,
      final List<ReminderModel> reminderModels,
      final bool isNotificationEnabled,
      final bool isNoteReadyNotificationEnabled}) = _$NotificationStateImpl;

  @override
  NotificationOneShotEvent get oneShotEvent;
  @override
  List<ReminderModel> get reminderModels;
  @override
  bool get isNotificationEnabled;
  @override
  bool get isNoteReadyNotificationEnabled;
  @override
  @JsonKey(ignore: true)
  _$$NotificationStateImplCopyWith<_$NotificationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
