import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:note_x/lib.dart';
import '../../../base/base_page_state.dart';
import 'cubit/purchase_state.dart';

class PurchasePage extends StatefulWidget {
  static const String routeName = 'iap';
  final PurchasePageFrom from;
  final bool isUiSpecial;

  const PurchasePage({Key? key, required this.from, this.isUiSpecial = false})
      : super(key: key);

  @override
  State<PurchasePage> createState() => _PurchasePageState();
}

class _PurchasePageState
    extends BasePageStateDelegate<PurchasePage, PurchaseCubit> {
  @override
  void initState() {
    super.initState();
    cubit.initOfferings();
    cubit.trackingShowPurchase(widget.from, widget.isUiSpecial);
    cubit.setVisibilityCloseButton();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<PurchaseCubit, PurchaseState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != PurchaseOneShotEvent.none,
      listener: _handlePurchaseEvents,
      child: child,
    );
  }

  void _handlePurchaseEvents(BuildContext context, PurchaseState state) {
    CommonDialogs.closeLoading();
    switch (state.oneShotEvent) {
      case PurchaseOneShotEvent.purchaseSuccess:
        _showPurchaseSuccessDialog(context);
        break;
      case PurchaseOneShotEvent.purchaseFail:
        CommonDialogs.showErrorDialog(context,
            title: S.current.purchase_init_fail);
        break;
      case PurchaseOneShotEvent.onNotChooseAnOptionYet:
        break;
      case PurchaseOneShotEvent.none:
        break;
      case PurchaseOneShotEvent.onNothingToRestore:
        showBlackCupertinoDialog(
            context: context,
            confirmButton: S.current.ok,
            title: S.current.restore_fail_title,
            message: S.current.restore_fail_message);
        break;
      case PurchaseOneShotEvent.restoreSuccess:
        CommonDialogs.showToast(S.current.restore_success_title);
        cubit.popOnPurchaseSuccess(context);
    }
    cubit.resetEnumState();
  }

  void _showPurchaseSuccessDialog(BuildContext context) {
    CommonDialogs.showCongratulationDialog(
      context,
      S.current.payment_successfully,
      S.current.content_payment_successfully,
      S.current.ok,
      () => cubit.popOnPurchaseSuccess(context),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      body: widget.isUiSpecial
          ? PurchaseSpecial(
              from: widget.from,
              cubit: cubit,
            )
          : PurchaseLayoutB(
              from: widget.from,
              cubit: cubit,
            ),
    );
  }

  @override
  void dispose() {
    cubit.close();
    super.dispose();
  }
}

enum PurchasePageFrom {
  iapSplash,
  iapHome,
  iapSetting,
  iapCreateNote,
  iapRecordScreen,
  iapRetrySummaryScreen,
  iapOnboarding,
  iapChat,
  iapTranslateNote,
  iapCreateShorts,
  iapDialogSaleOff,
  iapPayMain,
  iapBannerHome,
}
