import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class TemplateWidget extends StatefulWidget {
  final List<ChatTemplateModel> templates;
  final Function(ChatTemplateModel?) onTap;
  final String? selectedTemplateId;

  const TemplateWidget({
    super.key,
    required this.templates,
    required this.onTap,
    this.selectedTemplateId,
  });

  @override
  State<TemplateWidget> createState() => _TemplateWidgetState();
}

class _TemplateWidgetState extends State<TemplateWidget> {
  @override
  Widget build(BuildContext context) {
    return widget.templates.isNotEmpty
        ? Container(
            color: Colors.transparent,
            height: 36.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.templates.length,
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              itemBuilder: (context, index) {
                final template = widget.templates[index];
                final isSelected = template.id == widget.selectedTemplateId;

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: GestureDetector(
                    onTap: () {
                      if (isSelected) {
                        widget.onTap(null);
                      } else {
                        widget.onTap(template);
                      }
                    },
                    child: Chip(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(100.r),
                      ),
                      side:
                          const BorderSide(width: 0, color: Colors.transparent),
                      avatar: SvgPicture.asset(
                        getTemplateType(template.type).image,
                        colorFilter: ColorFilter.mode(
                          getTemplateType(template.type).color(context),
                          BlendMode.srcIn,
                        ),
                        width: 16.w,
                        height: 16.w,
                      ),
                      label: Text(
                        template.name,
                        style: TextStyle(
                          color: isSelected
                              ? getTemplateType(template.type).color(context)
                              : context.colorScheme.mainPrimary,
                          fontSize: context.isTablet ? 14 : 12.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      backgroundColor: isSelected
                          ? getTemplateType(template.type)
                              .color(context)
                              .withOpacity(0.1)
                          : context.colorScheme.mainNeutral,
                    ),
                  ),
                );
              },
            ),
          )
        : const SizedBox.shrink();
  }
}
