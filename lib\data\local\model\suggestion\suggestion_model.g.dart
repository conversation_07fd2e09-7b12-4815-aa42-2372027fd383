// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'suggestion_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SuggestionModelAdapter extends TypeAdapter<SuggestionModel> {
  @override
  final int typeId = 15;

  @override
  SuggestionModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SuggestionModel(
      question: fields[0] == null ? '' : fields[0] as String,
      type: fields[1] == null ? '' : fields[1] as String,
      difficulty: fields[2] == null ? '' : fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, SuggestionModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.question)
      ..writeByte(1)
      ..write(obj.type)
      ..writeByte(2)
      ..write(obj.difficulty);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SuggestionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
