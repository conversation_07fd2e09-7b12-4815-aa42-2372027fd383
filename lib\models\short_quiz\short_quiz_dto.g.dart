// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'short_quiz_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShortQuizDtoImpl _$$ShortQuizDtoImplFromJson(Map<String, dynamic> json) =>
    _$ShortQuizDtoImpl(
      noteId: json['note_id'] as String? ?? '',
      quizVideoUrl: json['quiz_video_url'] as String? ?? '',
      taskId: json['task_id'] as String? ?? '',
      userId: json['user_id'] as String? ?? '',
      redisData: json['redis_data'] as String? ?? '',
      duration: (json['duration'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$ShortQuizDtoImplToJson(_$ShortQuizDtoImpl instance) =>
    <String, dynamic>{
      'note_id': instance.noteId,
      'quiz_video_url': instance.quizVideoUrl,
      'task_id': instance.taskId,
      'user_id': instance.userId,
      'redis_data': instance.redisData,
      'duration': instance.duration,
    };
