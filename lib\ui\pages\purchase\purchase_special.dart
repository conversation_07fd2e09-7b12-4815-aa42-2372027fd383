import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'cubit/purchase_state.dart';

class PurchaseSpecial extends StatefulWidget {
  static const String routeName = 'iap_special';
  final PurchasePageFrom from;
  final PurchaseCubit cubit;

  const PurchaseSpecial({Key? key, required this.from, required this.cubit})
      : super(key: key);

  @override
  State<PurchaseSpecial> createState() => _PurchaseSpecialState();
}

class _PurchaseSpecialState extends State<PurchaseSpecial> {
  @override
  Widget build(BuildContext context) {
    return AdaptiveOrientationBase(
      portraitBuilder: (context, constraints) {
        return SafeArea(
          top: false,
          bottom: false,
          child: Stack(
            children: [
              _buildCloseButton(),
              Positioned(
                top: MediaQuery.of(context).padding.top + 32.h,
                left: 0,
                right: 0,
                child: CommonText(
                  S.current.special_gift,
                  style: TextStyle(
                    fontSize: context.isTablet ? 40 : 40.sp,
                    fontWeight: FontWeight.w600,
                    color: context.colorScheme.mainPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              _buildBottomSection(),
            ],
          ),
        );
      },
      landscapeBuilder: (context, constraints) {
        return buildLandScapeTablet();
      },
    );
  }

  Widget _buildSuggestIap(Package? package) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: S.current.day_free_trial_access_all_features,
              style: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 14 : 14.sp,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: package?.storeProduct.priceString ?? '',
              style: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 14 : 14.sp,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
              ),
            ),
            TextSpan(
              text: S.current.per_year,
              style: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 14 : 14.sp,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget buildLandScapeTablet() {
    return SafeArea(
      top: false,
      bottom: true,
      child: Stack(
        children: [
          Positioned(
            top: MediaQuery.of(context).padding.top + 32.h,
            left: 0,
            right: 0,
            child: CommonText(
              S.current.special_gift,
              style: TextStyle(
                fontSize: context.isTablet ? 40 : 40.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          _buildBottomSection(),
          _buildCloseButton(),
        ],
      ),
    );
  }

  Widget _buildCloseButton() {
    return BlocBuilder<PurchaseCubit, PurchaseState>(
      bloc: widget.cubit,
      buildWhen: (previous, current) =>
          previous.isVisibleClose != current.isVisibleClose ||
          previous.listPackage != current.listPackage ||
          previous.selectedIndex != current.selectedIndex,
      builder: (context, state) {
        return Align(
          alignment: Alignment.topLeft,
          child: Padding(
            padding: EdgeInsets.only(
              left: 20.w,
              top: MediaQuery.of(context).padding.top,
            ),
            child: state.isVisibleClose
                ? GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      HapticFeedback.mediumImpact();
                      widget.cubit.onCloseButtonPressed(context);
                    },
                    child: SvgPicture.asset(
                      Assets.icons.icCloseCircle,
                      width: 32.w,
                      height: 32.h,
                      colorFilter: ColorFilter.mode(
                        context.colorScheme.mainGray,
                        BlendMode.srcIn,
                      ),
                    ),
                  )
                : SizedBox(
                    width: 32.w,
                    height: 32.h,
                  ),
          ),
        );
      },
    );
  }

  Widget _buildBottomSection() {
    return BlocBuilder<PurchaseCubit, PurchaseState>(
      bloc: widget.cubit,
      buildWhen: (previous, current) =>
          previous.isEnableSwitch != current.isEnableSwitch,
      builder: (context, state) {
        return Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: context.isTablet ? 460.h : null,
                    child: Lottie.asset(
                      Assets.videos.saleOff,
                      repeat: false,
                    ),
                  ),
                  AppConstants.kSpacingItem60,
                  _buildSuggestIap(widget.cubit.getRcYearTrialPackage),
                  AppConstants.kSpacingItem8,
                  _buildPurchaseButton(),
                  AppConstants.kSpacingItem8,
                  Center(
                    child: CommonText(
                      S.current.auto_renewal,
                      style: TextStyle(
                        fontSize: context.isTablet ? 14 : 14.sp,
                        fontWeight: FontWeight.w400,
                        color: context.colorScheme.mainPrimary,
                      ),
                    ),
                  ),
                  TermPrivacyIosWidget(
                    purchasePageFrom: widget.from,
                    onRestorePurchase:
                        Func0(() => widget.cubit.onRestorePurchase()),
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).padding.bottom,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPurchaseButton() {
    return BlocBuilder<PurchaseCubit, PurchaseState>(
      bloc: widget.cubit,
      buildWhen: (previous, current) =>
          previous.listPackage != current.listPackage ||
          previous.selectedIndex != current.selectedIndex,
      builder: (context, state) {
        return AnimatedPulse(
          child: Stack(
            children: [
              AppCommonButton(
                height: context.isLandscape ? 76.h : 60.h,
                width: double.infinity,
                gradient: const LinearGradient(
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                  colors: [
                    Color(0xFFCB93FF),
                    Color(0xFF6587FF),
                    Color(0xFF008CFF),
                    Color(0xFF6BD7FF),
                  ],
                  transform: GradientRotation(pi),
                ),
                borderRadius: BorderRadius.circular(48.r),
                onPressed: () {
                  if (widget.cubit.getRcYearTrialPackage == null) {
                    CommonDialogs.showToast(
                        'We couldn\'t connect to the App Store/Play Store at this time. Please close this screen or relaunch the application to try again.');
                  } else {
                    HapticFeedback.mediumImpact();
                    widget.cubit.purchaseProduct(isTrial: true);
                    AnalyticsService.logAnalyticsEventNoParam(
                        eventName: EventName.iap_continue);
                  }
                },
                textWidget: CommonText(
                  "${S.current.redeem_7_days_for_0} ${widget.cubit.getRcYearTrialPackage?.storeProduct.currencyCode ?? ''}",
                  style: TextStyle(
                    fontSize: context.isTablet ? 20 : 20.sp,
                    color: context.colorScheme.themeWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(48.r),
                  child: IgnorePointer(
                    child: Lottie.asset(
                      Assets.videos.iapBtn,
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
