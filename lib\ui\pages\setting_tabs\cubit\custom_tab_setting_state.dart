import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';

import '../../../../utils/enum/tab_type.dart';

part 'custom_tab_setting_state.freezed.dart';

@freezed
class CustomTabSettingState extends BaseState with _$CustomTabSettingState {
  const factory CustomTabSettingState({
    @Default([]) List<TabType> selectedTabs,
    @Default([]) List<TabType> allTabs,
    @Default([]) List<TabType> alwaysSelected,
    @Default(false) bool isShowUpdateDialog,
    @Default(false) bool isSaveButtonEnabled,
  }) = _CustomTabSettingState;
}
