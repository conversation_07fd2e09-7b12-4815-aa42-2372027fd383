import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';
import 'package:pdfx/pdfx.dart';

class PdfPageThumbnail extends StatefulWidget {
  final String pdfPath;
  final int pageNumber;
  final bool isSelected;
  final Function(int) onPageTap;
  final double width;
  final double height;

  const PdfPageThumbnail({
    Key? key,
    required this.pdfPath,
    required this.pageNumber,
    this.isSelected = false,
    required this.onPageTap,
    this.width = 48,
    this.height = 72,
  }) : super(key: key);

  @override
  State<PdfPageThumbnail> createState() => _PdfPageThumbnailState();
}

class _PdfPageThumbnailState extends State<PdfPageThumbnail> {
  PdfDocument? _document;
  late Future<PdfPageImage?> _pageImage;

  @override
  void initState() {
    super.initState();
    _pageImage = _loadPageImage();
  }

  Future<PdfPageImage?> _loadPageImage() async {
    _document = await PdfDocument.openFile(widget.pdfPath);
    final page = await _document!.getPage(widget.pageNumber);
    final image = await page.render(
      width: widget.width * 4,
      height: widget.height * 4,
      format: PdfPageImageFormat.png,
      backgroundColor: '#FFFFFF',
    );
    await page.close();
    return image;
  }

  @override
  void dispose() {
    _document?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => widget.onPageTap(widget.pageNumber),
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.isSelected
                ? context.colorScheme.mainBlue
                : Colors.transparent,
            width: widget.isSelected ? 3 : 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          alignment: Alignment.center,
          fit: StackFit.expand,
          children: [
            FutureBuilder<PdfPageImage?>(
              future: _pageImage,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  if (snapshot.hasData && snapshot.data != null) {
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.memory(
                        snapshot.data!.bytes,
                        width: widget.width,
                        height: widget.height,
                        fit: BoxFit.cover,
                      ),
                    );
                  } else {
                    return const Center(child: Text('Failed to load PDF page'));
                  }
                } else {
                  return const Center(child: CupertinoActivityIndicator());
                }
              },
            ),
            if (widget.isSelected)
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    decoration: BoxDecoration(
                      color: context.colorScheme.mainPrimary.withOpacity(0.25),
                    ),
                  ),
                ),
              ),
            Positioned(
              left: 6,
              bottom: 6,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: context.colorScheme.mainNeutral,
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Text(
                  widget.pageNumber.toString().padLeft(2, '0'),
                  style: TextStyle(
                    color: context.colorScheme.mainPrimary,
                    fontWeight: FontWeight.w500,
                    fontSize: context.isTablet ? 12 : 8.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
