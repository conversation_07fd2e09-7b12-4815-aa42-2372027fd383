// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'note_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

NoteDto _$NoteDtoFromJson(Map<String, dynamic> json) {
  return _NoteDto.fromJson(json);
}

/// @nodoc
mixin _$NoteDto {
  @JsonKey(name: 'note_id')
  String get noteId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user')
  String get userId => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'sub_title')
  String get subTitle => throw _privateConstructorUsedError;
  List<String> get hashtags => throw _privateConstructorUsedError;
  String get summary => throw _privateConstructorUsedError;
  String get transcript => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'youtube_url')
  String get youtubeUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'youtube_id')
  String get youtubeId => throw _privateConstructorUsedError;
  List<QuizDetailDto> get quiz => throw _privateConstructorUsedError;
  @JsonKey(name: 'flashcards')
  List<FlashCarDetailDto> get flashCards => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_name')
  String get userName => throw _privateConstructorUsedError;
  String get views => throw _privateConstructorUsedError;
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson => throw _privateConstructorUsedError;
  @JsonKey(name: 'audio_url')
  String get audioUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'folder_id')
  String get folderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'mindmap')
  String get mindMap => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  String get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'duration')
  double get duration => throw _privateConstructorUsedError;
  @JsonKey(name: 'document_url')
  String get documentUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_feedback_submitted')
  bool get isFeedbackSubmitted => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_insufficient_content')
  bool get isInsufficientContent => throw _privateConstructorUsedError;
  @JsonKey(name: 'clip_durations')
  List<int> get clipDurations => throw _privateConstructorUsedError;
  List<VoiceDto> get voices => throw _privateConstructorUsedError;
  @JsonKey(name: 'web_url')
  String get webUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_public')
  bool get isPublic => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_password_protected')
  bool get isPasswordProtected => throw _privateConstructorUsedError;
  @JsonKey(name: 'share_password')
  String get sharePassword => throw _privateConstructorUsedError;
  @JsonKey(name: 'share_link')
  String get shareLink => throw _privateConstructorUsedError;
  @JsonKey(name: 'suggestions')
  List<SuggestionDto> get suggestions => throw _privateConstructorUsedError;
  @JsonKey(name: 'quiz_sets')
  List<QuizSetsDto> get quizSets => throw _privateConstructorUsedError;
  @JsonKey(name: 'flashcard_sets')
  List<FlashcardSetsDto> get flashcardSets =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'flashcard_set_data')
  FlashcardSetDataDto? get flashcardSetData =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'quiz_set_data')
  QuizSetDataDto? get quizSetDataDto => throw _privateConstructorUsedError;
  @JsonKey(name: 'save_to_notes')
  List<ChatMessageDto> get saveToNotes => throw _privateConstructorUsedError;
  @JsonKey(name: 'slide_url')
  String get slideUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'slide_pdf_url')
  String get slidePdfUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'chat_template_suggestions')
  List<ChatTemplateDto> get chatTemplateSuggestions =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NoteDtoCopyWith<NoteDto> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NoteDtoCopyWith<$Res> {
  factory $NoteDtoCopyWith(NoteDto value, $Res Function(NoteDto) then) =
      _$NoteDtoCopyWithImpl<$Res, NoteDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'note_id') String noteId,
      @JsonKey(name: 'user') String userId,
      String title,
      @JsonKey(name: 'sub_title') String subTitle,
      List<String> hashtags,
      String summary,
      String transcript,
      String type,
      @JsonKey(name: 'youtube_url') String youtubeUrl,
      @JsonKey(name: 'youtube_id') String youtubeId,
      List<QuizDetailDto> quiz,
      @JsonKey(name: 'flashcards') List<FlashCarDetailDto> flashCards,
      @JsonKey(name: 'user_name') String userName,
      String views,
      @JsonKey(name: 'transcript_json') List<TranscriptDto> transcriptJson,
      @JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'folder_id') String folderId,
      @JsonKey(name: 'mindmap') String mindMap,
      @JsonKey(name: 'createdAt') String createdAt,
      @JsonKey(name: 'duration') double duration,
      @JsonKey(name: 'document_url') String documentUrl,
      @JsonKey(name: 'is_feedback_submitted') bool isFeedbackSubmitted,
      @JsonKey(name: 'is_insufficient_content') bool isInsufficientContent,
      @JsonKey(name: 'clip_durations') List<int> clipDurations,
      List<VoiceDto> voices,
      @JsonKey(name: 'web_url') String webUrl,
      @JsonKey(name: 'is_public') bool isPublic,
      @JsonKey(name: 'is_password_protected') bool isPasswordProtected,
      @JsonKey(name: 'share_password') String sharePassword,
      @JsonKey(name: 'share_link') String shareLink,
      @JsonKey(name: 'suggestions') List<SuggestionDto> suggestions,
      @JsonKey(name: 'quiz_sets') List<QuizSetsDto> quizSets,
      @JsonKey(name: 'flashcard_sets') List<FlashcardSetsDto> flashcardSets,
      @JsonKey(name: 'flashcard_set_data')
      FlashcardSetDataDto? flashcardSetData,
      @JsonKey(name: 'quiz_set_data') QuizSetDataDto? quizSetDataDto,
      @JsonKey(name: 'save_to_notes') List<ChatMessageDto> saveToNotes,
      @JsonKey(name: 'slide_url') String slideUrl,
      @JsonKey(name: 'slide_pdf_url') String slidePdfUrl,
      @JsonKey(name: 'chat_template_suggestions')
      List<ChatTemplateDto> chatTemplateSuggestions});

  $FlashcardSetDataDtoCopyWith<$Res>? get flashcardSetData;
  $QuizSetDataDtoCopyWith<$Res>? get quizSetDataDto;
}

/// @nodoc
class _$NoteDtoCopyWithImpl<$Res, $Val extends NoteDto>
    implements $NoteDtoCopyWith<$Res> {
  _$NoteDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noteId = null,
    Object? userId = null,
    Object? title = null,
    Object? subTitle = null,
    Object? hashtags = null,
    Object? summary = null,
    Object? transcript = null,
    Object? type = null,
    Object? youtubeUrl = null,
    Object? youtubeId = null,
    Object? quiz = null,
    Object? flashCards = null,
    Object? userName = null,
    Object? views = null,
    Object? transcriptJson = null,
    Object? audioUrl = null,
    Object? folderId = null,
    Object? mindMap = null,
    Object? createdAt = null,
    Object? duration = null,
    Object? documentUrl = null,
    Object? isFeedbackSubmitted = null,
    Object? isInsufficientContent = null,
    Object? clipDurations = null,
    Object? voices = null,
    Object? webUrl = null,
    Object? isPublic = null,
    Object? isPasswordProtected = null,
    Object? sharePassword = null,
    Object? shareLink = null,
    Object? suggestions = null,
    Object? quizSets = null,
    Object? flashcardSets = null,
    Object? flashcardSetData = freezed,
    Object? quizSetDataDto = freezed,
    Object? saveToNotes = null,
    Object? slideUrl = null,
    Object? slidePdfUrl = null,
    Object? chatTemplateSuggestions = null,
  }) {
    return _then(_value.copyWith(
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      subTitle: null == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String,
      hashtags: null == hashtags
          ? _value.hashtags
          : hashtags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as String,
      transcript: null == transcript
          ? _value.transcript
          : transcript // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      youtubeUrl: null == youtubeUrl
          ? _value.youtubeUrl
          : youtubeUrl // ignore: cast_nullable_to_non_nullable
              as String,
      youtubeId: null == youtubeId
          ? _value.youtubeId
          : youtubeId // ignore: cast_nullable_to_non_nullable
              as String,
      quiz: null == quiz
          ? _value.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as List<QuizDetailDto>,
      flashCards: null == flashCards
          ? _value.flashCards
          : flashCards // ignore: cast_nullable_to_non_nullable
              as List<FlashCarDetailDto>,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      views: null == views
          ? _value.views
          : views // ignore: cast_nullable_to_non_nullable
              as String,
      transcriptJson: null == transcriptJson
          ? _value.transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptDto>,
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      folderId: null == folderId
          ? _value.folderId
          : folderId // ignore: cast_nullable_to_non_nullable
              as String,
      mindMap: null == mindMap
          ? _value.mindMap
          : mindMap // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as double,
      documentUrl: null == documentUrl
          ? _value.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isFeedbackSubmitted: null == isFeedbackSubmitted
          ? _value.isFeedbackSubmitted
          : isFeedbackSubmitted // ignore: cast_nullable_to_non_nullable
              as bool,
      isInsufficientContent: null == isInsufficientContent
          ? _value.isInsufficientContent
          : isInsufficientContent // ignore: cast_nullable_to_non_nullable
              as bool,
      clipDurations: null == clipDurations
          ? _value.clipDurations
          : clipDurations // ignore: cast_nullable_to_non_nullable
              as List<int>,
      voices: null == voices
          ? _value.voices
          : voices // ignore: cast_nullable_to_non_nullable
              as List<VoiceDto>,
      webUrl: null == webUrl
          ? _value.webUrl
          : webUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isPublic: null == isPublic
          ? _value.isPublic
          : isPublic // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordProtected: null == isPasswordProtected
          ? _value.isPasswordProtected
          : isPasswordProtected // ignore: cast_nullable_to_non_nullable
              as bool,
      sharePassword: null == sharePassword
          ? _value.sharePassword
          : sharePassword // ignore: cast_nullable_to_non_nullable
              as String,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
      suggestions: null == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<SuggestionDto>,
      quizSets: null == quizSets
          ? _value.quizSets
          : quizSets // ignore: cast_nullable_to_non_nullable
              as List<QuizSetsDto>,
      flashcardSets: null == flashcardSets
          ? _value.flashcardSets
          : flashcardSets // ignore: cast_nullable_to_non_nullable
              as List<FlashcardSetsDto>,
      flashcardSetData: freezed == flashcardSetData
          ? _value.flashcardSetData
          : flashcardSetData // ignore: cast_nullable_to_non_nullable
              as FlashcardSetDataDto?,
      quizSetDataDto: freezed == quizSetDataDto
          ? _value.quizSetDataDto
          : quizSetDataDto // ignore: cast_nullable_to_non_nullable
              as QuizSetDataDto?,
      saveToNotes: null == saveToNotes
          ? _value.saveToNotes
          : saveToNotes // ignore: cast_nullable_to_non_nullable
              as List<ChatMessageDto>,
      slideUrl: null == slideUrl
          ? _value.slideUrl
          : slideUrl // ignore: cast_nullable_to_non_nullable
              as String,
      slidePdfUrl: null == slidePdfUrl
          ? _value.slidePdfUrl
          : slidePdfUrl // ignore: cast_nullable_to_non_nullable
              as String,
      chatTemplateSuggestions: null == chatTemplateSuggestions
          ? _value.chatTemplateSuggestions
          : chatTemplateSuggestions // ignore: cast_nullable_to_non_nullable
              as List<ChatTemplateDto>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $FlashcardSetDataDtoCopyWith<$Res>? get flashcardSetData {
    if (_value.flashcardSetData == null) {
      return null;
    }

    return $FlashcardSetDataDtoCopyWith<$Res>(_value.flashcardSetData!,
        (value) {
      return _then(_value.copyWith(flashcardSetData: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $QuizSetDataDtoCopyWith<$Res>? get quizSetDataDto {
    if (_value.quizSetDataDto == null) {
      return null;
    }

    return $QuizSetDataDtoCopyWith<$Res>(_value.quizSetDataDto!, (value) {
      return _then(_value.copyWith(quizSetDataDto: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NoteDtoImplCopyWith<$Res> implements $NoteDtoCopyWith<$Res> {
  factory _$$NoteDtoImplCopyWith(
          _$NoteDtoImpl value, $Res Function(_$NoteDtoImpl) then) =
      __$$NoteDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'note_id') String noteId,
      @JsonKey(name: 'user') String userId,
      String title,
      @JsonKey(name: 'sub_title') String subTitle,
      List<String> hashtags,
      String summary,
      String transcript,
      String type,
      @JsonKey(name: 'youtube_url') String youtubeUrl,
      @JsonKey(name: 'youtube_id') String youtubeId,
      List<QuizDetailDto> quiz,
      @JsonKey(name: 'flashcards') List<FlashCarDetailDto> flashCards,
      @JsonKey(name: 'user_name') String userName,
      String views,
      @JsonKey(name: 'transcript_json') List<TranscriptDto> transcriptJson,
      @JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'folder_id') String folderId,
      @JsonKey(name: 'mindmap') String mindMap,
      @JsonKey(name: 'createdAt') String createdAt,
      @JsonKey(name: 'duration') double duration,
      @JsonKey(name: 'document_url') String documentUrl,
      @JsonKey(name: 'is_feedback_submitted') bool isFeedbackSubmitted,
      @JsonKey(name: 'is_insufficient_content') bool isInsufficientContent,
      @JsonKey(name: 'clip_durations') List<int> clipDurations,
      List<VoiceDto> voices,
      @JsonKey(name: 'web_url') String webUrl,
      @JsonKey(name: 'is_public') bool isPublic,
      @JsonKey(name: 'is_password_protected') bool isPasswordProtected,
      @JsonKey(name: 'share_password') String sharePassword,
      @JsonKey(name: 'share_link') String shareLink,
      @JsonKey(name: 'suggestions') List<SuggestionDto> suggestions,
      @JsonKey(name: 'quiz_sets') List<QuizSetsDto> quizSets,
      @JsonKey(name: 'flashcard_sets') List<FlashcardSetsDto> flashcardSets,
      @JsonKey(name: 'flashcard_set_data')
      FlashcardSetDataDto? flashcardSetData,
      @JsonKey(name: 'quiz_set_data') QuizSetDataDto? quizSetDataDto,
      @JsonKey(name: 'save_to_notes') List<ChatMessageDto> saveToNotes,
      @JsonKey(name: 'slide_url') String slideUrl,
      @JsonKey(name: 'slide_pdf_url') String slidePdfUrl,
      @JsonKey(name: 'chat_template_suggestions')
      List<ChatTemplateDto> chatTemplateSuggestions});

  @override
  $FlashcardSetDataDtoCopyWith<$Res>? get flashcardSetData;
  @override
  $QuizSetDataDtoCopyWith<$Res>? get quizSetDataDto;
}

/// @nodoc
class __$$NoteDtoImplCopyWithImpl<$Res>
    extends _$NoteDtoCopyWithImpl<$Res, _$NoteDtoImpl>
    implements _$$NoteDtoImplCopyWith<$Res> {
  __$$NoteDtoImplCopyWithImpl(
      _$NoteDtoImpl _value, $Res Function(_$NoteDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noteId = null,
    Object? userId = null,
    Object? title = null,
    Object? subTitle = null,
    Object? hashtags = null,
    Object? summary = null,
    Object? transcript = null,
    Object? type = null,
    Object? youtubeUrl = null,
    Object? youtubeId = null,
    Object? quiz = null,
    Object? flashCards = null,
    Object? userName = null,
    Object? views = null,
    Object? transcriptJson = null,
    Object? audioUrl = null,
    Object? folderId = null,
    Object? mindMap = null,
    Object? createdAt = null,
    Object? duration = null,
    Object? documentUrl = null,
    Object? isFeedbackSubmitted = null,
    Object? isInsufficientContent = null,
    Object? clipDurations = null,
    Object? voices = null,
    Object? webUrl = null,
    Object? isPublic = null,
    Object? isPasswordProtected = null,
    Object? sharePassword = null,
    Object? shareLink = null,
    Object? suggestions = null,
    Object? quizSets = null,
    Object? flashcardSets = null,
    Object? flashcardSetData = freezed,
    Object? quizSetDataDto = freezed,
    Object? saveToNotes = null,
    Object? slideUrl = null,
    Object? slidePdfUrl = null,
    Object? chatTemplateSuggestions = null,
  }) {
    return _then(_$NoteDtoImpl(
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      subTitle: null == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String,
      hashtags: null == hashtags
          ? _value._hashtags
          : hashtags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as String,
      transcript: null == transcript
          ? _value.transcript
          : transcript // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      youtubeUrl: null == youtubeUrl
          ? _value.youtubeUrl
          : youtubeUrl // ignore: cast_nullable_to_non_nullable
              as String,
      youtubeId: null == youtubeId
          ? _value.youtubeId
          : youtubeId // ignore: cast_nullable_to_non_nullable
              as String,
      quiz: null == quiz
          ? _value._quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as List<QuizDetailDto>,
      flashCards: null == flashCards
          ? _value._flashCards
          : flashCards // ignore: cast_nullable_to_non_nullable
              as List<FlashCarDetailDto>,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      views: null == views
          ? _value.views
          : views // ignore: cast_nullable_to_non_nullable
              as String,
      transcriptJson: null == transcriptJson
          ? _value._transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptDto>,
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      folderId: null == folderId
          ? _value.folderId
          : folderId // ignore: cast_nullable_to_non_nullable
              as String,
      mindMap: null == mindMap
          ? _value.mindMap
          : mindMap // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as double,
      documentUrl: null == documentUrl
          ? _value.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isFeedbackSubmitted: null == isFeedbackSubmitted
          ? _value.isFeedbackSubmitted
          : isFeedbackSubmitted // ignore: cast_nullable_to_non_nullable
              as bool,
      isInsufficientContent: null == isInsufficientContent
          ? _value.isInsufficientContent
          : isInsufficientContent // ignore: cast_nullable_to_non_nullable
              as bool,
      clipDurations: null == clipDurations
          ? _value._clipDurations
          : clipDurations // ignore: cast_nullable_to_non_nullable
              as List<int>,
      voices: null == voices
          ? _value._voices
          : voices // ignore: cast_nullable_to_non_nullable
              as List<VoiceDto>,
      webUrl: null == webUrl
          ? _value.webUrl
          : webUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isPublic: null == isPublic
          ? _value.isPublic
          : isPublic // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordProtected: null == isPasswordProtected
          ? _value.isPasswordProtected
          : isPasswordProtected // ignore: cast_nullable_to_non_nullable
              as bool,
      sharePassword: null == sharePassword
          ? _value.sharePassword
          : sharePassword // ignore: cast_nullable_to_non_nullable
              as String,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
      suggestions: null == suggestions
          ? _value._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<SuggestionDto>,
      quizSets: null == quizSets
          ? _value._quizSets
          : quizSets // ignore: cast_nullable_to_non_nullable
              as List<QuizSetsDto>,
      flashcardSets: null == flashcardSets
          ? _value._flashcardSets
          : flashcardSets // ignore: cast_nullable_to_non_nullable
              as List<FlashcardSetsDto>,
      flashcardSetData: freezed == flashcardSetData
          ? _value.flashcardSetData
          : flashcardSetData // ignore: cast_nullable_to_non_nullable
              as FlashcardSetDataDto?,
      quizSetDataDto: freezed == quizSetDataDto
          ? _value.quizSetDataDto
          : quizSetDataDto // ignore: cast_nullable_to_non_nullable
              as QuizSetDataDto?,
      saveToNotes: null == saveToNotes
          ? _value._saveToNotes
          : saveToNotes // ignore: cast_nullable_to_non_nullable
              as List<ChatMessageDto>,
      slideUrl: null == slideUrl
          ? _value.slideUrl
          : slideUrl // ignore: cast_nullable_to_non_nullable
              as String,
      slidePdfUrl: null == slidePdfUrl
          ? _value.slidePdfUrl
          : slidePdfUrl // ignore: cast_nullable_to_non_nullable
              as String,
      chatTemplateSuggestions: null == chatTemplateSuggestions
          ? _value._chatTemplateSuggestions
          : chatTemplateSuggestions // ignore: cast_nullable_to_non_nullable
              as List<ChatTemplateDto>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NoteDtoImpl implements _NoteDto {
  const _$NoteDtoImpl(
      {@JsonKey(name: 'note_id') this.noteId = '',
      @JsonKey(name: 'user') this.userId = '',
      this.title = '',
      @JsonKey(name: 'sub_title') this.subTitle = '',
      final List<String> hashtags = const [],
      this.summary = '',
      this.transcript = '',
      this.type = '',
      @JsonKey(name: 'youtube_url') this.youtubeUrl = '',
      @JsonKey(name: 'youtube_id') this.youtubeId = '',
      final List<QuizDetailDto> quiz = const [],
      @JsonKey(name: 'flashcards')
      final List<FlashCarDetailDto> flashCards = const [],
      @JsonKey(name: 'user_name') this.userName = '',
      this.views = '',
      @JsonKey(name: 'transcript_json')
      final List<TranscriptDto> transcriptJson = const [],
      @JsonKey(name: 'audio_url') this.audioUrl = '',
      @JsonKey(name: 'folder_id') this.folderId = '',
      @JsonKey(name: 'mindmap') this.mindMap = '',
      @JsonKey(name: 'createdAt') this.createdAt = '',
      @JsonKey(name: 'duration') this.duration = 0.0,
      @JsonKey(name: 'document_url') this.documentUrl = '',
      @JsonKey(name: 'is_feedback_submitted') this.isFeedbackSubmitted = false,
      @JsonKey(name: 'is_insufficient_content')
      this.isInsufficientContent = false,
      @JsonKey(name: 'clip_durations') final List<int> clipDurations = const [],
      final List<VoiceDto> voices = const [],
      @JsonKey(name: 'web_url') this.webUrl = '',
      @JsonKey(name: 'is_public') this.isPublic = false,
      @JsonKey(name: 'is_password_protected') this.isPasswordProtected = false,
      @JsonKey(name: 'share_password') this.sharePassword = '',
      @JsonKey(name: 'share_link') this.shareLink = '',
      @JsonKey(name: 'suggestions')
      final List<SuggestionDto> suggestions = const [],
      @JsonKey(name: 'quiz_sets') final List<QuizSetsDto> quizSets = const [],
      @JsonKey(name: 'flashcard_sets')
      final List<FlashcardSetsDto> flashcardSets = const [],
      @JsonKey(name: 'flashcard_set_data') this.flashcardSetData = null,
      @JsonKey(name: 'quiz_set_data') this.quizSetDataDto = null,
      @JsonKey(name: 'save_to_notes')
      final List<ChatMessageDto> saveToNotes = const [],
      @JsonKey(name: 'slide_url') this.slideUrl = '',
      @JsonKey(name: 'slide_pdf_url') this.slidePdfUrl = '',
      @JsonKey(name: 'chat_template_suggestions')
      final List<ChatTemplateDto> chatTemplateSuggestions = const []})
      : _hashtags = hashtags,
        _quiz = quiz,
        _flashCards = flashCards,
        _transcriptJson = transcriptJson,
        _clipDurations = clipDurations,
        _voices = voices,
        _suggestions = suggestions,
        _quizSets = quizSets,
        _flashcardSets = flashcardSets,
        _saveToNotes = saveToNotes,
        _chatTemplateSuggestions = chatTemplateSuggestions;

  factory _$NoteDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$NoteDtoImplFromJson(json);

  @override
  @JsonKey(name: 'note_id')
  final String noteId;
  @override
  @JsonKey(name: 'user')
  final String userId;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey(name: 'sub_title')
  final String subTitle;
  final List<String> _hashtags;
  @override
  @JsonKey()
  List<String> get hashtags {
    if (_hashtags is EqualUnmodifiableListView) return _hashtags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_hashtags);
  }

  @override
  @JsonKey()
  final String summary;
  @override
  @JsonKey()
  final String transcript;
  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey(name: 'youtube_url')
  final String youtubeUrl;
  @override
  @JsonKey(name: 'youtube_id')
  final String youtubeId;
  final List<QuizDetailDto> _quiz;
  @override
  @JsonKey()
  List<QuizDetailDto> get quiz {
    if (_quiz is EqualUnmodifiableListView) return _quiz;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_quiz);
  }

  final List<FlashCarDetailDto> _flashCards;
  @override
  @JsonKey(name: 'flashcards')
  List<FlashCarDetailDto> get flashCards {
    if (_flashCards is EqualUnmodifiableListView) return _flashCards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_flashCards);
  }

  @override
  @JsonKey(name: 'user_name')
  final String userName;
  @override
  @JsonKey()
  final String views;
  final List<TranscriptDto> _transcriptJson;
  @override
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson {
    if (_transcriptJson is EqualUnmodifiableListView) return _transcriptJson;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transcriptJson);
  }

  @override
  @JsonKey(name: 'audio_url')
  final String audioUrl;
  @override
  @JsonKey(name: 'folder_id')
  final String folderId;
  @override
  @JsonKey(name: 'mindmap')
  final String mindMap;
  @override
  @JsonKey(name: 'createdAt')
  final String createdAt;
  @override
  @JsonKey(name: 'duration')
  final double duration;
  @override
  @JsonKey(name: 'document_url')
  final String documentUrl;
  @override
  @JsonKey(name: 'is_feedback_submitted')
  final bool isFeedbackSubmitted;
  @override
  @JsonKey(name: 'is_insufficient_content')
  final bool isInsufficientContent;
  final List<int> _clipDurations;
  @override
  @JsonKey(name: 'clip_durations')
  List<int> get clipDurations {
    if (_clipDurations is EqualUnmodifiableListView) return _clipDurations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_clipDurations);
  }

  final List<VoiceDto> _voices;
  @override
  @JsonKey()
  List<VoiceDto> get voices {
    if (_voices is EqualUnmodifiableListView) return _voices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_voices);
  }

  @override
  @JsonKey(name: 'web_url')
  final String webUrl;
  @override
  @JsonKey(name: 'is_public')
  final bool isPublic;
  @override
  @JsonKey(name: 'is_password_protected')
  final bool isPasswordProtected;
  @override
  @JsonKey(name: 'share_password')
  final String sharePassword;
  @override
  @JsonKey(name: 'share_link')
  final String shareLink;
  final List<SuggestionDto> _suggestions;
  @override
  @JsonKey(name: 'suggestions')
  List<SuggestionDto> get suggestions {
    if (_suggestions is EqualUnmodifiableListView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestions);
  }

  final List<QuizSetsDto> _quizSets;
  @override
  @JsonKey(name: 'quiz_sets')
  List<QuizSetsDto> get quizSets {
    if (_quizSets is EqualUnmodifiableListView) return _quizSets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_quizSets);
  }

  final List<FlashcardSetsDto> _flashcardSets;
  @override
  @JsonKey(name: 'flashcard_sets')
  List<FlashcardSetsDto> get flashcardSets {
    if (_flashcardSets is EqualUnmodifiableListView) return _flashcardSets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_flashcardSets);
  }

  @override
  @JsonKey(name: 'flashcard_set_data')
  final FlashcardSetDataDto? flashcardSetData;
  @override
  @JsonKey(name: 'quiz_set_data')
  final QuizSetDataDto? quizSetDataDto;
  final List<ChatMessageDto> _saveToNotes;
  @override
  @JsonKey(name: 'save_to_notes')
  List<ChatMessageDto> get saveToNotes {
    if (_saveToNotes is EqualUnmodifiableListView) return _saveToNotes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_saveToNotes);
  }

  @override
  @JsonKey(name: 'slide_url')
  final String slideUrl;
  @override
  @JsonKey(name: 'slide_pdf_url')
  final String slidePdfUrl;
  final List<ChatTemplateDto> _chatTemplateSuggestions;
  @override
  @JsonKey(name: 'chat_template_suggestions')
  List<ChatTemplateDto> get chatTemplateSuggestions {
    if (_chatTemplateSuggestions is EqualUnmodifiableListView)
      return _chatTemplateSuggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_chatTemplateSuggestions);
  }

  @override
  String toString() {
    return 'NoteDto(noteId: $noteId, userId: $userId, title: $title, subTitle: $subTitle, hashtags: $hashtags, summary: $summary, transcript: $transcript, type: $type, youtubeUrl: $youtubeUrl, youtubeId: $youtubeId, quiz: $quiz, flashCards: $flashCards, userName: $userName, views: $views, transcriptJson: $transcriptJson, audioUrl: $audioUrl, folderId: $folderId, mindMap: $mindMap, createdAt: $createdAt, duration: $duration, documentUrl: $documentUrl, isFeedbackSubmitted: $isFeedbackSubmitted, isInsufficientContent: $isInsufficientContent, clipDurations: $clipDurations, voices: $voices, webUrl: $webUrl, isPublic: $isPublic, isPasswordProtected: $isPasswordProtected, sharePassword: $sharePassword, shareLink: $shareLink, suggestions: $suggestions, quizSets: $quizSets, flashcardSets: $flashcardSets, flashcardSetData: $flashcardSetData, quizSetDataDto: $quizSetDataDto, saveToNotes: $saveToNotes, slideUrl: $slideUrl, slidePdfUrl: $slidePdfUrl, chatTemplateSuggestions: $chatTemplateSuggestions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoteDtoImpl &&
            (identical(other.noteId, noteId) || other.noteId == noteId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            const DeepCollectionEquality().equals(other._hashtags, _hashtags) &&
            (identical(other.summary, summary) || other.summary == summary) &&
            (identical(other.transcript, transcript) ||
                other.transcript == transcript) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.youtubeUrl, youtubeUrl) ||
                other.youtubeUrl == youtubeUrl) &&
            (identical(other.youtubeId, youtubeId) ||
                other.youtubeId == youtubeId) &&
            const DeepCollectionEquality().equals(other._quiz, _quiz) &&
            const DeepCollectionEquality()
                .equals(other._flashCards, _flashCards) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.views, views) || other.views == views) &&
            const DeepCollectionEquality()
                .equals(other._transcriptJson, _transcriptJson) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.folderId, folderId) ||
                other.folderId == folderId) &&
            (identical(other.mindMap, mindMap) || other.mindMap == mindMap) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.isFeedbackSubmitted, isFeedbackSubmitted) ||
                other.isFeedbackSubmitted == isFeedbackSubmitted) &&
            (identical(other.isInsufficientContent, isInsufficientContent) ||
                other.isInsufficientContent == isInsufficientContent) &&
            const DeepCollectionEquality()
                .equals(other._clipDurations, _clipDurations) &&
            const DeepCollectionEquality().equals(other._voices, _voices) &&
            (identical(other.webUrl, webUrl) || other.webUrl == webUrl) &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.isPasswordProtected, isPasswordProtected) ||
                other.isPasswordProtected == isPasswordProtected) &&
            (identical(other.sharePassword, sharePassword) ||
                other.sharePassword == sharePassword) &&
            (identical(other.shareLink, shareLink) ||
                other.shareLink == shareLink) &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions) &&
            const DeepCollectionEquality().equals(other._quizSets, _quizSets) &&
            const DeepCollectionEquality()
                .equals(other._flashcardSets, _flashcardSets) &&
            (identical(other.flashcardSetData, flashcardSetData) ||
                other.flashcardSetData == flashcardSetData) &&
            (identical(other.quizSetDataDto, quizSetDataDto) ||
                other.quizSetDataDto == quizSetDataDto) &&
            const DeepCollectionEquality()
                .equals(other._saveToNotes, _saveToNotes) &&
            (identical(other.slideUrl, slideUrl) ||
                other.slideUrl == slideUrl) &&
            (identical(other.slidePdfUrl, slidePdfUrl) ||
                other.slidePdfUrl == slidePdfUrl) &&
            const DeepCollectionEquality().equals(
                other._chatTemplateSuggestions, _chatTemplateSuggestions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        noteId,
        userId,
        title,
        subTitle,
        const DeepCollectionEquality().hash(_hashtags),
        summary,
        transcript,
        type,
        youtubeUrl,
        youtubeId,
        const DeepCollectionEquality().hash(_quiz),
        const DeepCollectionEquality().hash(_flashCards),
        userName,
        views,
        const DeepCollectionEquality().hash(_transcriptJson),
        audioUrl,
        folderId,
        mindMap,
        createdAt,
        duration,
        documentUrl,
        isFeedbackSubmitted,
        isInsufficientContent,
        const DeepCollectionEquality().hash(_clipDurations),
        const DeepCollectionEquality().hash(_voices),
        webUrl,
        isPublic,
        isPasswordProtected,
        sharePassword,
        shareLink,
        const DeepCollectionEquality().hash(_suggestions),
        const DeepCollectionEquality().hash(_quizSets),
        const DeepCollectionEquality().hash(_flashcardSets),
        flashcardSetData,
        quizSetDataDto,
        const DeepCollectionEquality().hash(_saveToNotes),
        slideUrl,
        slidePdfUrl,
        const DeepCollectionEquality().hash(_chatTemplateSuggestions)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NoteDtoImplCopyWith<_$NoteDtoImpl> get copyWith =>
      __$$NoteDtoImplCopyWithImpl<_$NoteDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NoteDtoImplToJson(
      this,
    );
  }
}

abstract class _NoteDto implements NoteDto {
  const factory _NoteDto(
      {@JsonKey(name: 'note_id') final String noteId,
      @JsonKey(name: 'user') final String userId,
      final String title,
      @JsonKey(name: 'sub_title') final String subTitle,
      final List<String> hashtags,
      final String summary,
      final String transcript,
      final String type,
      @JsonKey(name: 'youtube_url') final String youtubeUrl,
      @JsonKey(name: 'youtube_id') final String youtubeId,
      final List<QuizDetailDto> quiz,
      @JsonKey(name: 'flashcards') final List<FlashCarDetailDto> flashCards,
      @JsonKey(name: 'user_name') final String userName,
      final String views,
      @JsonKey(name: 'transcript_json')
      final List<TranscriptDto> transcriptJson,
      @JsonKey(name: 'audio_url') final String audioUrl,
      @JsonKey(name: 'folder_id') final String folderId,
      @JsonKey(name: 'mindmap') final String mindMap,
      @JsonKey(name: 'createdAt') final String createdAt,
      @JsonKey(name: 'duration') final double duration,
      @JsonKey(name: 'document_url') final String documentUrl,
      @JsonKey(name: 'is_feedback_submitted') final bool isFeedbackSubmitted,
      @JsonKey(name: 'is_insufficient_content')
      final bool isInsufficientContent,
      @JsonKey(name: 'clip_durations') final List<int> clipDurations,
      final List<VoiceDto> voices,
      @JsonKey(name: 'web_url') final String webUrl,
      @JsonKey(name: 'is_public') final bool isPublic,
      @JsonKey(name: 'is_password_protected') final bool isPasswordProtected,
      @JsonKey(name: 'share_password') final String sharePassword,
      @JsonKey(name: 'share_link') final String shareLink,
      @JsonKey(name: 'suggestions') final List<SuggestionDto> suggestions,
      @JsonKey(name: 'quiz_sets') final List<QuizSetsDto> quizSets,
      @JsonKey(name: 'flashcard_sets')
      final List<FlashcardSetsDto> flashcardSets,
      @JsonKey(name: 'flashcard_set_data')
      final FlashcardSetDataDto? flashcardSetData,
      @JsonKey(name: 'quiz_set_data') final QuizSetDataDto? quizSetDataDto,
      @JsonKey(name: 'save_to_notes') final List<ChatMessageDto> saveToNotes,
      @JsonKey(name: 'slide_url') final String slideUrl,
      @JsonKey(name: 'slide_pdf_url') final String slidePdfUrl,
      @JsonKey(name: 'chat_template_suggestions')
      final List<ChatTemplateDto> chatTemplateSuggestions}) = _$NoteDtoImpl;

  factory _NoteDto.fromJson(Map<String, dynamic> json) = _$NoteDtoImpl.fromJson;

  @override
  @JsonKey(name: 'note_id')
  String get noteId;
  @override
  @JsonKey(name: 'user')
  String get userId;
  @override
  String get title;
  @override
  @JsonKey(name: 'sub_title')
  String get subTitle;
  @override
  List<String> get hashtags;
  @override
  String get summary;
  @override
  String get transcript;
  @override
  String get type;
  @override
  @JsonKey(name: 'youtube_url')
  String get youtubeUrl;
  @override
  @JsonKey(name: 'youtube_id')
  String get youtubeId;
  @override
  List<QuizDetailDto> get quiz;
  @override
  @JsonKey(name: 'flashcards')
  List<FlashCarDetailDto> get flashCards;
  @override
  @JsonKey(name: 'user_name')
  String get userName;
  @override
  String get views;
  @override
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson;
  @override
  @JsonKey(name: 'audio_url')
  String get audioUrl;
  @override
  @JsonKey(name: 'folder_id')
  String get folderId;
  @override
  @JsonKey(name: 'mindmap')
  String get mindMap;
  @override
  @JsonKey(name: 'createdAt')
  String get createdAt;
  @override
  @JsonKey(name: 'duration')
  double get duration;
  @override
  @JsonKey(name: 'document_url')
  String get documentUrl;
  @override
  @JsonKey(name: 'is_feedback_submitted')
  bool get isFeedbackSubmitted;
  @override
  @JsonKey(name: 'is_insufficient_content')
  bool get isInsufficientContent;
  @override
  @JsonKey(name: 'clip_durations')
  List<int> get clipDurations;
  @override
  List<VoiceDto> get voices;
  @override
  @JsonKey(name: 'web_url')
  String get webUrl;
  @override
  @JsonKey(name: 'is_public')
  bool get isPublic;
  @override
  @JsonKey(name: 'is_password_protected')
  bool get isPasswordProtected;
  @override
  @JsonKey(name: 'share_password')
  String get sharePassword;
  @override
  @JsonKey(name: 'share_link')
  String get shareLink;
  @override
  @JsonKey(name: 'suggestions')
  List<SuggestionDto> get suggestions;
  @override
  @JsonKey(name: 'quiz_sets')
  List<QuizSetsDto> get quizSets;
  @override
  @JsonKey(name: 'flashcard_sets')
  List<FlashcardSetsDto> get flashcardSets;
  @override
  @JsonKey(name: 'flashcard_set_data')
  FlashcardSetDataDto? get flashcardSetData;
  @override
  @JsonKey(name: 'quiz_set_data')
  QuizSetDataDto? get quizSetDataDto;
  @override
  @JsonKey(name: 'save_to_notes')
  List<ChatMessageDto> get saveToNotes;
  @override
  @JsonKey(name: 'slide_url')
  String get slideUrl;
  @override
  @JsonKey(name: 'slide_pdf_url')
  String get slidePdfUrl;
  @override
  @JsonKey(name: 'chat_template_suggestions')
  List<ChatTemplateDto> get chatTemplateSuggestions;
  @override
  @JsonKey(ignore: true)
  _$$NoteDtoImplCopyWith<_$NoteDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
