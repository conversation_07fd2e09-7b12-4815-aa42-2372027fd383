// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'background_quiz_video_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BackgroundQuizVideoDtoImpl _$$BackgroundQuizVideoDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$BackgroundQuizVideoDtoImpl(
      templateId: json['template_id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      thumbnailUrl: json['thumbnail_url'] as String? ?? '',
      videoUrl: json['video_url'] as String? ?? '',
      width: (json['width'] as num?)?.toInt() ?? 0,
      height: (json['height'] as num?)?.toInt() ?? 0,
      displayOrder: (json['display_order'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$BackgroundQuizVideoDtoImplToJson(
        _$BackgroundQuizVideoDtoImpl instance) =>
    <String, dynamic>{
      'template_id': instance.templateId,
      'title': instance.title,
      'thumbnail_url': instance.thumbnailUrl,
      'video_url': instance.videoUrl,
      'width': instance.width,
      'height': instance.height,
      'display_order': instance.displayOrder,
    };
