// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'summary_page_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$SummaryPageState {
  SummaryPageOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  bool get isShowFeedback => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SummaryPageStateCopyWith<SummaryPageState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SummaryPageStateCopyWith<$Res> {
  factory $SummaryPageStateCopyWith(
          SummaryPageState value, $Res Function(SummaryPageState) then) =
      _$SummaryPageStateCopyWithImpl<$Res, SummaryPageState>;
  @useResult
  $Res call({SummaryPageOneShotEvent oneShotEvent, bool isShowFeedback});
}

/// @nodoc
class _$SummaryPageStateCopyWithImpl<$Res, $Val extends SummaryPageState>
    implements $SummaryPageStateCopyWith<$Res> {
  _$SummaryPageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? isShowFeedback = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as SummaryPageOneShotEvent,
      isShowFeedback: null == isShowFeedback
          ? _value.isShowFeedback
          : isShowFeedback // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SummaryPageStateImplCopyWith<$Res>
    implements $SummaryPageStateCopyWith<$Res> {
  factory _$$SummaryPageStateImplCopyWith(_$SummaryPageStateImpl value,
          $Res Function(_$SummaryPageStateImpl) then) =
      __$$SummaryPageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({SummaryPageOneShotEvent oneShotEvent, bool isShowFeedback});
}

/// @nodoc
class __$$SummaryPageStateImplCopyWithImpl<$Res>
    extends _$SummaryPageStateCopyWithImpl<$Res, _$SummaryPageStateImpl>
    implements _$$SummaryPageStateImplCopyWith<$Res> {
  __$$SummaryPageStateImplCopyWithImpl(_$SummaryPageStateImpl _value,
      $Res Function(_$SummaryPageStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? isShowFeedback = null,
  }) {
    return _then(_$SummaryPageStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as SummaryPageOneShotEvent,
      isShowFeedback: null == isShowFeedback
          ? _value.isShowFeedback
          : isShowFeedback // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SummaryPageStateImpl implements _SummaryPageState {
  const _$SummaryPageStateImpl(
      {this.oneShotEvent = SummaryPageOneShotEvent.none,
      this.isShowFeedback = false});

  @override
  @JsonKey()
  final SummaryPageOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final bool isShowFeedback;

  @override
  String toString() {
    return 'SummaryPageState(oneShotEvent: $oneShotEvent, isShowFeedback: $isShowFeedback)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SummaryPageStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.isShowFeedback, isShowFeedback) ||
                other.isShowFeedback == isShowFeedback));
  }

  @override
  int get hashCode => Object.hash(runtimeType, oneShotEvent, isShowFeedback);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SummaryPageStateImplCopyWith<_$SummaryPageStateImpl> get copyWith =>
      __$$SummaryPageStateImplCopyWithImpl<_$SummaryPageStateImpl>(
          this, _$identity);
}

abstract class _SummaryPageState implements SummaryPageState {
  const factory _SummaryPageState(
      {final SummaryPageOneShotEvent oneShotEvent,
      final bool isShowFeedback}) = _$SummaryPageStateImpl;

  @override
  SummaryPageOneShotEvent get oneShotEvent;
  @override
  bool get isShowFeedback;
  @override
  @JsonKey(ignore: true)
  _$$SummaryPageStateImplCopyWith<_$SummaryPageStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
