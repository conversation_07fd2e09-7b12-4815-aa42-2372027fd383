import 'package:flutter/cupertino.dart';
import 'package:note_x/base/style/app_theme_entity.dart';

class AppThemeConst {
  static const AppThemeEntity light = AppThemeEntity(
    id: 1,
    name: 'Light Theme',
    brightness: Brightness.light,
    blue: '#007BFE',
    gray: '#9F9F9F',
    primary: '#101010',
    secondary: '#FFFFFF',
    neutral: '#F3F3F9',
    summary: '#FFB054',
    red: '#FE4336',
    yellow: '#FBBC05',
    green: '#31CC5B',
    blue2: '#4285F4',
    purple: '#5856D6',
    violet: '#9992FF',
    white: '#FFFFFF',
    black: '#000000',
    bg: '#FFFFFF',
  );

  static const AppThemeEntity dark = AppThemeEntity(
    id: 2,
    name: 'Dark Theme',
    brightness: Brightness.dark,
    blue: '#007BFE',
    gray: '#9F9F9F',
    primary: '#FFFFFF',
    secondary: '#101010', // black deep
    neutral: '#181818',
    summary: '#FFCF97',
    red: '#FE4336',
    yellow: '#FBBC05',
    green: '#31CC5B',
    blue2: '#4285F4',
    purple: '#5856D6',
    violet: '#9992FF',
    white: '#FFFFFF',
    black: '#000000',
    bg: '#000000',
  );

  static const AppThemeEntity system = AppThemeEntity(
    id: 0,
    name: 'System Theme',
    brightness: Brightness.dark,
    blue: '#007BFE',
    gray: '#9F9F9F',
    primary: '#FFFFFF',
    secondary: '#101010', // black deep
    neutral: '#181818',
    summary: '#FFCF97',
    red: '#FE4336',
    yellow: '#FBBC05',
    green: '#31CC5B',
    blue2: '#4285F4',
    purple: '#5856D6',
    violet: '#9992FF',
    white: '#FFFFFF',
    black: '#000000',
    bg: '#000000',
  );

  static List<AppThemeEntity> get allTheme => [light, dark, system];

  static AppThemeEntity getById(int id) {
    return allTheme.firstWhere((theme) => theme.id == id, orElse: () => light);
  }
}
