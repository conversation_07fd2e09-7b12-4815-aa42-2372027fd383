// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'youtube_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$YoutubeState {
  CreateNoteOneShotEventYoutube get oneShotEvent =>
      throw _privateConstructorUsedError;
  bool get youtubeLinkIsReady => throw _privateConstructorUsedError;
  bool get isEnableWebLinkLanguageDropDown =>
      throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;
  bool get isSummaryLanguageYoutubeMode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $YoutubeStateCopyWith<YoutubeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $YoutubeStateCopyWith<$Res> {
  factory $YoutubeStateCopyWith(
          YoutubeState value, $Res Function(YoutubeState) then) =
      _$YoutubeStateCopyWithImpl<$Res, YoutubeState>;
  @useResult
  $Res call(
      {CreateNoteOneShotEventYoutube oneShotEvent,
      bool youtubeLinkIsReady,
      bool isEnableWebLinkLanguageDropDown,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions,
      bool isSummaryLanguageYoutubeMode});
}

/// @nodoc
class _$YoutubeStateCopyWithImpl<$Res, $Val extends YoutubeState>
    implements $YoutubeStateCopyWith<$Res> {
  _$YoutubeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? youtubeLinkIsReady = null,
    Object? isEnableWebLinkLanguageDropDown = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
    Object? isSummaryLanguageYoutubeMode = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteOneShotEventYoutube,
      youtubeLinkIsReady: null == youtubeLinkIsReady
          ? _value.youtubeLinkIsReady
          : youtubeLinkIsReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableWebLinkLanguageDropDown: null == isEnableWebLinkLanguageDropDown
          ? _value.isEnableWebLinkLanguageDropDown
          : isEnableWebLinkLanguageDropDown // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      isSummaryLanguageYoutubeMode: null == isSummaryLanguageYoutubeMode
          ? _value.isSummaryLanguageYoutubeMode
          : isSummaryLanguageYoutubeMode // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$YoutubeStateImplCopyWith<$Res>
    implements $YoutubeStateCopyWith<$Res> {
  factory _$$YoutubeStateImplCopyWith(
          _$YoutubeStateImpl value, $Res Function(_$YoutubeStateImpl) then) =
      __$$YoutubeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreateNoteOneShotEventYoutube oneShotEvent,
      bool youtubeLinkIsReady,
      bool isEnableWebLinkLanguageDropDown,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions,
      bool isSummaryLanguageYoutubeMode});
}

/// @nodoc
class __$$YoutubeStateImplCopyWithImpl<$Res>
    extends _$YoutubeStateCopyWithImpl<$Res, _$YoutubeStateImpl>
    implements _$$YoutubeStateImplCopyWith<$Res> {
  __$$YoutubeStateImplCopyWithImpl(
      _$YoutubeStateImpl _value, $Res Function(_$YoutubeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? youtubeLinkIsReady = null,
    Object? isEnableWebLinkLanguageDropDown = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
    Object? isSummaryLanguageYoutubeMode = null,
  }) {
    return _then(_$YoutubeStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteOneShotEventYoutube,
      youtubeLinkIsReady: null == youtubeLinkIsReady
          ? _value.youtubeLinkIsReady
          : youtubeLinkIsReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableWebLinkLanguageDropDown: null == isEnableWebLinkLanguageDropDown
          ? _value.isEnableWebLinkLanguageDropDown
          : isEnableWebLinkLanguageDropDown // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
      isSummaryLanguageYoutubeMode: null == isSummaryLanguageYoutubeMode
          ? _value.isSummaryLanguageYoutubeMode
          : isSummaryLanguageYoutubeMode // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$YoutubeStateImpl implements _YoutubeState {
  const _$YoutubeStateImpl(
      {this.oneShotEvent = CreateNoteOneShotEventYoutube.none,
      this.youtubeLinkIsReady = false,
      this.isEnableWebLinkLanguageDropDown = false,
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = '',
      this.isSummaryLanguageYoutubeMode = true});

  @override
  @JsonKey()
  final CreateNoteOneShotEventYoutube oneShotEvent;
  @override
  @JsonKey()
  final bool youtubeLinkIsReady;
  @override
  @JsonKey()
  final bool isEnableWebLinkLanguageDropDown;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;
  @override
  @JsonKey()
  final bool isSummaryLanguageYoutubeMode;

  @override
  String toString() {
    return 'YoutubeState(oneShotEvent: $oneShotEvent, youtubeLinkIsReady: $youtubeLinkIsReady, isEnableWebLinkLanguageDropDown: $isEnableWebLinkLanguageDropDown, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions, isSummaryLanguageYoutubeMode: $isSummaryLanguageYoutubeMode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$YoutubeStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.youtubeLinkIsReady, youtubeLinkIsReady) ||
                other.youtubeLinkIsReady == youtubeLinkIsReady) &&
            (identical(other.isEnableWebLinkLanguageDropDown,
                    isEnableWebLinkLanguageDropDown) ||
                other.isEnableWebLinkLanguageDropDown ==
                    isEnableWebLinkLanguageDropDown) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions) &&
            (identical(other.isSummaryLanguageYoutubeMode,
                    isSummaryLanguageYoutubeMode) ||
                other.isSummaryLanguageYoutubeMode ==
                    isSummaryLanguageYoutubeMode));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      youtubeLinkIsReady,
      isEnableWebLinkLanguageDropDown,
      summaryStyle,
      writingStyle,
      additionalInstructions,
      isSummaryLanguageYoutubeMode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$YoutubeStateImplCopyWith<_$YoutubeStateImpl> get copyWith =>
      __$$YoutubeStateImplCopyWithImpl<_$YoutubeStateImpl>(this, _$identity);
}

abstract class _YoutubeState implements YoutubeState {
  const factory _YoutubeState(
      {final CreateNoteOneShotEventYoutube oneShotEvent,
      final bool youtubeLinkIsReady,
      final bool isEnableWebLinkLanguageDropDown,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions,
      final bool isSummaryLanguageYoutubeMode}) = _$YoutubeStateImpl;

  @override
  CreateNoteOneShotEventYoutube get oneShotEvent;
  @override
  bool get youtubeLinkIsReady;
  @override
  bool get isEnableWebLinkLanguageDropDown;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  bool get isSummaryLanguageYoutubeMode;
  @override
  @JsonKey(ignore: true)
  _$$YoutubeStateImplCopyWith<_$YoutubeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
