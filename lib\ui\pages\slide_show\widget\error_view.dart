import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class ErrorView extends StatelessWidget {
  final VoidCallback onRetry;
  const ErrorView({Key? key, required this.onRetry}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          AppConstants.kSpacingItem84,
          SvgPicture.asset(
            Assets.images.imgMascotErrorSlideShow,
            width: 144,
            height: 144,
          ),
          AppConstants.kSpacingItem16,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32.w),
            child: CommonText(
              S.current.failed_to_load_slideshow,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          AppConstants.kSpacingItem16,
          AppCommonButton(
            width: MediaQuery.of(context).size.height * 0.2,
            height: 48,
            borderRadius: BorderRadius.circular(100.r),
            gradient: const LinearGradient(
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
              colors: AppColors.gradientCTABlue,
            ),
            textWidget: CommonText(
              S.current.retry,
              style: TextStyle(
                height: 1,
                fontSize: context.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w600,
                color: context.colorScheme.mainPrimary,
              ),
            ),
            onPressed: onRetry,
          ),
        ],
      ),
    );
  }
}
