import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/chat/cubit/chat_state.dart';
import 'package:extended_text_field/extended_text_field.dart';

class TextFiledChat extends StatelessWidget {
  final TextEditingController messageController;
  final FocusNode focusNode;
  final ValueNotifier<ChatTemplateModel?> currentTemplate;
  final bool showGuide;
  final String noteId;
  final bool isLimitMessages;
  final VoidCallback clearTemplate;
  final Function(String) onSendMessage;
  final Function(BuildContext) navigateToPurchasePage;
  final Function() stopReceiving;
  final ValueNotifier<bool> _hasText = ValueNotifier<bool>(false);
  final List<ChatTemplateModel> templates;
  final bool showScrollButton;

  TextFiledChat({
    Key? key,
    required this.messageController,
    required this.focusNode,
    required this.currentTemplate,
    required this.showGuide,
    required this.noteId,
    required this.isLimitMessages,
    required this.clearTemplate,
    required this.onSendMessage,
    required this.navigateToPurchasePage,
    required this.stopReceiving,
    required this.templates,
    this.showScrollButton = false,
  }) : super(key: key) {
    messageController.addListener(_updateHasText);
  }

  void _updateHasText() {
    _hasText.value = messageController.text.trim().isNotEmpty;
  }

  bool _isExceededMessageLimit(BuildContext context) {
    final currentNumberOfMessages =
        context.read<ChatCubit>().getNumberOfLimitMessagesById(noteId);
    return isLimitMessages &&
        currentNumberOfMessages > AppConstants.numberOfLimitMessages;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatCubit, ChatState>(builder: (context, state) {
      _updateHasText();
      return Column(
        children: [
          AnimatedSize(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: showScrollButton ? 0.0 : 1.0,
              child: showScrollButton
                  ? const SizedBox.shrink()
                  : ValueListenableBuilder<ChatTemplateModel?>(
                      valueListenable: currentTemplate,
                      builder: (context, template, child) {
                        return TemplateWidget(
                          templates: templates,
                          selectedTemplateId: template?.id,
                          onTap: (selectedTemplate) {
                            if (selectedTemplate != null) {
                              messageController.text =
                                  "${selectedTemplate.name} ${selectedTemplate.prompt}";
                              currentTemplate.value = selectedTemplate;
                            } else {
                              clearTemplate();
                            }
                          },
                        );
                      },
                    ),
            ),
          ),
          AppConstants.kSpacingItem12,
          ValueListenableBuilder<ChatTemplateModel?>(
            valueListenable: currentTemplate,
            builder: (context, template, child) {
              return ValueListenableBuilder<bool>(
                valueListenable: _hasText,
                builder: (context, hasText, child) {
                  return Container(
                    decoration: BoxDecoration(
                      color: context.colorScheme.mainNeutral,
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Lottie.asset(
                                Assets.videos.typeChat,
                                width: context.isTablet ? 48.0 : 48.0.w,
                                height: context.isTablet ? 48.0 : 48.0.w,
                              ),
                              const Spacer(),
                            ],
                          ),
                          Expanded(
                            child: ExtendedTextField(
                              controller: messageController,
                              focusNode: focusNode,
                              textAlignVertical: TextAlignVertical.center,
                              specialTextSpanBuilder: TemplateTextSpanBuilder(
                                context,
                                isTemplate: currentTemplate.value != null,
                                currentTemplate: currentTemplate.value,
                                onDeleteTemplate: clearTemplate,
                              ),
                              style: TextStyle(
                                color: context.colorScheme.mainPrimary,
                                fontSize: context.isTablet ? 16 : 14.sp,
                              ),
                              onChanged: (value) {
                                if (value.isEmpty) {
                                  clearTemplate();
                                } else if (currentTemplate.value != null) {
                                  final templatePrefix =
                                      "${currentTemplate.value!.name} ";
                                  if (value.length <= templatePrefix.length ||
                                      !value.startsWith(
                                          currentTemplate.value!.name)) {
                                    clearTemplate();
                                  }
                                }
                              },
                              decoration: _buildInputDecoration(
                                  context, _hasText.value, state.isSending),
                              cursorColor: context.colorScheme.mainPrimary,
                              maxLines: 3,
                              minLines: 1,
                              keyboardType: TextInputType.multiline,
                              textCapitalization: TextCapitalization.sentences,
                              buildCounter: (
                                context, {
                                required currentLength,
                                required isFocused,
                                maxLength,
                              }) {
                                if (isFocused) {
                                  return Padding(
                                    padding: EdgeInsets.only(
                                        bottom: context.isTablet ? 24 : 24.h),
                                    child: null,
                                  );
                                }
                                return null;
                              },
                            ),
                          ),
                          _buildSuffixIcon(
                              context, _hasText.value, state.isSending),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
          AppConstants.kSpacingItem4,
          if (!showGuide)
            CommonText(
              S.current.chat_topic_temporary_stored,
              style: TextStyle(
                color: context.colorScheme.mainGray,
                fontSize: context.isTablet ? 12 : 10.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
        ],
      );
    });
  }

  InputDecoration _buildInputDecoration(
      BuildContext context, bool hasText, bool isSending) {
    return InputDecoration(
      hintText: S.current.ask_anything,
      hintStyle: TextStyle(
        color: context.colorScheme.mainGray,
        fontSize: context.isTablet ? 16 : 14.sp,
        fontWeight: FontWeight.w400,
      ),
      filled: true,
      fillColor: context.colorScheme.mainNeutral,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 0,
        vertical: context.isTablet ? 12 : 12.h,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(100.r),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide.none,
      ),
    );
  }

  Widget _buildSuffixIcon(BuildContext context, bool hasText, bool isSending) {
    return Container(
      margin: EdgeInsets.only(
        right: context.isTablet ? 8 : 8.w,
        bottom: context.isTablet ? 8 : 8.w,
      ),
      padding: EdgeInsets.all(
        context.isTablet ? 6 : 6.w,
      ),
      decoration: BoxDecoration(
        color: context.colorScheme.mainPrimary.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: isSending
          ? CupertinoButton(
              minSize: 0,
              padding: EdgeInsets.zero,
              onPressed: () {
                AnalyticsService.logAnalyticsEventNoParam(
                  eventName: EventName.ai_chat_stop_chat,
                );
                HapticFeedback.mediumImpact();
                stopReceiving();
              },
              child: Icon(Icons.stop_circle,
                  color: context.colorScheme.mainPrimary),
            )
          : CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 0,
              onPressed: hasText
                  ? () {
                      if (_isExceededMessageLimit(context)) {
                        focusNode.unfocus();
                        navigateToPurchasePage(context);
                      } else {
                        HapticFeedback.mediumImpact();
                        AnalyticsService.logAnalyticsEventNoParam(
                          eventName: EventName.ai_chat_send_chat,
                        );
                        onSendMessage(messageController.text);
                      }
                    }
                  : null,
              child: SvgPicture.asset(
                Assets.icons.icSendChat,
                width: context.isTablet ? 20 : 20.w,
                height: context.isTablet ? 20 : 20.h,
                colorFilter: ColorFilter.mode(
                  hasText
                      ? context.colorScheme.mainBlue
                      : context.colorScheme.mainGray,
                  BlendMode.srcIn,
                ),
              ),
            ),
    );
  }
}
