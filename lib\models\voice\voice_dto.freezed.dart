// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

VoiceDto _$VoiceDtoFromJson(Map<String, dynamic> json) {
  return _VoiceDto.fromJson(json);
}

/// @nodoc
mixin _$VoiceDto {
  @JsonKey(name: 'voice_id')
  String get voiceId => throw _privateConstructorUsedError;
  @JsonKey(name: 'voice_name')
  String get voiceName => throw _privateConstructorUsedError;
  @JsonKey(name: 'voice_gender')
  String get voiceGender => throw _privateConstructorUsedError;
  @JsonKey(name: 'preview_url')
  String get previewUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_caption_supported')
  bool get isCaptionSupport => throw _privateConstructorUsedError;
  @JsonKey(name: 'voice_language')
  String get voiceLanguage => throw _privateConstructorUsedError;
  @JsonKey(name: 'display_language')
  String get displayLanguage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VoiceDtoCopyWith<VoiceDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceDtoCopyWith<$Res> {
  factory $VoiceDtoCopyWith(VoiceDto value, $Res Function(VoiceDto) then) =
      _$VoiceDtoCopyWithImpl<$Res, VoiceDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'voice_id') String voiceId,
      @JsonKey(name: 'voice_name') String voiceName,
      @JsonKey(name: 'voice_gender') String voiceGender,
      @JsonKey(name: 'preview_url') String previewUrl,
      @JsonKey(name: 'is_caption_supported') bool isCaptionSupport,
      @JsonKey(name: 'voice_language') String voiceLanguage,
      @JsonKey(name: 'display_language') String displayLanguage});
}

/// @nodoc
class _$VoiceDtoCopyWithImpl<$Res, $Val extends VoiceDto>
    implements $VoiceDtoCopyWith<$Res> {
  _$VoiceDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voiceId = null,
    Object? voiceName = null,
    Object? voiceGender = null,
    Object? previewUrl = null,
    Object? isCaptionSupport = null,
    Object? voiceLanguage = null,
    Object? displayLanguage = null,
  }) {
    return _then(_value.copyWith(
      voiceId: null == voiceId
          ? _value.voiceId
          : voiceId // ignore: cast_nullable_to_non_nullable
              as String,
      voiceName: null == voiceName
          ? _value.voiceName
          : voiceName // ignore: cast_nullable_to_non_nullable
              as String,
      voiceGender: null == voiceGender
          ? _value.voiceGender
          : voiceGender // ignore: cast_nullable_to_non_nullable
              as String,
      previewUrl: null == previewUrl
          ? _value.previewUrl
          : previewUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isCaptionSupport: null == isCaptionSupport
          ? _value.isCaptionSupport
          : isCaptionSupport // ignore: cast_nullable_to_non_nullable
              as bool,
      voiceLanguage: null == voiceLanguage
          ? _value.voiceLanguage
          : voiceLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      displayLanguage: null == displayLanguage
          ? _value.displayLanguage
          : displayLanguage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoiceDtoImplCopyWith<$Res>
    implements $VoiceDtoCopyWith<$Res> {
  factory _$$VoiceDtoImplCopyWith(
          _$VoiceDtoImpl value, $Res Function(_$VoiceDtoImpl) then) =
      __$$VoiceDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'voice_id') String voiceId,
      @JsonKey(name: 'voice_name') String voiceName,
      @JsonKey(name: 'voice_gender') String voiceGender,
      @JsonKey(name: 'preview_url') String previewUrl,
      @JsonKey(name: 'is_caption_supported') bool isCaptionSupport,
      @JsonKey(name: 'voice_language') String voiceLanguage,
      @JsonKey(name: 'display_language') String displayLanguage});
}

/// @nodoc
class __$$VoiceDtoImplCopyWithImpl<$Res>
    extends _$VoiceDtoCopyWithImpl<$Res, _$VoiceDtoImpl>
    implements _$$VoiceDtoImplCopyWith<$Res> {
  __$$VoiceDtoImplCopyWithImpl(
      _$VoiceDtoImpl _value, $Res Function(_$VoiceDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voiceId = null,
    Object? voiceName = null,
    Object? voiceGender = null,
    Object? previewUrl = null,
    Object? isCaptionSupport = null,
    Object? voiceLanguage = null,
    Object? displayLanguage = null,
  }) {
    return _then(_$VoiceDtoImpl(
      voiceId: null == voiceId
          ? _value.voiceId
          : voiceId // ignore: cast_nullable_to_non_nullable
              as String,
      voiceName: null == voiceName
          ? _value.voiceName
          : voiceName // ignore: cast_nullable_to_non_nullable
              as String,
      voiceGender: null == voiceGender
          ? _value.voiceGender
          : voiceGender // ignore: cast_nullable_to_non_nullable
              as String,
      previewUrl: null == previewUrl
          ? _value.previewUrl
          : previewUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isCaptionSupport: null == isCaptionSupport
          ? _value.isCaptionSupport
          : isCaptionSupport // ignore: cast_nullable_to_non_nullable
              as bool,
      voiceLanguage: null == voiceLanguage
          ? _value.voiceLanguage
          : voiceLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      displayLanguage: null == displayLanguage
          ? _value.displayLanguage
          : displayLanguage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceDtoImpl implements _VoiceDto {
  const _$VoiceDtoImpl(
      {@JsonKey(name: 'voice_id') this.voiceId = '',
      @JsonKey(name: 'voice_name') this.voiceName = '',
      @JsonKey(name: 'voice_gender') this.voiceGender = '',
      @JsonKey(name: 'preview_url') this.previewUrl = '',
      @JsonKey(name: 'is_caption_supported') this.isCaptionSupport = true,
      @JsonKey(name: 'voice_language') this.voiceLanguage = '',
      @JsonKey(name: 'display_language') this.displayLanguage = ''});

  factory _$VoiceDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceDtoImplFromJson(json);

  @override
  @JsonKey(name: 'voice_id')
  final String voiceId;
  @override
  @JsonKey(name: 'voice_name')
  final String voiceName;
  @override
  @JsonKey(name: 'voice_gender')
  final String voiceGender;
  @override
  @JsonKey(name: 'preview_url')
  final String previewUrl;
  @override
  @JsonKey(name: 'is_caption_supported')
  final bool isCaptionSupport;
  @override
  @JsonKey(name: 'voice_language')
  final String voiceLanguage;
  @override
  @JsonKey(name: 'display_language')
  final String displayLanguage;

  @override
  String toString() {
    return 'VoiceDto(voiceId: $voiceId, voiceName: $voiceName, voiceGender: $voiceGender, previewUrl: $previewUrl, isCaptionSupport: $isCaptionSupport, voiceLanguage: $voiceLanguage, displayLanguage: $displayLanguage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceDtoImpl &&
            (identical(other.voiceId, voiceId) || other.voiceId == voiceId) &&
            (identical(other.voiceName, voiceName) ||
                other.voiceName == voiceName) &&
            (identical(other.voiceGender, voiceGender) ||
                other.voiceGender == voiceGender) &&
            (identical(other.previewUrl, previewUrl) ||
                other.previewUrl == previewUrl) &&
            (identical(other.isCaptionSupport, isCaptionSupport) ||
                other.isCaptionSupport == isCaptionSupport) &&
            (identical(other.voiceLanguage, voiceLanguage) ||
                other.voiceLanguage == voiceLanguage) &&
            (identical(other.displayLanguage, displayLanguage) ||
                other.displayLanguage == displayLanguage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, voiceId, voiceName, voiceGender,
      previewUrl, isCaptionSupport, voiceLanguage, displayLanguage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceDtoImplCopyWith<_$VoiceDtoImpl> get copyWith =>
      __$$VoiceDtoImplCopyWithImpl<_$VoiceDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceDtoImplToJson(
      this,
    );
  }
}

abstract class _VoiceDto implements VoiceDto {
  const factory _VoiceDto(
          {@JsonKey(name: 'voice_id') final String voiceId,
          @JsonKey(name: 'voice_name') final String voiceName,
          @JsonKey(name: 'voice_gender') final String voiceGender,
          @JsonKey(name: 'preview_url') final String previewUrl,
          @JsonKey(name: 'is_caption_supported') final bool isCaptionSupport,
          @JsonKey(name: 'voice_language') final String voiceLanguage,
          @JsonKey(name: 'display_language') final String displayLanguage}) =
      _$VoiceDtoImpl;

  factory _VoiceDto.fromJson(Map<String, dynamic> json) =
      _$VoiceDtoImpl.fromJson;

  @override
  @JsonKey(name: 'voice_id')
  String get voiceId;
  @override
  @JsonKey(name: 'voice_name')
  String get voiceName;
  @override
  @JsonKey(name: 'voice_gender')
  String get voiceGender;
  @override
  @JsonKey(name: 'preview_url')
  String get previewUrl;
  @override
  @JsonKey(name: 'is_caption_supported')
  bool get isCaptionSupport;
  @override
  @JsonKey(name: 'voice_language')
  String get voiceLanguage;
  @override
  @JsonKey(name: 'display_language')
  String get displayLanguage;
  @override
  @JsonKey(ignore: true)
  _$$VoiceDtoImplCopyWith<_$VoiceDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
