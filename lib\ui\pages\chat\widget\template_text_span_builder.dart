import 'package:flutter/material.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:note_x/lib.dart';

class TemplateTextSpanBuilder extends SpecialTextSpanBuilder {
  final BuildContext context;
  final bool isTemplate;
  final ChatTemplateModel? currentTemplate;
  final VoidCallback? onDeleteTemplate;

  TemplateTextSpanBuilder(
    this.context, {
    this.isTemplate = false,
    this.currentTemplate,
    this.onDeleteTemplate,
  });

  @override
  TextSpan build(String data,
      {TextStyle? textStyle, SpecialTextGestureTapCallback? onTap}) {
    if (isTemplate && currentTemplate != null) {
      final templatePrefix = "${currentTemplate!.name} ";
      if (!data.startsWith(templatePrefix)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onDeleteTemplate?.call();
        });
        return TextSpan(text: data, style: textStyle);
      }

      return TextSpan(
        children: [
          TextSpan(
            text: templatePrefix,
            style: textStyle?.copyWith(
              color: getTemplateType(currentTemplate?.type ?? '').color(context),
              fontWeight: FontWeight.w500,
            ),
          ),
          TextSpan(
            text: data.substring(templatePrefix.length),
            style: textStyle?.copyWith(
              color: context.colorScheme.mainPrimary,
            ),
          ),
        ],
      );
    }
    return TextSpan(
      text: data,
      style: textStyle,
    );
  }

  @override
  SpecialText? createSpecialText(String flag,
      {TextStyle? textStyle,
      SpecialTextGestureTapCallback? onTap,
      int? index}) {
    return null;
  }
}
