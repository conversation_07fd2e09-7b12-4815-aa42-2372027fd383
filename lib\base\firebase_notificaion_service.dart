import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:permission_handler/permission_handler.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {}

class FirebaseNotificationService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  static final FirebaseInAppMessaging _firebaseInAppMessaging =
      FirebaseInAppMessaging.instance;

  late final AndroidLocalNotificationSupporter
      _androidLocalNotificationSupporter;

  final _clickedNotificationStreamController =
      StreamController<NotificationPayload>.broadcast();

  Stream<NotificationPayload> get onClickedNotification =>
      _clickedNotificationStreamController.stream;

  FirebaseNotificationService() {
    initialize();
  }

  Future<void> initialize() async {
    if (Platform.isIOS) {
      String? apnsToken = await _firebaseMessaging.getAPNSToken();
      if (apnsToken == null) {
        await Future.delayed(const Duration(seconds: 2));
        apnsToken = await _firebaseMessaging.getAPNSToken();
      }
      debugPrint('Final APNS Token: $apnsToken');
    }
    await Future.wait([
      FirebaseMessaging.instance.getToken(),
      FirebaseMessaging.instance.getInitialMessage(),
      _initializeLocalNotifications(),
    ]);

    _androidLocalNotificationSupporter = AndroidLocalNotificationSupporter(
        config: AndroidNotificationChannelConfig.defaultConfig);

    setupNotificationForeground();
    setupNotificationBackground();
    observerClickedNotification();
    setupInAppMessaging();
  }

  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@drawable/icon_app_transparent');
    final DarwinInitializationSettings initializationSettingsDarwin =
        DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification:
          (int id, String? title, String? body, String? payload) async {},
    );
    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
    );
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _handleNotificationResponse,
    );
  }

  void _handleNotificationResponse(
      NotificationResponse notificationResponse) async {
    final String? payload = notificationResponse.payload;
    if (payload != null) {
      final notificationPayload =
          NotificationPayload.fromJson(json.decode(payload));
      _handleClickedNotification(notificationPayload);
    }
  }

  void setupInAppMessaging() {
    _firebaseInAppMessaging
      ..setMessagesSuppressed(false)
      ..setAutomaticDataCollectionEnabled(true);
  }

  Future<void> triggerInAppMessage(String eventName) =>
      _firebaseInAppMessaging.triggerEvent(eventName);

  Future<void> suppressInAppMessages(bool suppress) =>
      _firebaseInAppMessaging.setMessagesSuppressed(suppress);

  void setupNotificationForeground() {
    FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    _firebaseMessaging.onTokenRefresh.listen((newToken) {
      GetIt.instance.get<LocalService>().setFcmToken(newToken);
    });
  }

  void _handleForegroundMessage(RemoteMessage message) {
    if (message.notification != null) {
      final dataPayload = DataPayload.fromJson(message.data);
      final notificationPayload = message.toNotificationPayload(dataPayload);
      _showLocalNotificationForAndroid(notificationPayload);
      handlePayloadNotification(notificationPayload);
    }
  }

  void setupNotificationBackground() async {
    try {
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
    } catch (e) {
      debugPrint('Error setting up background message handler: $e');
    }
  }

  void handlePayloadNotification(NotificationPayload notificationPayload) {}

  static Future<String?> getFirebaseToken() => _firebaseMessaging.getToken();

  void observerClickedNotification() {
    FirebaseMessaging.onMessageOpenedApp
        .map(_createNotificationPayload)
        .listen(_handleClickedNotification);

    _androidLocalNotificationSupporter.onClickedNotification
        .listen(_handleClickedNotification);
  }

  NotificationPayload _createNotificationPayload(RemoteMessage message) {
    final dataPayload = DataPayload.fromJson(message.data);
    return message.toNotificationPayload(dataPayload);
  }

  void _handleClickedNotification(
      NotificationPayload notificationPayload) async {
    if (notificationPayload.data.type == 'success_note' ||
        notificationPayload.data.type == 'error_note') {
      final note = HiveService().noteBox.get(notificationPayload.data.id) ??
          HiveService().noteFailedBox.get(notificationPayload.data.id);
      if (note != null) {
        _navigateToNoteDetail(note);
      }
    }
    _clickedNotificationStreamController.add(notificationPayload);
  }

  void _navigateToNoteDetail(NoteModel note) async {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context != null) {
      final savedTabs =
          await GetIt.instance.get<LocalService>().loadSelectedItems();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MyNoteDetailPage(
            isCommunityNote: false,
            noteModel: note,
            isTablet: context.isTablet,
            from: NoteDetailPageFrom.notification,
            savedTabs: savedTabs,
          ),
        ),
      );
    }
  }

  void _showLocalNotificationForAndroid(
      NotificationPayload notificationPayload) {
    if (Platform.isAndroid) {
      _androidLocalNotificationSupporter.show(notificationPayload);
    }
  }

  Future<void> requestPermission() async {
    final status = await Permission.notification.status;
    if (status.isDenied || status.isPermanentlyDenied) {
      _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
    }
  }

  // Future<void> requestProvisionalPermission() async {
  //   // For iOS, use provisional notifications right away
  //   if (Platform.isIOS) {
  //     await _firebaseMessaging.requestPermission(
  //       alert: true,
  //       badge: true,
  //       sound: true,
  //       provisional: true,
  //     );
  //   }
  // }

  Future<void> showLocalNotification(NotificationPayload payload) async {
    try {
      const AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        'your_channel_id',
        'Your Channel Name',
        channelDescription: 'Your Channel Description',
        importance: Importance.max,
        priority: Priority.high,
      );
      const NotificationDetails notificationDetails =
          NotificationDetails(android: androidNotificationDetails);
      await flutterLocalNotificationsPlugin.show(
        0,
        payload.title,
        payload.body,
        notificationDetails,
        payload: json.encode(payload.toJson()),
      );
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

// Base function for showing notifications
  static Future<void> showRecordingNotification(
      String title, String body) async {
    flutterLocalNotificationsPlugin.show(
      888,
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'recording_channel',
          'Recording Service',
          ongoing: true,
        ),
      ),
    );
  }

  /// Disposes resources used by this service.
  /// Should be called when the app is shutting down or the service is no longer needed.
  void dispose() {
    _clickedNotificationStreamController.close();
    _androidLocalNotificationSupporter.dispose();
  }
}

extension RemoteMessageExtension on RemoteMessage {
  NotificationPayload toNotificationPayload(DataPayload dataPayload) {
    return NotificationPayload(
      title: notification?.title,
      body: notification?.body,
      data: dataPayload,
    );
  }
}

extension NotificationPayloadExtension on NotificationPayload {
  Map<String, dynamic> toJson() => {
        'title': title,
        'body': body,
        'data': data.toJson(),
      };

  static NotificationPayload fromJson(Map<String, dynamic> json) =>
      NotificationPayload(
        title: json['title'],
        body: json['body'],
        data: DataPayload.fromJson(json['data']),
      );
}
