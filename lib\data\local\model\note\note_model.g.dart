// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NoteModelAdapter extends TypeAdapter<NoteModel> {
  @override
  final int typeId = 1;

  @override
  NoteModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NoteModel(
      id: fields[0] == null ? '' : fields[0] as String,
      title: fields[1] == null ? '' : fields[1] as String,
      subTitle: fields[2] == null ? '' : fields[2] as String,
      icon: fields[3] == null ? '' : fields[3] as String,
      youtubeUrl: fields[4] == null ? '' : fields[4] as String,
      type: fields[5] == null ? '' : fields[5] as String,
      summary: fields[6] == null ? '' : fields[6] as String,
      transcript: fields[7] == null ? '' : fields[7] as String,
      quiz: fields[8] == null ? [] : (fields[8] as List).cast<QuizModel>(),
      flashCard:
          fields[9] == null ? [] : (fields[9] as List).cast<FlashCardModel>(),
      statusQuiz: fields[10] == null ? false : fields[10] as bool,
      statusFlashCard: fields[11] == null ? false : fields[11] as bool,
      audioFilePath: fields[12] == null ? '' : fields[12] as String,
      youtubeId: fields[13] == null ? '' : fields[13] as String,
      userName: fields[14] == null ? '' : fields[14] as String,
      views: fields[15] == null ? '' : fields[15] as String,
      timeStamp: fields[16] == null ? 0 : fields[16] as int,
      hashtags: fields[17] == null ? [] : (fields[17] as List).cast<String>(),
      transcriptJson: fields[18] == null
          ? []
          : (fields[18] as List).cast<TranscriptModel>(),
      audioUrl: fields[19] == null ? '' : fields[19] as String,
      backendNoteId: fields[20] == null ? '' : fields[20] as String,
      noteStatus:
          fields[21] == null ? NoteStatus.init : fields[21] as NoteStatus,
      folderId: fields[22] == null ? '' : fields[22] as String,
      statusMindMap: fields[23] == null ? false : fields[23] as bool,
      savingRecording: fields[24] == null
          ? const ProcessModel(
              status: ProcessStatus.completed,
              stepName: 'Saving recording to device')
          : fields[24] as ProcessModel,
      uploadingToServer: fields[25] == null
          ? const ProcessModel(stepName: 'Uploading to secure server')
          : fields[25] as ProcessModel,
      transcribing: fields[26] == null
          ? const ProcessModel(stepName: 'Transcribing with best AI')
          : fields[26] as ProcessModel,
      generatingAINote: fields[27] == null
          ? const ProcessModel(stepName: 'Generating AI note')
          : fields[27] as ProcessModel,
      currentStep: fields[28] == null
          ? ProcessStep.savingRecording
          : fields[28] as ProcessStep,
      targetLanguage: fields[29] == null ? 'en' : fields[29] as String,
      duration: fields[30] == null ? 0 : fields[30] as int,
      isFeedbackSubmitted: fields[31] == null ? false : fields[31] as bool,
      isUnRead: fields[33] == null ? true : fields[33] as bool,
      userId: fields[32] == null ? '' : fields[32] as String,
      folderName: fields[34] == null ? '' : fields[34] as String,
      folderColor: fields[35] == null ? '' : fields[35] as String,
      documentUrl: fields[36] == null ? '' : fields[36] as String,
      taskId: fields[37] == null ? '' : fields[37] as String,
      isLocalRecording: fields[38] == null ? false : fields[38] as bool,
      isGeneratingMindMap: fields[39] == null ? false : fields[39] as bool,
      isGeneratingFlashCard: fields[40] == null ? false : fields[40] as bool,
      isGeneratingQuiz: fields[41] == null ? false : fields[41] as bool,
      webUrl: fields[42] == null ? '' : fields[42] as String,
      isInsufficientContent: fields[43] == null ? false : fields[43] as bool,
      clipDurations: fields[44] == null ? [] : (fields[44] as List).cast<int>(),
      voices: fields[45] == null ? [] : (fields[45] as List).cast<VoiceModel>(),
      isPublic: fields[46] == null ? false : fields[46] as bool,
      isPasswordProtected: fields[47] == null ? false : fields[47] as bool,
      sharePassword: fields[48] == null ? '' : fields[48] as String,
      shareLink: fields[49] == null ? '' : fields[49] as String,
      suggestions: fields[50] == null
          ? []
          : (fields[50] as List).cast<SuggestionModel>(),
      flashcardSets: fields[51] == null
          ? []
          : (fields[51] as List).cast<FlashcardSetsModel>(),
      quizSets:
          fields[52] == null ? [] : (fields[52] as List).cast<QuizSetsModel>(),
      saveToNotes: fields[53] == null
          ? []
          : (fields[53] as List).cast<ChatMessageModel>(),
      flashcardSetDataModel: fields[54] as FlashcardSetDataModel?,
      quizSetDataModel: fields[55] as QuizSetDataModel?,
      slideUrl: fields[56] == null ? '' : fields[56] as String,
      isGeneratingSlide: fields[57] == null ? false : fields[57] as bool,
      slidePdfUrl: fields[58] == null ? '' : fields[58] as String,
      chatTemplateSuggestions: fields[59] == null
          ? []
          : (fields[59] as List).cast<ChatTemplateModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, NoteModel obj) {
    writer
      ..writeByte(60)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.subTitle)
      ..writeByte(3)
      ..write(obj.icon)
      ..writeByte(4)
      ..write(obj.youtubeUrl)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.summary)
      ..writeByte(7)
      ..write(obj.transcript)
      ..writeByte(8)
      ..write(obj.quiz)
      ..writeByte(9)
      ..write(obj.flashCard)
      ..writeByte(10)
      ..write(obj.statusQuiz)
      ..writeByte(11)
      ..write(obj.statusFlashCard)
      ..writeByte(12)
      ..write(obj.audioFilePath)
      ..writeByte(13)
      ..write(obj.youtubeId)
      ..writeByte(14)
      ..write(obj.userName)
      ..writeByte(15)
      ..write(obj.views)
      ..writeByte(16)
      ..write(obj.timeStamp)
      ..writeByte(17)
      ..write(obj.hashtags)
      ..writeByte(18)
      ..write(obj.transcriptJson)
      ..writeByte(19)
      ..write(obj.audioUrl)
      ..writeByte(20)
      ..write(obj.backendNoteId)
      ..writeByte(21)
      ..write(obj.noteStatus)
      ..writeByte(22)
      ..write(obj.folderId)
      ..writeByte(23)
      ..write(obj.statusMindMap)
      ..writeByte(24)
      ..write(obj.savingRecording)
      ..writeByte(25)
      ..write(obj.uploadingToServer)
      ..writeByte(26)
      ..write(obj.transcribing)
      ..writeByte(27)
      ..write(obj.generatingAINote)
      ..writeByte(28)
      ..write(obj.currentStep)
      ..writeByte(29)
      ..write(obj.targetLanguage)
      ..writeByte(30)
      ..write(obj.duration)
      ..writeByte(31)
      ..write(obj.isFeedbackSubmitted)
      ..writeByte(32)
      ..write(obj.userId)
      ..writeByte(33)
      ..write(obj.isUnRead)
      ..writeByte(34)
      ..write(obj.folderName)
      ..writeByte(35)
      ..write(obj.folderColor)
      ..writeByte(36)
      ..write(obj.documentUrl)
      ..writeByte(37)
      ..write(obj.taskId)
      ..writeByte(38)
      ..write(obj.isLocalRecording)
      ..writeByte(39)
      ..write(obj.isGeneratingMindMap)
      ..writeByte(40)
      ..write(obj.isGeneratingFlashCard)
      ..writeByte(41)
      ..write(obj.isGeneratingQuiz)
      ..writeByte(42)
      ..write(obj.webUrl)
      ..writeByte(43)
      ..write(obj.isInsufficientContent)
      ..writeByte(44)
      ..write(obj.clipDurations)
      ..writeByte(45)
      ..write(obj.voices)
      ..writeByte(46)
      ..write(obj.isPublic)
      ..writeByte(47)
      ..write(obj.isPasswordProtected)
      ..writeByte(48)
      ..write(obj.sharePassword)
      ..writeByte(49)
      ..write(obj.shareLink)
      ..writeByte(50)
      ..write(obj.suggestions)
      ..writeByte(51)
      ..write(obj.flashcardSets)
      ..writeByte(52)
      ..write(obj.quizSets)
      ..writeByte(53)
      ..write(obj.saveToNotes)
      ..writeByte(54)
      ..write(obj.flashcardSetDataModel)
      ..writeByte(55)
      ..write(obj.quizSetDataModel)
      ..writeByte(56)
      ..write(obj.slideUrl)
      ..writeByte(57)
      ..write(obj.isGeneratingSlide)
      ..writeByte(58)
      ..write(obj.slidePdfUrl)
      ..writeByte(59)
      ..write(obj.chatTemplateSuggestions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
