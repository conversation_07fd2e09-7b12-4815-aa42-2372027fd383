// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_life_time_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PurchaseLifeTimeState {
  PurchaseLifeTimeStateOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  List<Package> get listPackage => throw _privateConstructorUsedError;
  List<Package> get listLifeTimeProPackage =>
      throw _privateConstructorUsedError;
  PurchaseLifetimeType get selectedPackage =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PurchaseLifeTimeStateCopyWith<PurchaseLifeTimeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseLifeTimeStateCopyWith<$Res> {
  factory $PurchaseLifeTimeStateCopyWith(PurchaseLifeTimeState value,
          $Res Function(PurchaseLifeTimeState) then) =
      _$PurchaseLifeTimeStateCopyWithImpl<$Res, PurchaseLifeTimeState>;
  @useResult
  $Res call(
      {PurchaseLifeTimeStateOneShotEvent oneShotEvent,
      List<Package> listPackage,
      List<Package> listLifeTimeProPackage,
      PurchaseLifetimeType selectedPackage});
}

/// @nodoc
class _$PurchaseLifeTimeStateCopyWithImpl<$Res,
        $Val extends PurchaseLifeTimeState>
    implements $PurchaseLifeTimeStateCopyWith<$Res> {
  _$PurchaseLifeTimeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? listPackage = null,
    Object? listLifeTimeProPackage = null,
    Object? selectedPackage = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as PurchaseLifeTimeStateOneShotEvent,
      listPackage: null == listPackage
          ? _value.listPackage
          : listPackage // ignore: cast_nullable_to_non_nullable
              as List<Package>,
      listLifeTimeProPackage: null == listLifeTimeProPackage
          ? _value.listLifeTimeProPackage
          : listLifeTimeProPackage // ignore: cast_nullable_to_non_nullable
              as List<Package>,
      selectedPackage: null == selectedPackage
          ? _value.selectedPackage
          : selectedPackage // ignore: cast_nullable_to_non_nullable
              as PurchaseLifetimeType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseLifeTimeStateImplCopyWith<$Res>
    implements $PurchaseLifeTimeStateCopyWith<$Res> {
  factory _$$PurchaseLifeTimeStateImplCopyWith(
          _$PurchaseLifeTimeStateImpl value,
          $Res Function(_$PurchaseLifeTimeStateImpl) then) =
      __$$PurchaseLifeTimeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {PurchaseLifeTimeStateOneShotEvent oneShotEvent,
      List<Package> listPackage,
      List<Package> listLifeTimeProPackage,
      PurchaseLifetimeType selectedPackage});
}

/// @nodoc
class __$$PurchaseLifeTimeStateImplCopyWithImpl<$Res>
    extends _$PurchaseLifeTimeStateCopyWithImpl<$Res,
        _$PurchaseLifeTimeStateImpl>
    implements _$$PurchaseLifeTimeStateImplCopyWith<$Res> {
  __$$PurchaseLifeTimeStateImplCopyWithImpl(_$PurchaseLifeTimeStateImpl _value,
      $Res Function(_$PurchaseLifeTimeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? listPackage = null,
    Object? listLifeTimeProPackage = null,
    Object? selectedPackage = null,
  }) {
    return _then(_$PurchaseLifeTimeStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as PurchaseLifeTimeStateOneShotEvent,
      listPackage: null == listPackage
          ? _value._listPackage
          : listPackage // ignore: cast_nullable_to_non_nullable
              as List<Package>,
      listLifeTimeProPackage: null == listLifeTimeProPackage
          ? _value._listLifeTimeProPackage
          : listLifeTimeProPackage // ignore: cast_nullable_to_non_nullable
              as List<Package>,
      selectedPackage: null == selectedPackage
          ? _value.selectedPackage
          : selectedPackage // ignore: cast_nullable_to_non_nullable
              as PurchaseLifetimeType,
    ));
  }
}

/// @nodoc

class _$PurchaseLifeTimeStateImpl implements _PurchaseLifeTimeState {
  const _$PurchaseLifeTimeStateImpl(
      {this.oneShotEvent = PurchaseLifeTimeStateOneShotEvent.none,
      final List<Package> listPackage = const [],
      final List<Package> listLifeTimeProPackage = const [],
      this.selectedPackage = PurchaseLifetimeType.essential})
      : _listPackage = listPackage,
        _listLifeTimeProPackage = listLifeTimeProPackage;

  @override
  @JsonKey()
  final PurchaseLifeTimeStateOneShotEvent oneShotEvent;
  final List<Package> _listPackage;
  @override
  @JsonKey()
  List<Package> get listPackage {
    if (_listPackage is EqualUnmodifiableListView) return _listPackage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listPackage);
  }

  final List<Package> _listLifeTimeProPackage;
  @override
  @JsonKey()
  List<Package> get listLifeTimeProPackage {
    if (_listLifeTimeProPackage is EqualUnmodifiableListView)
      return _listLifeTimeProPackage;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listLifeTimeProPackage);
  }

  @override
  @JsonKey()
  final PurchaseLifetimeType selectedPackage;

  @override
  String toString() {
    return 'PurchaseLifeTimeState(oneShotEvent: $oneShotEvent, listPackage: $listPackage, listLifeTimeProPackage: $listLifeTimeProPackage, selectedPackage: $selectedPackage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseLifeTimeStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._listPackage, _listPackage) &&
            const DeepCollectionEquality().equals(
                other._listLifeTimeProPackage, _listLifeTimeProPackage) &&
            (identical(other.selectedPackage, selectedPackage) ||
                other.selectedPackage == selectedPackage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      const DeepCollectionEquality().hash(_listPackage),
      const DeepCollectionEquality().hash(_listLifeTimeProPackage),
      selectedPackage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseLifeTimeStateImplCopyWith<_$PurchaseLifeTimeStateImpl>
      get copyWith => __$$PurchaseLifeTimeStateImplCopyWithImpl<
          _$PurchaseLifeTimeStateImpl>(this, _$identity);
}

abstract class _PurchaseLifeTimeState implements PurchaseLifeTimeState {
  const factory _PurchaseLifeTimeState(
          {final PurchaseLifeTimeStateOneShotEvent oneShotEvent,
          final List<Package> listPackage,
          final List<Package> listLifeTimeProPackage,
          final PurchaseLifetimeType selectedPackage}) =
      _$PurchaseLifeTimeStateImpl;

  @override
  PurchaseLifeTimeStateOneShotEvent get oneShotEvent;
  @override
  List<Package> get listPackage;
  @override
  List<Package> get listLifeTimeProPackage;
  @override
  PurchaseLifetimeType get selectedPackage;
  @override
  @JsonKey(ignore: true)
  _$$PurchaseLifeTimeStateImplCopyWith<_$PurchaseLifeTimeStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
