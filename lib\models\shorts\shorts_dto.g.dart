// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shorts_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShortsDtoImpl _$$ShortsDtoImplFromJson(Map<String, dynamic> json) =>
    _$ShortsDtoImpl(
      audioUrl: json['audio_url'] as String? ?? '',
      subtitleRawData: json['subtitles'] as String? ?? '',
    );

Map<String, dynamic> _$$ShortsDtoImplToJson(_$ShortsDtoImpl instance) =>
    <String, dynamic>{
      'audio_url': instance.audioUrl,
      'subtitles': instance.subtitleRawData,
    };
