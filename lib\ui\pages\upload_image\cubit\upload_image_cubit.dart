import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/upload_image/cubit/upload_image_state.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class UploadImageCubit extends BaseCubit<UploadImageState> {
  UploadImageCubit(super.initialState);

  final _localService = GetIt.instance.get<LocalService>();
  final _createNoteApiService = GetIt.instance.get<CreateNoteApiServiceImpl>();
  final _taskResultApiService = GetIt.instance.get<ResultTaskApiServiceImpl>();
  final _creditApiServiceImpl = GetIt.instance.get<CreditApiServiceImpl>();
  final _fileUploadService = GetIt.instance.get<FileUploadServiceImpl>();

  final FirebaseNotificationService _notificationService =
      FirebaseNotificationService();

  FocusNode focusNodeCreateNote = FocusNode();
  NoteModel _note = NoteDataMapper().mapToEntity(const NoteDto());
  final List<String> _selectedImagePaths = [];

  NoteModel getImageNote() => _note;

  Language? _targetImageLanguage;

  Language? get targetImageLanguage => _targetImageLanguage;

  bool _isAdvancedEnabled = false;

  ValueNotifier<FolderModel> selectFolderNotifier = ValueNotifier(
    FolderModel(
      id: 'all_notes',
      folderName: S.current.all_note,
      backendId: '',
    ),
  );

  void resetState() {
    if (!isClosed) {
      try {
        // Reset state
        emit(UploadImageState.initial());

        // Reset variables
        _note = NoteDataMapper().mapToEntity(const NoteDto());
        _selectedImagePaths.clear();
      } catch (e) {
        debugPrint('Error in resetState: $e');
        // Just reset variables without emitting state if there's an error
        _note = NoteDataMapper().mapToEntity(const NoteDto());
        _selectedImagePaths.clear();
      }
    }
  }

  void setImageLanguage(Language? lang) {
    if (lang != null) {
      _targetImageLanguage = lang;
    }
  }

  Future<void> addImagesFromGallery() async {
    try {
      if (_selectedImagePaths.length >= 10) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithImageOneShotEvent.maxImagesReached,
        ));
        return;
      }

      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.uploadImageAddImage,
      );

      final images = await ImagePicker().pickMultiImage(limit: 10);
      if (images.isEmpty) return;

      final remainingSlots = 10 - _selectedImagePaths.length;
      final imagePaths = images.take(remainingSlots).map((image) => image.path).toList();

      _selectedImagePaths.addAll(imagePaths);
      emit(state.copyWith(
        selectedImagePaths: List.from(_selectedImagePaths),
        hasSelectedImages: true,
      ));

      if (images.length > remainingSlots) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithImageOneShotEvent.maxImagesReached,
        ));
      }

      emit(state.copyWith(
        oneShotEvent: CreateNoteWithImageOneShotEvent.scrollToLastImage,
      ));
    } catch (e) {
      debugPrint('Error picking images: $e');
    }
  }

  void removeImage(int index) {
    if (index >= 0 && index < _selectedImagePaths.length) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.uploadImageDeleteImage,
      );

      _selectedImagePaths.removeAt(index);
      emit(state.copyWith(
        selectedImagePaths: List.from(_selectedImagePaths),
        hasSelectedImages: _selectedImagePaths.isNotEmpty,
      ));
    }
  }

  void swapImages(int oldIndex, int newIndex) {
    if (oldIndex < 0 ||
        oldIndex >= _selectedImagePaths.length ||
        newIndex < 0 ||
        newIndex >= _selectedImagePaths.length) {
      return;
    }

    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.uploadImageSwapImages,
    );

    final image = _selectedImagePaths.removeAt(oldIndex);
    _selectedImagePaths.insert(newIndex, image);

    emit(state.copyWith(
      selectedImagePaths: List.from(_selectedImagePaths),
    ));
  }

  void clearImages() {
    _selectedImagePaths.clear();
    emit(state.copyWith(
      selectedImagePaths: [],
      hasSelectedImages: false,
    ));
  }

  Future<void> onSubmitImages({bool useOcr = true}) async {
    if (_selectedImagePaths.isEmpty) return;

    _logEventTrackingImage();
    if (_isAdvancedEnabled) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.advanced_on,
      );
    }

    if (appCubit.getAppUser().documentCredit > 0 || appCubit.getAppUser().rewardCredits > 0) {
      emit(state.copyWith(isMergingImages: true));

      final pdfPath = await mergeImagesToPdf(_selectedImagePaths);
      emit(state.copyWith(isMergingImages: false));

      if (pdfPath == null) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithImageOneShotEvent.mergePdfFailed,
        ));
        return;
      }

      emit(state.copyWith(
        oneShotEvent: CreateNoteWithImageOneShotEvent.mergePdfCompleted,
      ));

      final idNoteLocal = MyUtils.generateUniqueId();
      final noteModel = await _createInitialNoteModel(idNoteLocal, pdfPath);
      _note = noteModel;

      await HiveService().createOrUpdateNote(idNoteLocal, noteModel);
      emit(state.copyWith(
        oneShotEvent: CreateNoteWithImageOneShotEvent.createNoteSuccessfully,
      ));

      runCubitCatching(
        handleErrorMessage: true,
        handleLoading: false,
        action: () async {
          final presignedDto = await _fileUploadService.getPresignedUrl(
            fileUniqueName: MyUtils.getFileNameFromPath(pdfPath),
          );

          await _fileUploadService.uploadFileAWS(
            note: noteModel,
            presignedDto: presignedDto,
            filePath: pdfPath,
          );

          final taskDto = await _createNoteApiService.createNotes(
            fileUrl: presignedDto.fileUrl,
            isRecord: false,
            targetLanguage: noteModel.targetLanguage,
            summaryStyle: state.summaryStyle,
            writingStyle: state.writingStyle,
            humanStyle: state.additionalInstructions,
            useOcr: useOcr,
          );

          await _taskResultApiService.getTaskResult(
            taskId: taskDto.taskId,
            localNoteId: idNoteLocal,
            onStatusSuccess: _refreshUserCredits,
          );

          final note = HiveService().noteBox.get(idNoteLocal);
          if (note != null) {
            try {
              await _createNoteApiService.updateNote(
                backendNoteId: note.backendNoteId,
                folderId: selectFolderNotifier.value.backendId,
              );

              await HiveService().noteBox.put(
                idNoteLocal,
                note.copyWith(
                  folderId: selectFolderNotifier.value.backendId,
                  folderName: selectFolderNotifier.value.backendId.isNotEmpty
                      ? selectFolderNotifier.value.folderName
                      : '',
                ),
              );
            } catch (e) {
              debugPrint('Error updating note folder: $e');
            }
          }
        },
        doOnError: (exception) => _getTaskFail(
          idNoteLocal,
          noteModel,
          ProcessStep.uploadingToServer,
        ),
      );
    } else {
      emit(state.copyWith(
        oneShotEvent: CreateNoteWithImageOneShotEvent.onShowIAPFromImage,
      ));
    }
  }

  Future<String?> mergeImagesToPdf(List<String> imagePaths) async {
    try {
      final pdf = pw.Document();
      for (final imagePath in imagePaths) {
        final image = File(imagePath);
        final imageBytes = await image.readAsBytes();
        final pdfImage = pw.MemoryImage(imageBytes);
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(
                  pdfImage,
                  fit: pw.BoxFit.contain,
                  width: PdfPageFormat.a4.availableWidth,
                  height: PdfPageFormat.a4.availableHeight,
                ),
              );
            },
          ),
        );
      }
      final outputDir = await getTemporaryDirectory();
      final pdfFile = File(
          '${outputDir.path}/merged_images_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await pdfFile.writeAsBytes(await pdf.save());
      return pdfFile.path;
    } catch (e) {
      debugPrint('Error merging images to PDF: $e');
      return null;
    }
  }

  void _logEventTrackingImage() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.uploadImageSubmitClicked,
    );
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.general_event_create_note,
    );
  }

  void _logEventTrackingImageFail() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.uploadImageUploadFailed,
    );
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.general_event_create_note_fail,
    );
  }

  void logEventTrackingOpenImagePage() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.home_image,
    );
    AnalyticsService.logEventScreenView(
        screenName: EventScreenClass.uploadImagePage,
        screenClass: EventScreenClass.uploadImagePage);
  }

  void logEventTrackingImageBack() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.uploadImageBackClicked,
    );
  }

  void logEventTrackingPreviewImage() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.uploadImagePreviewImage,
    );
  }

  ProcessModel updateProcessIfMatch(
    ProcessStep stepIndex,
    ProcessModel process,
    ProcessStep step,
  ) {
    return stepIndex == step
        ? process.copyWith(
            status: ProcessStatus.failed,
          )
        : process;
  }

  Future<void> _getTaskFail(
    String idNoteLocal,
    NoteModel noteModel,
    ProcessStep stepIndex,
  ) async {
    _logEventTrackingImageFail();
    final note = HiveService().noteBox.get(idNoteLocal);
    if (note == null) return;
    await HiveService().createOrUpdateNote(
      idNoteLocal,
      note.copyWith(
        noteStatus: NoteStatus.error,
        title: "Image",
        subtitle:
            MyUtils.formatDateTimeV2(DateTime.now().millisecondsSinceEpoch),
        timeStamp: DateTime.now().millisecondsSinceEpoch,
        currentStep: stepIndex,
        uploadingToServer: updateProcessIfMatch(
            stepIndex, note.uploadingToServer, ProcessStep.uploadingToServer),
        transcribing: stepIndex == ProcessStep.generatingAINote
            ? note.transcribing.copyWith(
                status: ProcessStatus.completed,
                progressValue: 1.0,
              )
            : note.transcribing,
        generatingAINote: updateProcessIfMatch(
            stepIndex, note.generatingAINote, ProcessStep.generatingAINote),
      ),
    );
    await _notificationService.showLocalNotification(NotificationPayload(
      title: S.current.title_error_note,
      body: noteTypeToErrorMessage[noteModel.type] ??
          S.current.body_error_note_document,
      data: DataPayload(
        type: 'error_note',
        title: S.current.title_error_note,
        id: idNoteLocal,
      ),
    ));
    _refreshUserCredits();
  }

  Future<NoteModel> _createInitialNoteModel(
    String idNoteLocal,
    String? filePath,
  ) async {
    return NoteDataMapper().mapToEntity(const NoteDto()).copyWith(
          id: idNoteLocal,
          title: 'Image',
          subtitle:
              MyUtils.formatDateTimeV2(DateTime.now().millisecondsSinceEpoch),
          type: NoteType.document.backendType,
          // Note always shows at the top of the list when created
          timeStamp: DateTime.now()
              .add(const Duration(days: 2))
              .millisecondsSinceEpoch,
          audioFilePath: filePath,
          noteStatus: NoteStatus.loading,
          targetLanguage: _targetImageLanguage?.code ?? 'auto',
          uploadingToServer: ProcessModel(
            stepName: S.current.uploading_to_server,
            status: ProcessStatus.inProgress,
            progressValue: 0.0,
          ),
          currentStep: ProcessStep.uploadingToServer,
          transcribing: ProcessModel(
            stepName: S.current.transcribing,
            status: ProcessStatus.initial,
          ),
          generatingAINote: ProcessModel(
            stepName: S.current.generating_ai_note,
            status: ProcessStatus.initial,
          ),
          userId: appCubit.getAppUser().id,
        );
  }

  void _refreshUserCredits() async {
    final creditInfo = await _creditApiServiceImpl.fetchDataCredit();
    final user = appCubit.getAppUser().copyWith(
        youtubeCredit: creditInfo.youtubeCredit,
        webCredit: creditInfo.webCredit,
        audioCredit: creditInfo.audioCredit,
        rewardCredits: creditInfo.rewardCredits,
        shortsCredit: creditInfo.shortsCredit,
        documentCredit: creditInfo.documentCredit,
        userType: mapUserTypeStringToEnum(creditInfo.userType));
    appCubit.updateAppUser(user);
    _localService.saveAppUser(user);
  }

  void resetEnumState() {
    if (!isClosed) {
      emit(state.copyWith(oneShotEvent: CreateNoteWithImageOneShotEvent.none));
    }
  }

  void setAdvancedEnabled(bool enabled) {
    _isAdvancedEnabled = enabled;
  }

  bool get hasUnsavedChanges {
    return _selectedImagePaths.isNotEmpty;
  }

  void updateSummaryStyle(String style) {
    emit(state.copyWith(summaryStyle: style));
  }

  void updateWritingStyle(String style) {
    emit(state.copyWith(writingStyle: style));
  }

  void updateAdditionalInstructions(String instructions) {
    emit(state.copyWith(additionalInstructions: instructions));
  }

  @override
  Future<void> close() {
    focusNodeCreateNote.unfocus();
    focusNodeCreateNote.dispose();
    return super.close();
  }
}
