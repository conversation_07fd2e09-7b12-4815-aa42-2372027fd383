import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
part 'app_color_scheme.dart';

AppThemeEntity get _appTheme {
  final themeNotifier = GetIt.instance.get<ThemeNotifier>();
  if (themeNotifier.value.id == 0) {
    final isDark =
        WidgetsBinding.instance.platformDispatcher.platformBrightness ==
            Brightness.dark;
    return isDark ? AppThemeConst.dark : AppThemeConst.light;
  }
  return themeNotifier.value;
}

ThemeData get lightTheme => ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        primary: _appTheme.gray.toColor,
        brightness: Brightness.light,
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: Colors.blue,
        selectionColor: Colors.blue.withOpacity(0.3),
        selectionHandleColor: Colors.blue,
      ),
      brightness: Brightness.light,
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: _appTheme.primary.toColor,
        ),
      ),
      scaffoldBackgroundColor: _appTheme.bg.toColor,
      bottomSheetTheme: const BottomSheetThemeData(
        dragHandleColor: Colors.black,
      ),
      textTheme: TextTheme(
        labelMedium: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.fontSFPro,
        ),
      ),
      cupertinoOverrideTheme: const NoDefaultCupertinoThemeData(
        primaryColor: Colors.blue,
      ),
      appBarTheme: const AppBarTheme(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
      ),
    );

ThemeData get darkTheme => ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        primary: _appTheme.gray.toColor,
        brightness: Brightness.dark,
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: Colors.blue,
        selectionColor: Colors.blue.withOpacity(0.3),
        selectionHandleColor: Colors.blue,
      ),
      brightness: Brightness.dark,
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: _appTheme.primary.toColor,
        ),
      ),
      scaffoldBackgroundColor: _appTheme.bg.toColor,
      bottomSheetTheme: const BottomSheetThemeData(
        dragHandleColor: Colors.white,
      ),
      textTheme: TextTheme(
        labelMedium: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
          fontFamily: AppConstants.fontSFPro,
        ),
      ),
      cupertinoOverrideTheme: const NoDefaultCupertinoThemeData(
        primaryColor: Colors.blue,
      ),
      appBarTheme: const AppBarTheme(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
      ),
    );
