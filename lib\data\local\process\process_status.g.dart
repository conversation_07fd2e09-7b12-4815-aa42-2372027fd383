// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'process_status.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProcessStatusAdapter extends TypeAdapter<ProcessStatus> {
  @override
  final int typeId = 8;

  @override
  ProcessStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ProcessStatus.initial;
      case 1:
        return ProcessStatus.inProgress;
      case 2:
        return ProcessStatus.completed;
      case 3:
        return ProcessStatus.failed;
      default:
        return ProcessStatus.initial;
    }
  }

  @override
  void write(BinaryWriter writer, ProcessStatus obj) {
    switch (obj) {
      case ProcessStatus.initial:
        writer.writeByte(0);
        break;
      case ProcessStatus.inProgress:
        writer.writeByte(1);
        break;
      case ProcessStatus.completed:
        writer.writeByte(2);
        break;
      case ProcessStatus.failed:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProcessStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
