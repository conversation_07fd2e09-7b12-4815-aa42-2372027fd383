import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class SearchResultItem extends StatelessWidget {
  final NoteModel note;
  final String searchQuery;
  final bool isTablet;

  const SearchResultItem({
    super.key,
    required this.note,
    required this.searchQuery,
    required this.isTablet,
  });

  @override
  Widget build(BuildContext context) {
    final noteType = NoteItemType.fromString(note.type);

    return GestureDetector(
      onTap: () async {
        final savedTabs =
            await GetIt.instance.get<LocalService>().loadSelectedItems();
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MyNoteDetailPage(
              noteModel: note,
              isTablet: isTablet,
              isCommunityNote: false,
              from: NoteDetailPageFrom.searchScreen,
              savedTabs: savedTabs,
            ),
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: context.colorScheme.mainNeutral,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            HighlightedText(
              text: note.title,
              searchQuery: _extractSearchText(searchQuery),
              style: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            isTablet ? const SizedBox(height: 4) : SizedBox(height: 4.h),
            _buildHighlightedContent(
                context, note, _extractSearchText(searchQuery)),
            CommonText(
              _buildDateTimeAndDurationText(note),
              style: TextStyle(
                fontSize: isTablet ? 14 : 12.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainPrimary.withOpacity(0.6),
                fontFamily: AppConstants.fontSFPro,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildTagTypeNote(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        height: 16.h,
                        width: 16.w,
                        noteType.iconPath,
                        colorFilter: ColorFilter.mode(
                          noteType.color,
                          BlendMode.srcIn,
                        ),
                      ),
                      context.isTablet
                          ? const SizedBox(width: 4)
                          : AppConstants.kSpacingItemW4,
                      CommonText(
                        noteType.typeName,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: context.isTablet ? 14 : 12.sp,
                          color: noteType.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                if (note.folderName.isNotEmpty) ...[
                  _buildTagFolder(
                    context,
                    CommonText(
                      note.folderName.length > 20
                          ? '${note.folderName.substring(0, 20)}...'
                          : note.folderName,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: context.isTablet ? 14 : 12.sp,
                        color: context.colorScheme.mainGray,
                        fontFamily: AppConstants.fontSFPro,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _extractSearchText(String searchQuery) {
    if (searchQuery.contains(SearchCubit.TAG_SEPARATOR)) {
      final parts = searchQuery.split(SearchCubit.TAG_SEPARATOR);
      return parts[1].trim();
    }
    return searchQuery;
  }

  Widget _buildHighlightedContent(
      BuildContext context, NoteModel note, String searchQuery) {
    if (searchQuery.isEmpty) return const SizedBox.shrink();
    final lowercaseQuery = searchQuery.toLowerCase();
    String? contentToShow;
    if (note.summary.isNotEmpty &&
        note.summary.toLowerCase().contains(lowercaseQuery)) {
      contentToShow = note.summary;
    } else if (note.transcript.isNotEmpty &&
        note.transcript.toLowerCase().contains(lowercaseQuery)) {
      contentToShow = note.transcript;
    }
    if (contentToShow == null) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isTablet ? const SizedBox(height: 4) : SizedBox(height: 4.h),
        HighlightedText(
          text: _extractRelevantText(contentToShow, searchQuery),
          searchQuery: searchQuery,
          style: TextStyle(
            fontSize: isTablet ? 15 : 13.sp,
            fontWeight: FontWeight.w400,
            color: context.colorScheme.mainGray,
            fontFamily: AppConstants.fontSFPro,
          ),
        ),
        isTablet ? const SizedBox(height: 4) : SizedBox(height: 4.h),
      ],
    );
  }

  Widget _buildTagFolder(BuildContext context, Widget child) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 4.h,
        horizontal: 8.w,
      ),
      decoration: BoxDecoration(
        color: context.colorScheme.mainSecondary,
        borderRadius: BorderRadius.circular(100.r),
      ),
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            Assets.icons.icFolderItem,
            height: 12.h,
            width: 12.w,
          ),
          AppConstants.kSpacingItemW4,
          Flexible(child: child),
        ],
      ),
    );
  }

  Widget _buildTagTypeNote(Widget child) {
    final noteType = NoteItemType.fromString(note.type);
    return Container(
      height: 24.h,
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        color: noteType.containerColor,
        borderRadius: BorderRadius.circular(100.r),
      ),
      constraints: BoxConstraints(minWidth: 60.w),
      alignment: Alignment.center,
      child: child,
    );
  }
}

String _buildDateTimeAndDurationText(NoteModel noteModel) {
  if (noteModel.duration > 0) {
    return "${noteModel.subTitle} · ${MyUtils.formatDuration(noteModel.duration)}";
  } else {
    return noteModel.subTitle;
  }
}

String _extractRelevantText(String content, String query) {
  final lowercaseSummary = content.toLowerCase();
  final lowercaseQuery = query.toLowerCase();
  final matchIndex = lowercaseSummary.indexOf(lowercaseQuery);

  if (matchIndex == -1) {
    return content.length > 50 ? '${content.substring(0, 50)}...' : content;
  }

  final startIndex = (matchIndex - 20).clamp(0, content.length);
  final endIndex = (matchIndex + query.length + 20).clamp(0, content.length);

  String result = content.substring(startIndex, endIndex);
  if (startIndex > 0) result = '...$result';
  if (endIndex < content.length) result = '$result...';

  return result;
}

String getTypeName(String noteType) {
  return NoteItemType.fromString(noteType).typeName;
}

enum NoteItemType {
  youtubeUrl,
  website,
  audioFile,
  audio,
  document;

  factory NoteItemType.fromString(String type) {
    switch (type) {
      case 'youtube_url':
        return NoteItemType.youtubeUrl;
      case 'website':
        return NoteItemType.website;
      case 'audio_file':
        return NoteItemType.audioFile;
      case 'audio':
        return NoteItemType.audio;
      case 'document':
        return NoteItemType.document;
      default:
        return NoteItemType.website;
    }
  }

  String get iconPath {
    switch (this) {
      case NoteItemType.youtubeUrl:
        return Assets.icons.icWelcomePageYoutube;
      case NoteItemType.website:
        return Assets.icons.icHomeYt;
      case NoteItemType.audioFile:
        return Assets.icons.icHomeAudio;
      case NoteItemType.audio:
        return Assets.icons.icRecord;
      case NoteItemType.document:
        return Assets.icons.icHomeDoc;
    }
  }

  Color get color {
    switch (this) {
      case NoteItemType.youtubeUrl:
        return AppColors.primaryRed;
      case NoteItemType.website:
        return AppColors.primaryPurple;
      case NoteItemType.audioFile:
        return AppColors.primaryYellow;
      case NoteItemType.audio:
        return AppColors.primaryBlue;
      case NoteItemType.document:
        return AppColors.primaryGreen;
    }
  }

  Color get containerColor {
    return color.withOpacity(0.24);
  }

  String get typeName {
    switch (this) {
      case NoteItemType.youtubeUrl:
        return S.current.youtube;
      case NoteItemType.website:
        return S.current.web_link;
      case NoteItemType.audio:
        return S.current.record;
      case NoteItemType.audioFile:
        return S.current.audio;
      case NoteItemType.document:
        return S.current.document_tab;
    }
  }
}
