import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class SuggestedTagItem extends StatelessWidget {
  final String tag;
  final VoidCallback onTagSelected;
  final bool isTablet;

  const SuggestedTagItem({
    super.key,
    required this.tag,
    required this.onTagSelected,
    required this.isTablet,
  });

  @override
  Widget build(BuildContext context) {
    final noteType = NoteItemType.fromString(tag);
    final typeName = getTypeName(tag);

    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTagSelected,
      child: Container(
        decoration: BoxDecoration(
          color: noteType.containerColor,
          borderRadius: BorderRadius.circular(100.r),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Tag icon
              SvgPicture.asset(
                height: 16.h,
                width: 16.w,
                noteType.iconPath,
                colorFilter: ColorFilter.mode(
                  noteType.color,
                  BlendMode.srcIn,
                ),
              ),
              // Spacing
              isTablet ? const SizedBox(width: 4) : AppConstants.kSpacingItemW4,
              // Tag name
              CommonText(
                typeName,
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: isTablet ? 16 : 14.sp,
                  height: 1,
                  color: noteType.color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
