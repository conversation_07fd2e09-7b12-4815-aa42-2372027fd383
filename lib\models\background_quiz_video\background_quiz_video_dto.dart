// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'background_quiz_video_dto.freezed.dart';

part 'background_quiz_video_dto.g.dart';

@freezed
class BackgroundQuizVideoDto with _$BackgroundQuizVideoDto {
  const factory BackgroundQuizVideoDto({
    @J<PERSON><PERSON>ey(name: 'template_id') @Default('') String templateId,
    @Default('') String title,
    @Json<PERSON>ey(name: 'thumbnail_url') @Default('') String thumbnailUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'video_url') @Default('') String videoUrl,
    @Default(0) int width,
    @Default(0) int height,
    @J<PERSON><PERSON><PERSON>(name: 'display_order') @Default(0) int displayOrder,
  }) = _BackgroundQuizVideoDto;

  factory BackgroundQuizVideoDto.fromJson(Map<String, dynamic> json) =>
      _$BackgroundQuizVideoDtoFromJson(json);
}
