
import 'package:flutter/widgets.dart';
import 'package:note_x/base/base.dart';

enum TemplateType {
  none,
  outline,
  actionItems,
  blogPost,
  recapEmail,
  projectUpdate,
  linkedinPost,
  twitterPost,
  newsletter,
}

// outline

// action_items

// blog_post

// recap_email

// project_update

// linkedin_post

// twitter_post

// newsletter
TemplateType getTemplateType(String type) {
  switch (type) {
    case 'outline':
      return TemplateType.outline;
    case 'action_items':
      return TemplateType.actionItems;
    case 'blog_post':
      return TemplateType.blogPost;
    case 'recap_email':
      return TemplateType.recapEmail;
    case 'project_update':
      return TemplateType.projectUpdate;
    case 'linkedin_post':
      return TemplateType.linkedinPost;
    case 'twitter_post':
      return TemplateType.twitterPost;
    case 'newsletter':
      return TemplateType.newsletter;
    default:
      return TemplateType.none;
  }
}

extension TemplateTypeExtension on TemplateType {
  String get image {
    switch (this) {
      case TemplateType.outline:
        return Assets.icons.icHomeDoc;
      case TemplateType.actionItems:
        return Assets.icons.icToDoList;
      case TemplateType.blogPost:
        return Assets.icons.icPencil;
      case TemplateType.recapEmail:
        return Assets.icons.icEmail;
      case TemplateType.projectUpdate:
        return Assets.icons.icCalendar;
      case TemplateType.linkedinPost:
        return Assets.icons.icLinkedin;
      case TemplateType.twitterPost:
        return Assets.icons.icTweet;
      case TemplateType.newsletter:
        return Assets.icons.icEmail2;
      default:
        return '';
    }
  }

  Color color(BuildContext context) {
    switch (this) {
      case TemplateType.outline:
        return AppColors.primaryYellow;
      case TemplateType.actionItems:
        return AppColors.primaryGreen;
      case TemplateType.blogPost:
        return AppColors.primaryPurple;
      case TemplateType.recapEmail:
        return AppColors.primaryViolet;
      case TemplateType.projectUpdate:
        return AppColors.primaryBlue;
      case TemplateType.linkedinPost:
        return AppColors.primaryBlue;
      case TemplateType.twitterPost:
        return context.colorScheme.mainPrimary;
      case TemplateType.newsletter:
        return AppColors.primaryGreen;
      default:
        return AppColors.primaryBlue;
    }
  }
}
