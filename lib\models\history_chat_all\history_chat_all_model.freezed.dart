// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_chat_all_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

HistoryChatAllModel _$HistoryChatAllModelFromJson(Map<String, dynamic> json) {
  return _HistoryChatAllModel.fromJson(json);
}

/// @nodoc
mixin _$HistoryChatAllModel {
  List<HistoryChatModel> get historyChatModels =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoryChatAllModelCopyWith<HistoryChatAllModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryChatAllModelCopyWith<$Res> {
  factory $HistoryChatAllModelCopyWith(
          HistoryChatAllModel value, $Res Function(HistoryChatAllModel) then) =
      _$HistoryChatAllModelCopyWithImpl<$Res, HistoryChatAllModel>;
  @useResult
  $Res call({List<HistoryChatModel> historyChatModels});
}

/// @nodoc
class _$HistoryChatAllModelCopyWithImpl<$Res, $Val extends HistoryChatAllModel>
    implements $HistoryChatAllModelCopyWith<$Res> {
  _$HistoryChatAllModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyChatModels = null,
  }) {
    return _then(_value.copyWith(
      historyChatModels: null == historyChatModels
          ? _value.historyChatModels
          : historyChatModels // ignore: cast_nullable_to_non_nullable
              as List<HistoryChatModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistoryChatAllModelImplCopyWith<$Res>
    implements $HistoryChatAllModelCopyWith<$Res> {
  factory _$$HistoryChatAllModelImplCopyWith(_$HistoryChatAllModelImpl value,
          $Res Function(_$HistoryChatAllModelImpl) then) =
      __$$HistoryChatAllModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<HistoryChatModel> historyChatModels});
}

/// @nodoc
class __$$HistoryChatAllModelImplCopyWithImpl<$Res>
    extends _$HistoryChatAllModelCopyWithImpl<$Res, _$HistoryChatAllModelImpl>
    implements _$$HistoryChatAllModelImplCopyWith<$Res> {
  __$$HistoryChatAllModelImplCopyWithImpl(_$HistoryChatAllModelImpl _value,
      $Res Function(_$HistoryChatAllModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? historyChatModels = null,
  }) {
    return _then(_$HistoryChatAllModelImpl(
      historyChatModels: null == historyChatModels
          ? _value._historyChatModels
          : historyChatModels // ignore: cast_nullable_to_non_nullable
              as List<HistoryChatModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoryChatAllModelImpl implements _HistoryChatAllModel {
  const _$HistoryChatAllModelImpl(
      {final List<HistoryChatModel> historyChatModels = const []})
      : _historyChatModels = historyChatModels;

  factory _$HistoryChatAllModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoryChatAllModelImplFromJson(json);

  final List<HistoryChatModel> _historyChatModels;
  @override
  @JsonKey()
  List<HistoryChatModel> get historyChatModels {
    if (_historyChatModels is EqualUnmodifiableListView)
      return _historyChatModels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_historyChatModels);
  }

  @override
  String toString() {
    return 'HistoryChatAllModel(historyChatModels: $historyChatModels)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryChatAllModelImpl &&
            const DeepCollectionEquality()
                .equals(other._historyChatModels, _historyChatModels));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_historyChatModels));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryChatAllModelImplCopyWith<_$HistoryChatAllModelImpl> get copyWith =>
      __$$HistoryChatAllModelImplCopyWithImpl<_$HistoryChatAllModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoryChatAllModelImplToJson(
      this,
    );
  }
}

abstract class _HistoryChatAllModel implements HistoryChatAllModel {
  const factory _HistoryChatAllModel(
          {final List<HistoryChatModel> historyChatModels}) =
      _$HistoryChatAllModelImpl;

  factory _HistoryChatAllModel.fromJson(Map<String, dynamic> json) =
      _$HistoryChatAllModelImpl.fromJson;

  @override
  List<HistoryChatModel> get historyChatModels;
  @override
  @JsonKey(ignore: true)
  _$$HistoryChatAllModelImplCopyWith<_$HistoryChatAllModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
