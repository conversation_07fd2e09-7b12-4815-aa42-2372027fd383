// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'process_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProcessModelAdapter extends TypeAdapter<ProcessModel> {
  @override
  final int typeId = 7;

  @override
  ProcessModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProcessModel(
      status: fields[0] == null
          ? ProcessStatus.initial
          : fields[0] as ProcessStatus,
      progressValue: fields[1] == null ? 0.0 : fields[1] as double,
      stepName: fields[2] == null ? '' : fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ProcessModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.status)
      ..writeByte(1)
      ..write(obj.progressValue)
      ..writeByte(2)
      ..write(obj.stepName);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProcessModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
