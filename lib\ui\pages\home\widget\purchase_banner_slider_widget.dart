import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

/// A widget that displays a slider of purchase banners for free users.
/// This widget handles its own state and auto-scrolling functionality.
class PurchaseBannerSliderWidget extends StatefulWidget {
  const PurchaseBannerSliderWidget({
    super.key,
  });

  @override
  State<PurchaseBannerSliderWidget> createState() =>
      _PurchaseBannerSliderWidgetState();
}

class _PurchaseBannerSliderWidgetState
    extends State<PurchaseBannerSliderWidget> {
  final PageController _pageController = PageController();
  final appCubit = GetIt.instance.get<AppCubit>();

  Timer? _autoScrollTimer;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          height: context.isLandscape ? 142.h : 108.h,
          child: PageView.builder(
            controller: _pageController,
            itemCount: 3,
            itemBuilder: (context, pageIndex) {
              switch (pageIndex) {
                case 0:
                  return HomePurchaseBanner(
                    title: S.current.upgrade_pro_2,
                    content: S.current.achieve_more,
                    backgroundImage: Assets.icons.icHomeUpgradePro,
                    onTap: () {
                      Navigator.push(
                        context,
                        CupertinoPageRoute(
                          builder: (context) => const PurchasePage(
                            from: PurchasePageFrom.iapHome,
                          ),
                          fullscreenDialog: true,
                        ),
                      );
                    },
                    colorTitle: const Color(0xFF2177FF),
                    colorContainerTitle: const Color(0xFF5A99FD),
                  );
                case 1:
                  return HomePurchaseBanner(
                    title: S.current.sale_off,
                    content: S.current.special_gift_title,
                    backgroundImage: Assets.images.imgBgSetting,
                    onTap: () {
                      Navigator.of(context).push(
                        CupertinoPageRoute(
                          builder: (context) {
                            return const PurchasePage(
                              from: PurchasePageFrom.iapBannerHome,
                              isUiSpecial: true,
                            );
                          },
                          fullscreenDialog: true,
                        ),
                      );
                    },
                    colorTitle: const Color(0xFF4083F6),
                    colorContainerTitle: const Color(0xFF4083F6),
                  );
                case 2:
                  return HomePurchaseBanner(
                    title: S.current.referral_02,
                    content: S.current.unlock_toge,
                    backgroundImage: Assets.icons.icHomeReferral,
                    onTap: () {
                      AnalyticsService.logAnalyticsEventNoParam(
                        eventName: EventName.home_referral,
                      );
                      showReferralDialog(context);
                    },
                    colorTitle: const Color(0xFFE02653),
                    colorContainerTitle: const Color(0xFFE05073),
                  );
                default:
                  return const SizedBox.shrink();
              }
            },
            onPageChanged: (index) {
              // Update current page without setState
              _currentPage = index;
              _startAutoScroll();
            },
          ),
        ),
        Positioned(
          bottom: context.isLandscape
              ? 24.h
              : context.isTablet
                  ? 16.h
                  : 12.h,
          child: Center(
            child: SmoothPageIndicator(
              controller: _pageController,
              count: 3,
              effect: WormEffect(
                dotHeight: 4,
                dotWidth: 4,
                spacing: 4,
                dotColor: context.colorScheme.mainPrimary.withOpacity(0.5),
                activeDotColor: context.colorScheme.mainPrimary,
              ),
            ),
          ),
        ),
        AppConstants.kSpacingItem40,
      ],
    );
  }

  void _startAutoScroll() {
    if (appCubit.isUserFree()) {
      _autoScrollTimer?.cancel();

      _autoScrollTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
        if (!mounted || !_pageController.hasClients) {
          timer.cancel();
          return;
        }

        if (_currentPage < 2) {
          _currentPage++;
          _pageController.animateToPage(
            _currentPage,
            duration: const Duration(milliseconds: 600),
            curve: Curves.easeInOut,
          );
        } else {
          _currentPage = 0;
          _pageController.jumpToPage(0);
        }
      });
    }
  }
}
