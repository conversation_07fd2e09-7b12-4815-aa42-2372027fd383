import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';

part 'upload_image_state.freezed.dart';

@freezed
class UploadImageState extends BaseState with _$UploadImageState {
  const factory UploadImageState({
    @Default(CreateNoteWithImageOneShotEvent.none)
    CreateNoteWithImageOneShotEvent oneShotEvent,
    @Default([]) List<String> selectedImagePaths,
    @Default(false) bool hasSelectedImages,
    @Default(false) bool isMergingImages,
    @Default('') String summaryStyle,
    @Default('') String writingStyle,
    @Default('') String additionalInstructions,
  }) = _UploadImageState;

  factory UploadImageState.initial() => const UploadImageState();
}

enum CreateNoteWithImageOneShotEvent {
  none,
  createNoteSuccessfully,
  onShowIAPFromImage,
  maxImagesReached,
  mergePdfFailed,
  mergePdfCompleted,
  scrollToLastImage,
}
