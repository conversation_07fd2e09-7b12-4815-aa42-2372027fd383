import 'dart:io';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:note_x/lib.dart';

abstract class AuthApiService {
  Future<LoginEntity> login({required String firebaseToken});

  Future<LoginEntity> refreshToken(String refreshToken);

  Future<bool> logout();

  Future<bool> deleteAccount();
}

class AuthApiServiceImpl extends BaseApiService implements AuthApiService {
  @override
  Future<LoginEntity> login({required String firebaseToken}) async {
    try {
      await ensureConnection();
      String? fcmToken = await FirebaseNotificationService.getFirebaseToken();
      final localService = GetIt.instance.get<LocalService>();

      final Map<String, dynamic> postData = {
        "firebaseToken": firebaseToken,
        "platform": Platform.isAndroid ? 'ANDROID' : 'IOS',
        "fcmToken": fcmToken,
        "displayName": FirebaseAuth.instance.currentUser?.displayName,
        "onboardingUserType": localService.getOnboardingUserType(),
        "locale": Intl.systemLocale,
        "storeCountryCode": Platform.isIOS
            ? localService.getStoreCountryCode()
            : Intl.systemLocale,
      };
      final response = await dio.post(
        'v3/auth/login',
        data: postData,
        options: Options(
          sendTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
        ),
      );

      BaseDataDto<LoginEntity> data = BaseDataDto<LoginEntity>.fromJson(
        response.data,
        (json) => LoginEntity.fromJson(json as Map<String, dynamic>),
      );
      final userDto = data.data;
      localService.saveLoginInfo(userDto);
      localService.setFcmToken(fcmToken ?? '');
      Map<String, String> attributes = {
        "userID": userDto.id,
        "fcmToken": fcmToken ?? '',
        "onboardingUserType": localService.getOnboardingUserType()
      };
      // Set attributes for webhook
      Purchases.setAttributes(attributes);
      return userDto;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<LoginEntity> refreshToken(String refreshToken) async {
    try {
      final Map<String, dynamic> requestData = {
        "refresh_token": refreshToken,
      };
      final response = await dio.post('v1/auth/refresh', data: requestData);
      BaseDataDto<LoginEntity> data = BaseDataDto<LoginEntity>.fromJson(
        response.data,
        (json) => LoginEntity.fromJson(json as Map<String, dynamic>),
      );
      return data.data;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<bool> deleteAccount() async {
    try {
      final response = await dio.delete('v1/user/delete_account');
      return response.statusCode == 200;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<bool> logout() async {
    try {
      await dio.post('v1/auth/logout');
      return true;
    } catch (err) {
      throw getError(err);
    }
  }
}
