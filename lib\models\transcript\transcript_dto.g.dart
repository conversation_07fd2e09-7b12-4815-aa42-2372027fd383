// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcript_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TranscriptDtoImpl _$$TranscriptDtoImplFromJson(Map<String, dynamic> json) =>
    _$TranscriptDtoImpl(
      text: json['text'] as String? ?? '',
      start: (json['start'] as num?)?.toDouble() ?? 0.0,
      duration: (json['duration'] as num?)?.toDouble() ?? 0.0,
      speaker: json['speaker'] as String? ?? '',
    );

Map<String, dynamic> _$$TranscriptDtoImplToJson(_$TranscriptDtoImpl instance) =>
    <String, dynamic>{
      'text': instance.text,
      'start': instance.start,
      'duration': instance.duration,
      'speaker': instance.speaker,
    };
