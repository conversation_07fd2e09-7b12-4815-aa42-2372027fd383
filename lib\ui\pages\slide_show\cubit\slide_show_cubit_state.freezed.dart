// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slide_show_cubit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$SlideShowCubitState {
  int get horizontalCurrentPage => throw _privateConstructorUsedError;
  int get verticalCurrentPage => throw _privateConstructorUsedError;
  int get horizontalTotalPages => throw _privateConstructorUsedError;
  int get verticalTotalPages => throw _privateConstructorUsedError;
  ViewMode get viewMode => throw _privateConstructorUsedError;
  bool get isPdfLoaded => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get localPdfPath => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SlideShowCubitStateCopyWith<SlideShowCubitState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SlideShowCubitStateCopyWith<$Res> {
  factory $SlideShowCubitStateCopyWith(
          SlideShowCubitState value, $Res Function(SlideShowCubitState) then) =
      _$SlideShowCubitStateCopyWithImpl<$Res, SlideShowCubitState>;
  @useResult
  $Res call(
      {int horizontalCurrentPage,
      int verticalCurrentPage,
      int horizontalTotalPages,
      int verticalTotalPages,
      ViewMode viewMode,
      bool isPdfLoaded,
      bool isLoading,
      String? localPdfPath});
}

/// @nodoc
class _$SlideShowCubitStateCopyWithImpl<$Res, $Val extends SlideShowCubitState>
    implements $SlideShowCubitStateCopyWith<$Res> {
  _$SlideShowCubitStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? horizontalCurrentPage = null,
    Object? verticalCurrentPage = null,
    Object? horizontalTotalPages = null,
    Object? verticalTotalPages = null,
    Object? viewMode = null,
    Object? isPdfLoaded = null,
    Object? isLoading = null,
    Object? localPdfPath = freezed,
  }) {
    return _then(_value.copyWith(
      horizontalCurrentPage: null == horizontalCurrentPage
          ? _value.horizontalCurrentPage
          : horizontalCurrentPage // ignore: cast_nullable_to_non_nullable
              as int,
      verticalCurrentPage: null == verticalCurrentPage
          ? _value.verticalCurrentPage
          : verticalCurrentPage // ignore: cast_nullable_to_non_nullable
              as int,
      horizontalTotalPages: null == horizontalTotalPages
          ? _value.horizontalTotalPages
          : horizontalTotalPages // ignore: cast_nullable_to_non_nullable
              as int,
      verticalTotalPages: null == verticalTotalPages
          ? _value.verticalTotalPages
          : verticalTotalPages // ignore: cast_nullable_to_non_nullable
              as int,
      viewMode: null == viewMode
          ? _value.viewMode
          : viewMode // ignore: cast_nullable_to_non_nullable
              as ViewMode,
      isPdfLoaded: null == isPdfLoaded
          ? _value.isPdfLoaded
          : isPdfLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      localPdfPath: freezed == localPdfPath
          ? _value.localPdfPath
          : localPdfPath // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SlideShowCubitStateImplCopyWith<$Res>
    implements $SlideShowCubitStateCopyWith<$Res> {
  factory _$$SlideShowCubitStateImplCopyWith(_$SlideShowCubitStateImpl value,
          $Res Function(_$SlideShowCubitStateImpl) then) =
      __$$SlideShowCubitStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int horizontalCurrentPage,
      int verticalCurrentPage,
      int horizontalTotalPages,
      int verticalTotalPages,
      ViewMode viewMode,
      bool isPdfLoaded,
      bool isLoading,
      String? localPdfPath});
}

/// @nodoc
class __$$SlideShowCubitStateImplCopyWithImpl<$Res>
    extends _$SlideShowCubitStateCopyWithImpl<$Res, _$SlideShowCubitStateImpl>
    implements _$$SlideShowCubitStateImplCopyWith<$Res> {
  __$$SlideShowCubitStateImplCopyWithImpl(_$SlideShowCubitStateImpl _value,
      $Res Function(_$SlideShowCubitStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? horizontalCurrentPage = null,
    Object? verticalCurrentPage = null,
    Object? horizontalTotalPages = null,
    Object? verticalTotalPages = null,
    Object? viewMode = null,
    Object? isPdfLoaded = null,
    Object? isLoading = null,
    Object? localPdfPath = freezed,
  }) {
    return _then(_$SlideShowCubitStateImpl(
      horizontalCurrentPage: null == horizontalCurrentPage
          ? _value.horizontalCurrentPage
          : horizontalCurrentPage // ignore: cast_nullable_to_non_nullable
              as int,
      verticalCurrentPage: null == verticalCurrentPage
          ? _value.verticalCurrentPage
          : verticalCurrentPage // ignore: cast_nullable_to_non_nullable
              as int,
      horizontalTotalPages: null == horizontalTotalPages
          ? _value.horizontalTotalPages
          : horizontalTotalPages // ignore: cast_nullable_to_non_nullable
              as int,
      verticalTotalPages: null == verticalTotalPages
          ? _value.verticalTotalPages
          : verticalTotalPages // ignore: cast_nullable_to_non_nullable
              as int,
      viewMode: null == viewMode
          ? _value.viewMode
          : viewMode // ignore: cast_nullable_to_non_nullable
              as ViewMode,
      isPdfLoaded: null == isPdfLoaded
          ? _value.isPdfLoaded
          : isPdfLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      localPdfPath: freezed == localPdfPath
          ? _value.localPdfPath
          : localPdfPath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SlideShowCubitStateImpl implements _SlideShowCubitState {
  const _$SlideShowCubitStateImpl(
      {this.horizontalCurrentPage = 1,
      this.verticalCurrentPage = 1,
      this.horizontalTotalPages = 0,
      this.verticalTotalPages = 0,
      this.viewMode = ViewMode.horizontal,
      this.isPdfLoaded = false,
      this.isLoading = false,
      this.localPdfPath});

  @override
  @JsonKey()
  final int horizontalCurrentPage;
  @override
  @JsonKey()
  final int verticalCurrentPage;
  @override
  @JsonKey()
  final int horizontalTotalPages;
  @override
  @JsonKey()
  final int verticalTotalPages;
  @override
  @JsonKey()
  final ViewMode viewMode;
  @override
  @JsonKey()
  final bool isPdfLoaded;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? localPdfPath;

  @override
  String toString() {
    return 'SlideShowCubitState(horizontalCurrentPage: $horizontalCurrentPage, verticalCurrentPage: $verticalCurrentPage, horizontalTotalPages: $horizontalTotalPages, verticalTotalPages: $verticalTotalPages, viewMode: $viewMode, isPdfLoaded: $isPdfLoaded, isLoading: $isLoading, localPdfPath: $localPdfPath)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SlideShowCubitStateImpl &&
            (identical(other.horizontalCurrentPage, horizontalCurrentPage) ||
                other.horizontalCurrentPage == horizontalCurrentPage) &&
            (identical(other.verticalCurrentPage, verticalCurrentPage) ||
                other.verticalCurrentPage == verticalCurrentPage) &&
            (identical(other.horizontalTotalPages, horizontalTotalPages) ||
                other.horizontalTotalPages == horizontalTotalPages) &&
            (identical(other.verticalTotalPages, verticalTotalPages) ||
                other.verticalTotalPages == verticalTotalPages) &&
            (identical(other.viewMode, viewMode) ||
                other.viewMode == viewMode) &&
            (identical(other.isPdfLoaded, isPdfLoaded) ||
                other.isPdfLoaded == isPdfLoaded) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.localPdfPath, localPdfPath) ||
                other.localPdfPath == localPdfPath));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      horizontalCurrentPage,
      verticalCurrentPage,
      horizontalTotalPages,
      verticalTotalPages,
      viewMode,
      isPdfLoaded,
      isLoading,
      localPdfPath);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SlideShowCubitStateImplCopyWith<_$SlideShowCubitStateImpl> get copyWith =>
      __$$SlideShowCubitStateImplCopyWithImpl<_$SlideShowCubitStateImpl>(
          this, _$identity);
}

abstract class _SlideShowCubitState implements SlideShowCubitState {
  const factory _SlideShowCubitState(
      {final int horizontalCurrentPage,
      final int verticalCurrentPage,
      final int horizontalTotalPages,
      final int verticalTotalPages,
      final ViewMode viewMode,
      final bool isPdfLoaded,
      final bool isLoading,
      final String? localPdfPath}) = _$SlideShowCubitStateImpl;

  @override
  int get horizontalCurrentPage;
  @override
  int get verticalCurrentPage;
  @override
  int get horizontalTotalPages;
  @override
  int get verticalTotalPages;
  @override
  ViewMode get viewMode;
  @override
  bool get isPdfLoaded;
  @override
  bool get isLoading;
  @override
  String? get localPdfPath;
  @override
  @JsonKey(ignore: true)
  _$$SlideShowCubitStateImplCopyWith<_$SlideShowCubitStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
