import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';
import 'package:note_x/lib.dart';

part 'slide_show_cubit_state.freezed.dart';

@freezed
class SlideShowCubitState extends BaseState with _$SlideShowCubitState {
  const factory SlideShowCubitState({
    @Default(1) int horizontalCurrentPage,
    @Default(1) int verticalCurrentPage,
    @Default(0) int horizontalTotalPages,
    @Default(0) int verticalTotalPages,
    @Default(ViewMode.horizontal) ViewMode viewMode,
    @Default(false) bool isPdfLoaded,
    @Default(false) bool isLoading,
    String? localPdfPath,
  }) = _SlideShowCubitState;
}
