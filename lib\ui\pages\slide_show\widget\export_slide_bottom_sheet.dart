import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class ExportSlideBottomSheet extends StatelessWidget {
  final NoteModel note;
  final String? localPdfPath;
  final VoidCallback onSharePDF;
  final VoidCallback onSharePPTX;
  const ExportSlideBottomSheet({
    Key? key,
    required this.note,
    required this.localPdfPath,
    required this.onSharePDF,
    required this.onSharePPTX,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonText(
                  S.current.export_as,
                  style: TextStyle(
                    color: context.colorScheme.mainPrimary,
                    fontWeight: FontWeight.w600,
                    fontSize: context.isTablet ? 22 : 20.sp,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: SvgPicture.asset(
                    Assets.icons.icCloseWhite,
                    width: 24.w,
                    height: 24.h,
                    colorFilter: ColorFilter.mode(
                      context.colorScheme.mainGray,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
            AppConstants.kSpacingItem24,
            // Export Options
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 2,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    ExportOptionItem(
                      text: index == 0 ? 'PDF(.pdf)' : 'PowerPoint(.pptx)',
                      onTap: () {
                        Navigator.pop(context);
                        index == 0 ? onSharePDF() : onSharePPTX();
                      },
                    ),
                    if (index == 0) AppConstants.kSpacingItem12,
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
