// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_sets_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuizSetsModelAdapter extends TypeAdapter<QuizSetsModel> {
  @override
  final int typeId = 18;

  @override
  QuizSetsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuizSetsModel(
      setId: fields[0] == null ? '' : fields[0] as String,
      name: fields[1] == null ? '' : fields[1] as String,
      description: fields[2] == null ? '' : fields[2] as String,
      difficulty: fields[3] == null ? '' : fields[3] as String,
      language: fields[4] == null ? '' : fields[4] as String,
      questionsCount: fields[5] == null ? 0 : fields[5] as int,
      questions: fields[6] == null ? [] : (fields[6] as List).cast<QuizModel>(),
      createdAt: fields[7] == null ? '' : fields[7] as String,
      updatedAt: fields[8] == null ? '' : fields[8] as String,
    );
  }

  @override
  void write(BinaryWriter writer, QuizSetsModel obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.setId)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.difficulty)
      ..writeByte(4)
      ..write(obj.language)
      ..writeByte(5)
      ..write(obj.questionsCount)
      ..writeByte(6)
      ..write(obj.questions)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuizSetsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
