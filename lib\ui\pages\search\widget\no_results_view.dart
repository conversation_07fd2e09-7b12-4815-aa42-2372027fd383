import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class NoResultsView extends StatelessWidget {
  final String searchText;
  final bool isTablet;

  const NoResultsView({
    super.key,
    required this.searchText,
    required this.isTablet,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SizedBox(
            height: MediaQuery.of(context).size.height -
                MediaQuery.of(context).viewInsets.bottom -
                150,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Empty state image
                  SvgPicture.asset(
                    Assets.icons.icTempOb,
                    width: isTablet ? 160 : 160.w,
                    height: isTablet ? 160 : 160.h,
                  ),
                  AppConstants.kSpacingItem8,
                  // No results text
                  CommonText(
                    '${S.current.no_results_found} "$searchText"',
                    style: TextStyle(
                      color: context.colorScheme.mainPrimary,
                      fontSize: isTablet ? 22 : 20.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  AppConstants.kSpacingItem4,
                  // Searching all notes text
                  CommonText(
                    S.current.searching_all_notes,
                    style: TextStyle(
                      color: context.colorScheme.mainGray,
                      fontSize: context.isTablet ? 16 : 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
