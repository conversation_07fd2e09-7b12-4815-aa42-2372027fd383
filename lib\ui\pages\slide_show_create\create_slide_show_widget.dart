import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Thêm import này
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:note_x/ui/pages/slide_show_create/cubit/slide_show_create_cubit_state.dart';

class CreateSlideShowWidget extends StatefulWidget {
  final String noteId;
  const CreateSlideShowWidget({
    required this.noteId,
    Key? key,
  }) : super(key: key);

  @override
  State<CreateSlideShowWidget> createState() => _CreateSlideShowWidgetState();
}

class _CreateSlideShowWidgetState
    extends BasePageStateDelegate<CreateSlideShowWidget, SlideShowCreateCubit> {
  final FocusNode _cardCountFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    cubit.getSlideTemplates();
  }

  @override
  void dispose() {
    _cardCountFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<SlideShowCreateCubit, SlideShowCreateCubitState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 16.h,
            ),
            child: SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonText(
                        S.current.settings,
                        style: TextStyle(
                          color: context.colorScheme.mainPrimary,
                          fontWeight: FontWeight.w600,
                          fontSize: context.isTablet ? 22 : 20.sp,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          AnalyticsService.logAnalyticsEventNoParam(
                            eventName: EventName.slide_show_close_click,
                          );
                          Navigator.pop(context);
                        },
                        child: Icon(
                          Icons.close,
                          color: context.colorScheme.mainPrimary,
                          size: context.isTablet ? 24 : 24.w,
                        ),
                      ),
                    ],
                  ),
                  AppConstants.kSpacingItem16,
                  // Advanced Mode toggle
                  CommonText(
                    S.current.slide_range,
                    style: TextStyle(
                      color: state.isAdvancedMode
                          ? context.colorScheme.mainGray
                          : context.colorScheme.mainGray.withOpacity(0.5),
                      fontWeight: FontWeight.w400,
                      fontSize: context.isTablet ? 16 : 14.sp,
                    ),
                  ),
                  AppConstants.kSpacingItem8,
                  TimeRangeSelector(
                    ranges: const [
                      " Auto ",
                      "04 - 06",
                      "07 - 09",
                      "10 - 12",
                    ],
                    onSelected: (index) {
                      cubit.updateCardCount(cardCount: index);
                    },
                  ),

                  /// Templates
                  AppConstants.kSpacingItem16,
                  CommonText(
                    S.current.templates,
                    style: TextStyle(
                      color: context.colorScheme.mainGray,
                      fontWeight: FontWeight.w400,
                      fontSize: context.isTablet ? 16 : 14.sp,
                    ),
                  ),
                  AppConstants.kSpacingItem16,
                  Expanded(
                    child: state.slideTemplates.isEmpty
                        ? Center(
                            child: CommonText(
                              S.current.loading,
                              style: TextStyle(
                                color: context.colorScheme.mainGray,
                                fontWeight: FontWeight.w400,
                                fontSize: context.isTablet ? 16 : 14.sp,
                              ),
                            ),
                          )
                        : GridView.builder(
                            padding: EdgeInsets.only(bottom: 16.h),
                            physics: const BouncingScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              mainAxisSpacing: 12.h,
                              crossAxisSpacing: 12.w,
                              childAspectRatio: 16 / 9,
                            ),
                            itemCount: state.slideTemplates.length,
                            itemBuilder: (context, index) {
                              final template = state.slideTemplates[index];
                              final isSelected =
                                  state.selectedTemplateIndex == index;

                              return GifTemplateItem(
                                template: template,
                                isSelected: isSelected,
                                onTap: () {
                                  if (isSelected) {
                                    cubit.selectTemplate(-1);
                                  } else {
                                    cubit.selectTemplate(index);
                                  }
                                },
                              );
                            },
                          ),
                  ),
                  KeyboardVisibilityBuilder(
                    builder: (context, isKeyboardVisible) {
                      return Padding(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                        ),
                        child: Padding(
                          padding: EdgeInsets.only(
                              top: context.isTablet ? 16 : 16.0.w),
                          child: AppCommonButton(
                            disabled: state.selectedTemplateIndex == -1,
                            width: double.infinity,
                            height: context.isTablet ? 60 : 44.h,
                            borderRadius: BorderRadius.circular(24.r),
                            backgroundColor: state.selectedTemplateIndex != -1
                                ? context.colorScheme.mainBlue
                                : context.colorScheme.mainSecondary,
                            textWidget: Text(
                              S.current.create_slide,
                              style: TextStyle(
                                fontSize: context.isTablet ? 18 : 16.sp,
                                fontWeight: FontWeight.w500,
                                color: state.selectedTemplateIndex != -1
                                    ? context.colorScheme.themeWhite
                                    : context.colorScheme.mainGray,
                              ),
                            ),
                            onPressed: () {
                              AnalyticsService.logAnalyticsEventNoParam(
                                eventName: EventName
                                    .slide_show_create_advanced_clicked,
                              );
                              if (cubit.appCubit.getAppUser().slideCredit > 0 ||
                                  cubit.appCubit.getAppUser().rewardCredits >
                                      0) {
                                cubit.createSlideShow(
                                  noteId: widget.noteId,
                                  cardCount: state.cardCount,
                                  templateId: state
                                      .slideTemplates[
                                          state.selectedTemplateIndex]
                                      .id,
                                );
                                Navigator.of(context).pop();
                              } else {
                                if (cubit.appCubit.isUserFree()) {
                                  Navigator.push(
                                    context,
                                    CupertinoPageRoute(
                                      builder: (context) => const PurchasePage(
                                        from: PurchasePageFrom.iapCreateNote,
                                      ),
                                      fullscreenDialog: true,
                                    ),
                                  ).then((didPurchaseSuccess) {
                                    if (didPurchaseSuccess == true) {
                                      cubit.createSlideShow(
                                        noteId: widget.noteId,
                                        cardCount: state.cardCount,
                                        templateId: state
                                            .slideTemplates[
                                                state.selectedTemplateIndex]
                                            .id,
                                      );
                                    }
                                  });
                                } else {
                                  showBlackCupertinoDialog(
                                    context: context,
                                    title:
                                        S.current.daily_slideshow_limit_reached,
                                    confirmButton: S.current.ok,
                                    confirmButtonTextColor:
                                        context.colorScheme.mainBlue,
                                    message: S.current
                                        .daily_slideshow_limit_reached_detail,
                                  );
                                }
                              }
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
