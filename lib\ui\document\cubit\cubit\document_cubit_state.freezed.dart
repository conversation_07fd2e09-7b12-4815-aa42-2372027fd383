// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_cubit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DocumentCubitState {
  bool get isPdfLoaded => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get localPdfPath => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DocumentCubitStateCopyWith<DocumentCubitState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentCubitStateCopyWith<$Res> {
  factory $DocumentCubitStateCopyWith(
          DocumentCubitState value, $Res Function(DocumentCubitState) then) =
      _$DocumentCubitStateCopyWithImpl<$Res, DocumentCubitState>;
  @useResult
  $Res call({bool isPdfLoaded, bool isLoading, String? localPdfPath});
}

/// @nodoc
class _$DocumentCubitStateCopyWithImpl<$Res, $Val extends DocumentCubitState>
    implements $DocumentCubitStateCopyWith<$Res> {
  _$DocumentCubitStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPdfLoaded = null,
    Object? isLoading = null,
    Object? localPdfPath = freezed,
  }) {
    return _then(_value.copyWith(
      isPdfLoaded: null == isPdfLoaded
          ? _value.isPdfLoaded
          : isPdfLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      localPdfPath: freezed == localPdfPath
          ? _value.localPdfPath
          : localPdfPath // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DocumentCubitStateImplCopyWith<$Res>
    implements $DocumentCubitStateCopyWith<$Res> {
  factory _$$DocumentCubitStateImplCopyWith(_$DocumentCubitStateImpl value,
          $Res Function(_$DocumentCubitStateImpl) then) =
      __$$DocumentCubitStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isPdfLoaded, bool isLoading, String? localPdfPath});
}

/// @nodoc
class __$$DocumentCubitStateImplCopyWithImpl<$Res>
    extends _$DocumentCubitStateCopyWithImpl<$Res, _$DocumentCubitStateImpl>
    implements _$$DocumentCubitStateImplCopyWith<$Res> {
  __$$DocumentCubitStateImplCopyWithImpl(_$DocumentCubitStateImpl _value,
      $Res Function(_$DocumentCubitStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPdfLoaded = null,
    Object? isLoading = null,
    Object? localPdfPath = freezed,
  }) {
    return _then(_$DocumentCubitStateImpl(
      isPdfLoaded: null == isPdfLoaded
          ? _value.isPdfLoaded
          : isPdfLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      localPdfPath: freezed == localPdfPath
          ? _value.localPdfPath
          : localPdfPath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$DocumentCubitStateImpl implements _DocumentCubitState {
  const _$DocumentCubitStateImpl(
      {this.isPdfLoaded = false, this.isLoading = false, this.localPdfPath});

  @override
  @JsonKey()
  final bool isPdfLoaded;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? localPdfPath;

  @override
  String toString() {
    return 'DocumentCubitState(isPdfLoaded: $isPdfLoaded, isLoading: $isLoading, localPdfPath: $localPdfPath)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentCubitStateImpl &&
            (identical(other.isPdfLoaded, isPdfLoaded) ||
                other.isPdfLoaded == isPdfLoaded) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.localPdfPath, localPdfPath) ||
                other.localPdfPath == localPdfPath));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isPdfLoaded, isLoading, localPdfPath);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentCubitStateImplCopyWith<_$DocumentCubitStateImpl> get copyWith =>
      __$$DocumentCubitStateImplCopyWithImpl<_$DocumentCubitStateImpl>(
          this, _$identity);
}

abstract class _DocumentCubitState implements DocumentCubitState {
  const factory _DocumentCubitState(
      {final bool isPdfLoaded,
      final bool isLoading,
      final String? localPdfPath}) = _$DocumentCubitStateImpl;

  @override
  bool get isPdfLoaded;
  @override
  bool get isLoading;
  @override
  String? get localPdfPath;
  @override
  @JsonKey(ignore: true)
  _$$DocumentCubitStateImplCopyWith<_$DocumentCubitStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
