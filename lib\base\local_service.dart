import 'dart:convert';
import 'package:note_x/lib.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalService {
  final String kKeyAuth = 'key_auth';
  final String keyUser = 'key_user';
  final String kKeyOnboarding = 'key_onboarding';
  final String kKeyCredit = 'key_credit';
  final String kKeyFCMToken = 'key_fcm_token';
  final String kKeyUserHasViewedInstructions =
      'key_user_has_viewed_instructions';
  final String keyIdGenPortfolio = 'key_gen_portfolio';
  final String keyNumberOfRateDialogShowed = 'key_number_of_rate';
  final String keyFreePlan = 'key_free_plan';
  final String keyIsFirstInstalled = 'key_is_first_install';
  final String keyAppUser = 'key_app_user';
  final String keyFreeRecordLimitDialogShown = 'keyFreeRecordLimitDialogShown';
  static const String _hasShownFreeTrialDialogKey =
      'has_shown_free_trial_dialog';
  final String keyShowTooltip = 'key_show_tooltip';
  final String keyShowTooltipChat = 'key_has_shown_tooltip_chat';
  final String keyReferral = 'key_referral';
  final String keyNumberOfReferralDialogShown =
      'key_number_of_referral_dialog_shown';
  final String keySelectedLanguage = 'key_selected_language';
  final String keyNoteReadyNotificationEnabled =
      'key_note_ready_notification_enabled';
  final String keyShouldShouldTranscriptLineTooltip =
      'keyShouldShouldTranscriptLineTooltip';
  final String emailWithLink = 'email_with_link';
  final String keyOnboardingUserType = 'keyOnboardingUserType';
  final String keyStoreCountryCode = 'keyStoreCountryCode';
  final String keyNumberOfLimitMessages = 'keyNumberOfLimitMessages';
  final String keyLastShownDate = 'keyLastShownDate';
  final String keyIsFirstCreateNote = 'keyIsFirstCreateNote';
  final String keyFlashcardSetId = 'keyFlashcardSetId';
  final String keyFlashcardSetTitle = 'keyFlashcardSetTitle';
  final String keyQuizSetId = 'keyQuizSetId';
  final String keyQuizSetTitle = 'keyQuizSetTitle';
  final String keySelectedThemeId = 'KeySelectedThemeId';
  final String keySelectedItemsTab = 'keySelectedItemsTab';
  final String keyIsShowSwapAnimation = 'keyIsShowSwapAnimation';

  //NOTE: List key not delete when user logout
  List<String> get keyExcludes => [
        keyNumberOfRateDialogShowed,
        keySelectedItemsTab,
      ];

  final SharedPreferences sharedPreferences = GetIt.instance.get();

  bool isAuthorized() {
    return sharedPreferences.containsKey(kKeyAuth);
  }

  LoginEntity getLoginInfo() {
    if (isAuthorized() &&
        json.decode(sharedPreferences.getString(kKeyAuth)!) != null) {
      return LoginEntity.fromJson(
          json.decode(sharedPreferences.getString(kKeyAuth)!));
    } else {
      return const LoginEntity();
    }
  }

  Future saveLoginInfo(LoginEntity? userEntity) {
    return sharedPreferences.setString(
        kKeyAuth, json.encode(userEntity?.toJson()));
  }

  String? getFcmToken() {
    return sharedPreferences.getString(kKeyFCMToken);
  }

  Future setFcmToken(String token) {
    return sharedPreferences.setString(kKeyFCMToken, token);
  }

  bool? getKeyFreePlan() {
    return sharedPreferences.getBool(keyFreePlan) ?? false;
  }

  Future setKeyFreePlan(bool key) {
    return sharedPreferences.setBool(keyFreePlan, key);
  }

  Future setNumberOfRateDialogShown(int counter) {
    return sharedPreferences.setInt(keyNumberOfRateDialogShowed, counter);
  }

  int getNumberOfRateDialogShown() {
    return sharedPreferences.getInt(keyNumberOfRateDialogShowed) ?? 0;
  }

  bool isOnboardingComplete() {
    return sharedPreferences.getBool(kKeyOnboarding) ?? false;
  }

  Future setOnboardingStatus(bool isOnboarding) async {
    return await sharedPreferences.setBool(kKeyOnboarding, isOnboarding);
  }

  Future clear() async {
    final keys = sharedPreferences.getKeys().toList();
    keys.removeWhere((key) => keyExcludes.contains(key));
    keys.remove(kKeyOnboarding);
    for (final key in keys) {
      await sharedPreferences.remove(key);
    }
  }

  AppUserEntity getAppUser() {
    final appUserJson = sharedPreferences.getString(keyAppUser);
    if (appUserJson != null) {
      return AppUserEntity.fromJson(json.decode(appUserJson));
    } else {
      return const AppUserEntity();
    }
  }

  Future saveAppUser(AppUserEntity userEntity) {
    return sharedPreferences.setString(
      keyAppUser,
      json.encode(
        userEntity.toJson(),
      ),
    );
  }

  CreditDto? getCreditDtoRecently() {
    if (sharedPreferences.containsKey(kKeyCredit)) {
      final creditDto = json.decode(sharedPreferences.getString(kKeyCredit)!);
      return CreditDto.fromJson(creditDto);
    } else {
      return null;
    }
  }

  Future saveCreditDtoRecently(CreditDto? creditDto) {
    if (creditDto == null) {
      return sharedPreferences.remove(kKeyCredit);
    } else {
      return sharedPreferences.setString(
        kKeyCredit,
        json.encode(creditDto.toJson()),
      );
    }
  }

  bool getDidShowFreeRecordLimitDialog() {
    return sharedPreferences.getBool(keyFreeRecordLimitDialogShown) ?? false;
  }

  Future setDidShowFreeRecordLimitDialog() async {
    return await sharedPreferences.setBool(keyFreeRecordLimitDialogShown, true);
  }

  Future<void> setHasShownFreeTrialDialog() async {
    await sharedPreferences.setBool(_hasShownFreeTrialDialogKey, true);
  }

  bool getHasShownFreeTrialDialog() {
    return sharedPreferences.getBool(_hasShownFreeTrialDialogKey) ?? false;
  }

  bool hasShownTooltip() {
    return sharedPreferences.getBool(keyShowTooltip) ?? false;
  }

  Future setTooltipAsShown() async {
    return await sharedPreferences.setBool(keyShowTooltip, true);
  }

  Future markTooltipChatAsShown() async {
    return await sharedPreferences.setBool(keyShowTooltipChat, true);
  }

  bool hasShownTooltipChat() {
    return sharedPreferences.getBool(keyShowTooltipChat) ?? false;
  }

  Future setReferralDto(ReferralDto? referralDto) {
    if (referralDto == null) {
      return sharedPreferences.remove(keyReferral);
    } else {
      return sharedPreferences.setString(
        keyReferral,
        json.encode(referralDto.toJson()),
      );
    }
  }

  ReferralDto? getReferralDto() {
    if (sharedPreferences.containsKey(keyReferral)) {
      final referralDto =
          json.decode(sharedPreferences.getString(keyReferral)!);
      return ReferralDto.fromJson(referralDto);
    } else {
      return null;
    }
  }

  Future setNumberOfReferralDialogShown(int counter) {
    return sharedPreferences.setInt(keyNumberOfReferralDialogShown, counter);
  }

  int getNumberOfReferralDialogShown() {
    return sharedPreferences.getInt(keyNumberOfReferralDialogShown) ?? 0;
  }

  //Save selected language for each use case
  Future<void> saveSelectedLanguage(String languageCode, String useCase) async {
    final prefs = GetIt.instance.get<SharedPreferences>();
    await prefs.setString('keySelectedLanguage$useCase', languageCode);
  }

  String? getSavedLanguageCode(String useCase) {
    final prefs = GetIt.instance.get<SharedPreferences>();
    return prefs.getString('keySelectedLanguage$useCase');
  }

  bool getNoteReadyNotificationEnabled() {
    return sharedPreferences.getBool(keyNoteReadyNotificationEnabled) ?? true;
  }

  Future setNoteReadyNotificationEnabled(bool isEnabled) async {
    return await sharedPreferences.setBool(
        keyNoteReadyNotificationEnabled, isEnabled);
  }

  Future setTooltipTranscriptAsShown() async {
    return await sharedPreferences.setBool(
        keyShouldShouldTranscriptLineTooltip, true);
  }

  bool hasShownTooltipTranscript() {
    return sharedPreferences.getBool(keyShouldShouldTranscriptLineTooltip) ??
        false;
  }

  Future setEmailWithLink(String email) {
    return sharedPreferences.setString(emailWithLink, email);
  }

  String getEmailWithLink() {
    return sharedPreferences.getString(emailWithLink) ?? '';
  }

  Future setOnboardingUserType(String onboardingUserType) {
    return sharedPreferences.setString(
        keyOnboardingUserType, onboardingUserType);
  }

  String getOnboardingUserType() {
    return sharedPreferences.getString(keyOnboardingUserType) ?? '';
  }

  Future setStoreCountryCode(String storeCountryCode) {
    return sharedPreferences.setString(keyStoreCountryCode, storeCountryCode);
  }

  String getStoreCountryCode() {
    return sharedPreferences.getString(keyStoreCountryCode) ?? '';
  }

  Future setHistoryChatAllModel(HistoryChatAllModel historyChatAllModel) {
    return sharedPreferences.setString(
        keyNumberOfLimitMessages, json.encode(historyChatAllModel.toJson()));
  }

  HistoryChatAllModel getHistoryChatAllModel() {
    final historyChatAllModelJson =
        sharedPreferences.getString(keyNumberOfLimitMessages);
    if (historyChatAllModelJson != null) {
      return HistoryChatAllModel.fromJson(json.decode(historyChatAllModelJson));
    }

    final newModel = HistoryChatAllModel(
      historyChatModels: [
        HistoryChatModel(
          userId: GetIt.instance.get<AppCubit>().getAppUser().id,
          numberOfMessageNoteModels: const [],
        ),
      ],
    );
    setHistoryChatAllModel(newModel);
    return newModel;
  }

  Future setLastShownDate(DateTime date) {
    return sharedPreferences.setInt(
        keyLastShownDate, date.millisecondsSinceEpoch);
  }

  DateTime getLastShownDate() {
    return DateTime.fromMillisecondsSinceEpoch(
        sharedPreferences.getInt(keyLastShownDate) ?? 0);
  }

  Future setIsFirstCreateNote(bool isFirstCreateNote) {
    return sharedPreferences.setBool(keyIsFirstCreateNote, isFirstCreateNote);
  }

  bool getIsFirstCreateNote() {
    return sharedPreferences.getBool(keyIsFirstCreateNote) ?? true;
  }

  Future setFlashcardSetInfo(String noteId, String setId, String title) {
    return Future.wait([
      sharedPreferences.setString('${keyFlashcardSetId}_$noteId', setId),
      sharedPreferences.setString('${keyFlashcardSetTitle}_$noteId', title),
    ]);
  }

  Map<String, String> getFlashcardSetInfo(String noteId) {
    return {
      'setId':
          sharedPreferences.getString('${keyFlashcardSetId}_$noteId') ?? '',
      'title':
          sharedPreferences.getString('${keyFlashcardSetTitle}_$noteId') ?? '',
    };
  }

  Future setQuizSetInfo(String noteId, String setId, String title) {
    return Future.wait([
      sharedPreferences.setString('${keyQuizSetId}_$noteId', setId),
      sharedPreferences.setString('${keyQuizSetTitle}_$noteId', title),
    ]);
  }

  Map<String, String> getQuizSetInfo(String noteId) {
    return {
      'setId': sharedPreferences.getString('${keyQuizSetId}_$noteId') ?? '',
      'title': sharedPreferences.getString('${keyQuizSetTitle}_$noteId') ?? '',
    };
  }

  Future<void> saveThemeId(int themeId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(keySelectedThemeId, themeId);
  }

  Future<int> getThemeId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(keySelectedThemeId) ?? 2;
  }

  Future<void> saveSelectedItems(List<TabType> selectedItems) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(
      keySelectedItemsTab,
      TabType.toStringList(selectedItems),
    );
  }

  Future<List<TabType>> loadSelectedItems() async {
    final prefs = await SharedPreferences.getInstance();
    final saved = prefs.getStringList(keySelectedItemsTab);
    if (saved != null && saved.contains(S.current.summary)) {
      return TabType.fromStringList(saved);
    } else {
      return TabType.values.toList();
    }
  }

  Future setShowSwapAnimation(bool isShowSwapAnimation) {
    return sharedPreferences.setBool(
      keyIsShowSwapAnimation,
      isShowSwapAnimation,
    );
  }

  bool isShowSwapAnimation() {
    return sharedPreferences.getBool(keyIsShowSwapAnimation) ?? true;
  }
}
