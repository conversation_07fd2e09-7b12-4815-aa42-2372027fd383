import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:note_x/lib.dart';

enum CreateShortsType {
  audio,
  video,
}

class CreateShortsBottomSheet extends StatelessWidget {
  final NoteModel noteModel;
  final CreateShortsType type;

  const CreateShortsBottomSheet({
    super.key,
    required this.noteModel,
    required this.type,
  });

  static Future<void> show(
    BuildContext context,
    NoteModel noteModel,
    CreateShortsType type,
  ) {
    return showCupertinoModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: true,
      barrierColor: context.colorScheme.mainBackground.withOpacity(0.8),
      backgroundColor: context.colorScheme.mainBackground,
      topRadius: const Radius.circular(12),
      expand: false,
      useRootNavigator: true,
      closeProgressThreshold: 0.6,
      duration: const Duration(milliseconds: 300),
      animationCurve: Curves.easeOut,
      builder: (context) => CreateShortsBottomSheet(
        noteModel: noteModel,
        type: type,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(24.r),
        topRight: Radius.circular(24.r),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
          color: context.colorScheme.mainNeutral,
        ),
        padding: EdgeInsets.only(top: 8.h),
        child: Column(
          children: [
            Expanded(
              child: CreateShortsPage(
                noteModel: noteModel,
                type: type,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
