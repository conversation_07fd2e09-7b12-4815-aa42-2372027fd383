import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class HomeItemNoteListView extends StatefulWidget {
  final List<NoteModel> listNote;
  final Function(String)? onDeleteNote;
  final Function()? onNoteItemTap;
  final bool isFolderDetail;
  final bool isFilter;

  const HomeItemNoteListView({
    super.key,
    required this.listNote,
    this.onDeleteNote,
    this.onNoteItemTap,
    this.isFolderDetail = false,
    this.isFilter = false,
  });

  @override
  State<HomeItemNoteListView> createState() => _HomeItemNoteListViewState();
}

class _HomeItemNoteListViewState extends State<HomeItemNoteListView> {
  final ScrollController _scrollController = ScrollController();
  final appCubit = GetIt.instance.get<AppCubit>();
  final homeCubit = GetIt.instance.get<HomeCubit>();
  final _localService = GetIt.instance.get<LocalService>();

  late List<FolderModel> listFolder;

  @override
  void initState() {
    super.initState();
    listFolder = [
      FolderModel(
        id: MyNoteDetailCubit.allNotesId,
        folderName: S.current.all_note,
        backendId: '',
      ),
      ...HiveFolderService.getAllFolders()
    ];

    // Add scroll listener
    _scrollController.addListener(() {
      if (HomeOverlayManager().hasActiveOverlays &&
          _scrollController.position.userScrollDirection !=
              ScrollDirection.idle) {
        HomeOverlayManager().closeAllOverlays();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.listNote.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        Expanded(child: _buildNoteListView(context)),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    if (widget.isFolderDetail) return const SizedBox.shrink();
    if (widget.isFilter) return _EmptyFilterNote();
    return const EmptyNotePortrait();
  }

  Widget _buildNoteListView(BuildContext context) {
    // Determine if we need to show the purchase banner
    final bool showPurchaseBanner =
        appCubit.isUserFree() && !widget.isFolderDetail;

    // Calculate the number of special items (banner) to show
    final int specialItemsCount = showPurchaseBanner ? 1 : 0;

    // Calculate the total item count
    final int totalItemCount = widget.listNote.length + specialItemsCount;

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).padding.bottom,
        left: context.isTablet ? 24 : 0,
        right: context.isTablet ? 24 : 0,
      ),
      itemCount: totalItemCount,
      itemBuilder: (context, index) {
        // Handle purchase banner at index 0
        if (index == 0 && showPurchaseBanner) {
          return const PurchaseBannerSliderWidget();
        }

        // Calculate the actual note index in the list
        final int noteIndex = index - specialItemsCount;

        // We no longer need to get the folder name here

        // Build and return the note item
        return _buildNoteItem(context, widget.listNote[noteIndex], noteIndex);
      },
    );
  }

  Widget _buildNoteItem(BuildContext context, NoteModel note, int index) {
    // Get the item's position in the list view
    final listViewOffset = _scrollController.offset;

    // Calculate item height and position
    final double itemHeight = context.isTablet ? 80.0 : 72.0;
    final double itemPadding = context.isTablet ? 16.0 : 12.0;
    final double totalItemHeight = itemHeight + itemPadding;

    // Calculate the position of this item in the viewport
    final double itemPosition = index * totalItemHeight;
    final double itemPositionInViewport = itemPosition - listViewOffset;

    // Calculate the visible viewport height (excluding app bar, status bar, tab bar, bottom nav)
    final double viewportHeight = MediaQuery.of(context).size.height -
        kToolbarHeight - // App bar height
        MediaQuery.of(context).padding.top - // Status bar
        (context.isTablet ? 80.0 : 60.0) - // Tab bar height
        (context.isTablet ? 75.0 : 72.0) - // Bottom navigation height
        MediaQuery.of(context).padding.bottom; // Bottom safe area

    // Determine if the item is in the bottom half of the visible area
    final bool isInBottomHalf = itemPositionInViewport > (viewportHeight / 2);

    return FadeAnimation(
      (1.0 + index) / 4,
      Padding(
        padding: context.isTablet
            ? const EdgeInsets.symmetric(horizontal: 10, vertical: 8)
            : EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        child: HomeItemNoteWidget(
          noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
          onTap: () {
            widget.onNoteItemTap?.call();
            _navigateToNoteDetail(context, note);
          },
          overflowMenu: !widget.isFolderDetail
              ? HomeOverFlowMenu(
                  note: note,
                  isCommunityNote: false,
                  onShowFolder: (context) {
                    MoveToFolderHelper.showBottomSheet(context, note);
                  },
                  onShowBottomSheet: (exportType) {
                    ExportNoteHelper.showBottomSheet(context, exportType, note);
                  },
                  focusNode: FocusNode(),
                  onShowSharingView: Func0(() {
                    NoteSharingHelper.showBottomSheet(context, note);
                  }),
                  onDeleteNote: () {
                    widget.onDeleteNote?.call(note.id);
                  },
                  isInBottomHalf: isInBottomHalf,
                  // Initial value
                  calculateIsInBottomHalf: () {
                    // Real-time calculation when menu is shown
                    final currentListViewOffset = _scrollController.offset;
                    final currentItemPosition = index * totalItemHeight;
                    final currentPositionInViewport =
                        currentItemPosition - currentListViewOffset;

                    // Get item's bottom position in viewport
                    final itemBottomPosition =
                        currentPositionInViewport + totalItemHeight;

                    // Show menu above when item's bottom is in the bottom 25% of screen
                    return itemBottomPosition > (viewportHeight * 0.7);
                  })
              : const SizedBox.shrink(),
        ),
      ),
    );
  }

  void _navigateToNoteDetail(BuildContext context, NoteModel note) async {
    // Get saved tabs before navigation
    final savedTabs = await _initTabBar();

    if (!mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MyNoteDetailPage(
          isCommunityNote: false,
          // Regular notes are not community notes
          noteModel: note.backendNoteId.isNotEmpty ? note : note.copyWith(),
          isTablet: context.isTablet,
          from: widget.isFolderDetail
              ? NoteDetailPageFrom.folderDetailScreen
              : NoteDetailPageFrom.homeScreen,
          savedTabs: savedTabs,
        ),
      ),
    );
  }

  Future<List<TabType>> _initTabBar() async {
    return await _localService.loadSelectedItems();
  }
}

class _EmptyFilterNote extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                Assets.icons.icEmptyNoteNotexEmpty,
                width: 140,
                height: 140,
              ),
              AppConstants.kSpacingItem12,
              CommonText(
                S.current.no_notes_found,
                style: TextStyle(
                  fontSize: context.isTablet ? 18 : 16.sp,
                  fontWeight: FontWeight.w400,
                  color: context.colorScheme.mainGray,
                  fontFamily: AppConstants.fontSFPro,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
