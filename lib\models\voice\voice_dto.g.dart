// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceDtoImpl _$$VoiceDtoImplFromJson(Map<String, dynamic> json) =>
    _$VoiceDtoImpl(
      voiceId: json['voice_id'] as String? ?? '',
      voiceName: json['voice_name'] as String? ?? '',
      voiceGender: json['voice_gender'] as String? ?? '',
      previewUrl: json['preview_url'] as String? ?? '',
      isCaptionSupport: json['is_caption_supported'] as bool? ?? true,
      voiceLanguage: json['voice_language'] as String? ?? '',
      displayLanguage: json['display_language'] as String? ?? '',
    );

Map<String, dynamic> _$$VoiceDtoImplToJson(_$VoiceDtoImpl instance) =>
    <String, dynamic>{
      'voice_id': instance.voiceId,
      'voice_name': instance.voiceName,
      'voice_gender': instance.voiceGender,
      'preview_url': instance.previewUrl,
      'is_caption_supported': instance.isCaptionSupport,
      'voice_language': instance.voiceLanguage,
      'display_language': instance.displayLanguage,
    };
