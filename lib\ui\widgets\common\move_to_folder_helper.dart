import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

/// A common widget for moving notes to folders
///
/// This widget displays a modal bottom sheet that allows users to select
/// a folder to move a note to.
class MoveToFolderHelper {
  /// Shows a modal bottom sheet for selecting a folder
  ///
  /// [context] - The build context
  /// [noteModel] - The note to be moved
  /// [onFolderSelected] - Optional callback when a folder is selected
  static void showBottomSheet(
    BuildContext context,
    NoteModel noteModel, {
    Function(FolderModel folder)? onFolderSelected,
  }) {
    // Get all folders
    final List<FolderModel> listFolder = [
      FolderModel(
        id: MyNoteDetailCubit.allNotesId,
        folderName: S.current.all_note,
        backendId: '',
      ),
      ...HiveFolderService.getAllFolders()
    ];

    // Create a ValueNotifier for the chosen folder name
    final ValueNotifier<String> chooseFolderName = ValueNotifier(
      HiveFolderService.getNameFolderByBackEndId(
        noteModel.folderId,
      ),
    );

    // Calculate maximum height for the bottom sheet
    final double maxHeight = MediaQuery.of(context).size.height -
        MediaQuery.of(context).padding.top -
        kToolbarHeight;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: maxHeight,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: context.colorScheme.mainNeutral,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(20.r),
                  ),
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 24.h,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonText(
                          S.current.add_to,
                          style: TextStyle(
                            fontSize: context.isTablet ? 22 : 20.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: SvgPicture.asset(
                            Assets.icons.icCloseWhite,
                            width: 24.w,
                            height: 24.h,
                            colorFilter: ColorFilter.mode(
                              context.colorScheme.mainGray,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                    AppConstants.kSpacingItem12,
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: listFolder.length,
                      itemBuilder: (context, index) {
                        final folder = listFolder[index];
                        return FolderItemWidget(
                          folder: folder,
                          noteId: noteModel.id,
                          allNoteId: '',
                          onTap: () async {
                            final note =
                                HiveService().noteBox.get(noteModel.id)!;
                            CommonDialogs.showLoadingDialog();
                            // Call the folder service to add the note to the folder
                            await FolderService().addNoteToFolder(
                              folder,
                              note,
                              chooseFolderName,
                            );
                            CommonDialogs.closeLoading();
                            // Call the optional callback if provided
                            if (onFolderSelected != null) {
                              onFolderSelected(folder);
                            }

                            // ignore: use_build_context_synchronously
                            Navigator.of(context).pop();
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
