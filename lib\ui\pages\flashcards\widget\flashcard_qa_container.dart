import 'package:flip_card/flip_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/base/base.dart';

import '../../../../generated/l10n.dart';
import '../../../widgets/common/common_text.dart';

class FlashCardQAContainer extends StatelessWidget {
  final String question;
  final String answer;
  final int currentIndex;
  final int totalIndex;
  final double? width;
  final double? height;
  final List<TranscriptModel> transcriptJson;
  final String? audioFilePath;
  final String? audioUrl;
  final bool isCommunityNote;
  final ValueNotifier<bool> isQuestionSide;

  const FlashCardQAContainer({
    Key? key,
    required this.question,
    required this.answer,
    required this.currentIndex,
    required this.totalIndex,
    this.width,
    this.height,
    required this.transcriptJson,
    this.audioFilePath,
    this.audioUrl,
    required this.isCommunityNote,
    required this.isQuestionSide,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FlipCard(
      direction: FlipDirection.HORIZONTAL,
      onFlip: () {
        isQuestionSide.value = !isQuestionSide.value;
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.note_flashcard_flip,
        );
      },
      front: _buildSide(context, isQuestion: true),
      back: _buildSide(context, isQuestion: false),
    );
  }

  Widget _buildSide(BuildContext context, {required bool isQuestion}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.0.w),
      decoration: _buildDecoration(context, isQuestion),
      child: Column(
        children: [
          if (isQuestion) ...[
            AppConstants.kSpacingItem24,
            _buildHeader(context),
            AppConstants.kSpacingItem16,
          ],
          Expanded(
            child: _buildContent(context, isQuestion),
          ),
          AppConstants.kSpacingItem16,
          _buildFooter(context, isQuestion),
          AppConstants.kSpacingItem16,
        ],
      ),
    );
  }

  BoxDecoration _buildDecoration(BuildContext context, bool isQuestion) {
    return isQuestion
        ? BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.center,
              colors: context.isDarkMode
                  ? AppColors.quizFlashcardGradientList
                  : AppColors.quizFlashcardGradientListLighMode,
            ),
            borderRadius: BorderRadius.circular(20.0.r),
          )
        : BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.center,
              colors: AppColors.gradient18,
            ),
            borderRadius: BorderRadius.circular(20.0.r),
          );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CommonText(
          S.current.question,
          style: TextStyle(
            fontSize: context.isTablet ? 18 : 16.sp,
            fontWeight: FontWeight.w700,
            color: context.colorScheme.mainPrimary,
          ),
        ),
        CommonText(
          " ${currentIndex + 1} ${S.current.of_index} $totalIndex",
          style: TextStyle(
            fontSize: context.isTablet ? 18 : 16.sp,
            fontWeight: FontWeight.w700,
            color: context.colorScheme.mainPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, bool isQuestion) {
    return isQuestion ? _buildQuestionText(context) : _buildAnswerText(context);
  }

  Widget _buildQuestionText(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: CommonText(
          question,
          style: TextStyle(
            fontSize: context.isTablet ? 18 : 16.sp,
            color: context.colorScheme.mainPrimary,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildAnswerText(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: Directionality(
          textDirection: RtlUtils.getTextDirectionForContent(answer),
          child: CommonText(
            answer,
            style: TextStyle(
              fontSize: context.isTablet ? 18 : 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(BuildContext context, bool isQuestion) {
    return CommonText(
      isQuestion ? S.current.click_to_flip : '',
      style: TextStyle(
        fontSize: context.isTablet ? 14 : 12.sp,
        color: context.colorScheme.mainPrimary.withOpacity(0.6),
        fontWeight: FontWeight.w400,
      ),
      textAlign: TextAlign.center,
    );
  }
}
