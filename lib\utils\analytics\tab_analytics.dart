import 'package:note_x/lib.dart';

class TabAnalytics {
  static void trackTabSelection(TabType selectedTab) {
    // Log click event
    switch (selectedTab) {
      case TabType.summary:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_summary,
        );
        break;
      case TabType.transcript:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_transcript,
        );
        break;
      case TabType.slideShow:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_slide_show,
        );
        break;
      case TabType.mindMap:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_mind_map,
        );
        break;
      case TabType.shorts:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_shorts,
        );
        break;
      case TabType.documentTab:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_document,
        );
        break;
      case TabType.flashcard:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_flashcard,
        );
        break;
      case TabType.quizzes:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_quizzes,
        );
        break;
      case TabType.podcast:
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.tab_podcast,
        );
        break;
    }

    // Log screen view
    switch (selectedTab) {
      case TabType.summary:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_summary_scr,
          screenName: EventScreenName.tab_summary_scr,
        );
        break;
      case TabType.transcript:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_transcript_scr,
          screenName: EventScreenName.tab_transcript_scr,
        );
        break;
      case TabType.slideShow:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_slide_show_scr,
          screenName: EventScreenName.tab_slide_show_scr,
        );
        break;
      case TabType.mindMap:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_mind_map_scr,
          screenName: EventScreenName.tab_mind_map_scr,
        );
        break;
      case TabType.shorts:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_shorts_scr,
          screenName: EventScreenName.tab_shorts_scr,
        );
        break;
      case TabType.documentTab:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_document_scr,
          screenName: EventScreenName.tab_document_scr,
        );
        break;
      case TabType.flashcard:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_flashcard_scr,
          screenName: EventScreenName.tab_flashcard_scr,
        );
        break;
      case TabType.quizzes:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_quizzes_scr,
          screenName: EventScreenName.tab_quizzes_scr,
        );
        break;
      case TabType.podcast:
        AnalyticsService.logEventScreenView(
          screenClass: EventScreenClass.tab_podcast_scr,
          screenName: EventScreenName.tab_podcast_scr,
        );
        break;
    }
  }
} 