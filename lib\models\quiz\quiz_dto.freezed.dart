// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

QuizDto _$QuizDtoFromJson(Map<String, dynamic> json) {
  return _QuizDto.fromJson(json);
}

/// @nodoc
mixin _$QuizDto {
  List<QuizDetailDto> get quizzes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizDtoCopyWith<QuizDto> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizDtoCopyWith<$Res> {
  factory $QuizDtoCopyWith(QuizDto value, $Res Function(QuizDto) then) =
      _$QuizDtoCopyWithImpl<$Res, QuizDto>;
  @useResult
  $Res call({List<QuizDetailDto> quizzes});
}

/// @nodoc
class _$QuizDtoCopyWithImpl<$Res, $Val extends QuizDto>
    implements $QuizDtoCopyWith<$Res> {
  _$QuizDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quizzes = null,
  }) {
    return _then(_value.copyWith(
      quizzes: null == quizzes
          ? _value.quizzes
          : quizzes // ignore: cast_nullable_to_non_nullable
              as List<QuizDetailDto>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizDtoImplCopyWith<$Res> implements $QuizDtoCopyWith<$Res> {
  factory _$$QuizDtoImplCopyWith(
          _$QuizDtoImpl value, $Res Function(_$QuizDtoImpl) then) =
      __$$QuizDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<QuizDetailDto> quizzes});
}

/// @nodoc
class __$$QuizDtoImplCopyWithImpl<$Res>
    extends _$QuizDtoCopyWithImpl<$Res, _$QuizDtoImpl>
    implements _$$QuizDtoImplCopyWith<$Res> {
  __$$QuizDtoImplCopyWithImpl(
      _$QuizDtoImpl _value, $Res Function(_$QuizDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quizzes = null,
  }) {
    return _then(_$QuizDtoImpl(
      quizzes: null == quizzes
          ? _value._quizzes
          : quizzes // ignore: cast_nullable_to_non_nullable
              as List<QuizDetailDto>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizDtoImpl implements _QuizDto {
  const _$QuizDtoImpl({final List<QuizDetailDto> quizzes = const []})
      : _quizzes = quizzes;

  factory _$QuizDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizDtoImplFromJson(json);

  final List<QuizDetailDto> _quizzes;
  @override
  @JsonKey()
  List<QuizDetailDto> get quizzes {
    if (_quizzes is EqualUnmodifiableListView) return _quizzes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_quizzes);
  }

  @override
  String toString() {
    return 'QuizDto(quizzes: $quizzes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizDtoImpl &&
            const DeepCollectionEquality().equals(other._quizzes, _quizzes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_quizzes));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizDtoImplCopyWith<_$QuizDtoImpl> get copyWith =>
      __$$QuizDtoImplCopyWithImpl<_$QuizDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizDtoImplToJson(
      this,
    );
  }
}

abstract class _QuizDto implements QuizDto {
  const factory _QuizDto({final List<QuizDetailDto> quizzes}) = _$QuizDtoImpl;

  factory _QuizDto.fromJson(Map<String, dynamic> json) = _$QuizDtoImpl.fromJson;

  @override
  List<QuizDetailDto> get quizzes;
  @override
  @JsonKey(ignore: true)
  _$$QuizDtoImplCopyWith<_$QuizDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
