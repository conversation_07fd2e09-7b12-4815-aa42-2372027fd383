// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_user_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppUserEntityImpl _$$AppUserEntityImplFromJson(Map<String, dynamic> json) =>
    _$AppUserEntityImpl(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      youtubeCredit: (json['youtubeCredit'] as num?)?.toInt() ?? 0,
      webCredit: (json['webCredit'] as num?)?.toInt() ?? 0,
      accessToken: json['accessToken'] as String? ?? '',
      refreshToken: json['refreshToken'] as String? ?? '',
      audioCredit: (json['audioCredit'] as num?)?.toInt() ?? 0,
      rewardCredits: (json['rewardCredits'] as num?)?.toInt() ?? 0,
      shortsCredit: (json['shortsCredit'] as num?)?.toInt() ?? 0,
      documentCredit: (json['documentCredit'] as num?)?.toInt() ?? 0,
      slideCredit: (json['slideCredit'] as num?)?.toInt() ?? 0,
      userType: $enumDecodeNullable(_$UserTypeEnumMap, json['userType']) ??
          UserType.free,
    );

Map<String, dynamic> _$$AppUserEntityImplToJson(_$AppUserEntityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'youtubeCredit': instance.youtubeCredit,
      'webCredit': instance.webCredit,
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'audioCredit': instance.audioCredit,
      'rewardCredits': instance.rewardCredits,
      'shortsCredit': instance.shortsCredit,
      'documentCredit': instance.documentCredit,
      'slideCredit': instance.slideCredit,
      'userType': _$UserTypeEnumMap[instance.userType]!,
    };

const _$UserTypeEnumMap = {
  UserType.free: 'free',
  UserType.pro: 'pro',
  UserType.proLite: 'proLite',
};
