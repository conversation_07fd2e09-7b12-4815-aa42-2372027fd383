// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'folder_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FolderDtoImpl _$$FolderDtoImplFromJson(Map<String, dynamic> json) =>
    _$FolderDtoImpl(
      id: json['id'] as String? ?? '',
      folderName: json['folder_name'] as String? ?? '',
      user: json['user'] as String? ?? '',
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
    );

Map<String, dynamic> _$$FolderDtoImplToJson(_$FolderDtoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'folder_name': instance.folderName,
      'user': instance.user,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
