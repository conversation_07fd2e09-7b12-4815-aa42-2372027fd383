<svg width="105" height="97" viewBox="0 0 105 97" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_1445_6180)">
<ellipse cx="51.3585" cy="84.8625" rx="29.109" ry="4.65" fill="url(#paint0_linear_1445_6180)" fill-opacity="0.1"/>
</g>
<path d="M51.5182 38.1039C52.1364 34.6623 55.4242 32.3916 58.8616 33.032L81.3051 37.2132C84.7426 37.8535 87.028 41.1626 86.4098 44.6041L81.0081 74.6741C80.589 77.0069 78.9036 78.9019 76.6404 79.5848L64.3785 83.2846C62.4235 83.8745 60.2989 83.4787 58.6756 82.2222L48.4941 74.3412C46.6149 72.8866 45.6974 70.5067 46.1165 68.1738L51.5182 38.1039Z" fill="url(#paint1_linear_1445_6180)"/>
<path d="M67.2008 73.4673C66.8408 75.4714 64.9591 76.7827 63.0145 76.4204C61.07 76.0581 59.766 74.1534 60.126 72.1493C60.486 70.1452 62.3677 68.834 64.3123 69.1962C66.2568 69.5585 67.5608 71.4633 67.2008 73.4673Z" fill="#D9D9D9" stroke="url(#paint2_linear_1445_6180)" stroke-width="1.15459"/>
<g filter="url(#filter1_d_1445_6180)">
<path d="M8.53571 33.7476C7.72188 30.4958 9.69243 27.1769 12.9371 26.3348L51.5841 16.3041C54.8288 15.462 58.1188 17.4154 58.9327 20.6673L69.8396 64.2484C70.4094 66.5252 69.6184 68.9379 67.8083 70.4443L50.7767 84.6177C49.3754 85.7839 47.5322 86.2623 45.7537 85.9214L24.1382 81.7787C21.8409 81.3384 20.0125 79.6056 19.4426 77.3288L8.53571 33.7476Z" fill="url(#paint3_linear_1445_6180)"/>
</g>
<path d="M48.6697 72.7681C49.2765 75.1927 47.8368 77.6433 45.4775 78.2556C43.1182 78.868 40.7024 77.4181 40.0956 74.9935C39.4888 72.5688 40.9285 70.1183 43.2878 69.5059C45.6471 68.8936 48.0629 70.3434 48.6697 72.7681Z" fill="url(#paint4_linear_1445_6180)" stroke="url(#paint5_linear_1445_6180)" stroke-width="1.15459"/>
<g filter="url(#filter2_f_1445_6180)">
<ellipse cx="3.57985" cy="3.63753" rx="3.57985" ry="3.63753" transform="matrix(0.967929 -0.251223 0.24278 0.970082 0 55.6362)" fill="#DEDEDE"/>
</g>
<g filter="url(#filter3_i_1445_6180)">
<path d="M32.7867 30.6527C33.2098 29.1419 35.103 28.6506 36.1847 29.7708L39.4281 33.13C39.8229 33.5389 40.3726 33.7601 40.9417 33.739L45.6156 33.5662C47.1523 33.5093 48.1733 35.1288 47.4695 36.5066L45.2875 40.7781C45.0311 41.2801 44.9931 41.8649 45.1825 42.3928L46.7949 46.8847C47.3196 48.3465 46.0705 49.8439 44.543 49.5841L39.9925 48.81C39.4261 48.7136 38.8439 48.8648 38.3919 49.2254L34.7603 52.1231C33.5413 53.0958 31.7407 52.39 31.5144 50.8507L30.8189 46.1208C30.7371 45.565 30.4288 45.0701 29.9677 44.7543L26.0444 42.0674C24.7788 41.2007 24.9142 39.2812 26.2901 38.582L30.475 36.4556C30.9846 36.1967 31.3633 35.7346 31.5178 35.1831L32.7867 30.6527Z" fill="#FFC030"/>
</g>
<g filter="url(#filter4_f_1445_6180)">
<path d="M95.3805 58.3564C95.5704 57.6785 96.4198 57.458 96.9051 57.9607L98.3603 59.4679C98.5375 59.6513 98.7841 59.7505 99.0394 59.7411L101.136 59.6636C101.826 59.6381 102.284 60.3646 101.968 60.9828L100.989 62.8993C100.874 63.1246 100.857 63.387 100.942 63.6238L101.666 65.6392C101.901 66.2951 101.341 66.9669 100.655 66.8504L98.6136 66.5031C98.3594 66.4598 98.0982 66.5276 97.8954 66.6894L96.266 67.9896C95.7191 68.426 94.9112 68.1093 94.8096 67.4187L94.4976 65.2965C94.4609 65.0471 94.3226 64.825 94.1157 64.6834L92.3554 63.4778C91.7876 63.089 91.8483 62.2277 92.4656 61.914L94.3433 60.96C94.572 60.8438 94.7419 60.6365 94.8112 60.389L95.3805 58.3564Z" fill="#FDB100"/>
</g>
<g filter="url(#filter5_f_1445_6180)">
<path d="M36.1367 1.51296C36.2291 1.18327 36.6422 1.07604 36.8782 1.3205L37.586 2.05356C37.6722 2.14279 37.7921 2.19106 37.9163 2.18647L38.9363 2.14875C39.2716 2.13635 39.4944 2.48974 39.3408 2.79041L38.8647 3.72255C38.8087 3.83211 38.8004 3.95974 38.8418 4.07493L39.1936 5.05516C39.3081 5.37417 39.0355 5.70094 38.7022 5.64423L37.7092 5.47531C37.5856 5.45429 37.4585 5.48726 37.3599 5.56597L36.5674 6.19831C36.3014 6.41058 35.9085 6.25655 35.8591 5.92064L35.7073 4.88848C35.6894 4.76718 35.6222 4.65917 35.5215 4.59026L34.6654 4.00392C34.3892 3.81478 34.4187 3.39589 34.719 3.24333L35.6322 2.77929C35.7435 2.72279 35.8261 2.62194 35.8598 2.5016L36.1367 1.51296Z" fill="#FDB100"/>
</g>
<g filter="url(#filter6_f_1445_6180)">
<path d="M71.9757 8.49927L72.0259 19.1556M77.2329 13.8019L66.7687 13.853" stroke="#DEDEDE" stroke-width="1.74491" stroke-linecap="round"/>
</g>
<g filter="url(#filter7_f_1445_6180)">
<path d="M16.1448 84.9934L16.1797 92.4141M19.8057 88.686L12.5188 88.7215" stroke="#DEDEDE" stroke-width="1.21508" stroke-linecap="round"/>
</g>
<g filter="url(#filter8_f_1445_6180)">
<path d="M79.7935 21.2483L79.8184 26.5544M82.4111 23.8886L77.2008 23.914" stroke="#DEDEDE" stroke-width="0.868832" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_f_1445_6180" x="18.6703" y="76.6333" width="65.3765" height="16.4585" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.78962" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<filter id="filter1_d_1445_6180" x="2.00203" y="12.0673" width="76.6793" height="84.9303" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.15459" dy="3.46378"/>
<feGaussianBlur stdDeviation="3.75243"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.336094 0 0 0 0 0.336094 0 0 0 0 0.3375 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1445_6180"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1445_6180" result="shape"/>
</filter>
<filter id="filter2_f_1445_6180" x="0.309647" y="54.1613" width="8.077" height="8.20847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.230919" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<filter id="filter3_i_1445_6180" x="25.1715" y="29.1592" width="22.5249" height="28.0283" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.61838"/>
<feGaussianBlur stdDeviation="2.30919"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.867213 0 0 0 0 0.6625 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1445_6180"/>
</filter>
<filter id="filter4_f_1445_6180" x="91.5019" y="57.2244" width="11.03" height="11.4271" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.230919" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<filter id="filter5_f_1445_6180" x="34.013" y="0.725174" width="5.8392" height="6.03232" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.230919" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<filter id="filter6_f_1445_6180" x="65.4425" y="7.15706" width="13.1168" height="13.3408" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.230919" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<filter id="filter7_f_1445_6180" x="11.455" y="83.9185" width="9.4144" height="9.5704" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.230919" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<filter id="filter8_f_1445_6180" x="76.3084" y="20.3481" width="6.99496" height="7.10641" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.230919" result="effect1_foregroundBlur_1445_6180"/>
</filter>
<linearGradient id="paint0_linear_1445_6180" x1="53.6416" y1="56.9625" x2="52.4578" y2="89.511" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D9D9"/>
<stop offset="1" stop-color="#737373"/>
</linearGradient>
<linearGradient id="paint1_linear_1445_6180" x1="58.0634" y1="109.706" x2="47.5134" y2="41.4927" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C9E7FF"/>
</linearGradient>
<linearGradient id="paint2_linear_1445_6180" x1="64.4143" y1="68.628" x2="62.8586" y2="76.9786" gradientUnits="userSpaceOnUse">
<stop stop-color="#16ACFF"/>
<stop offset="1" stop-color="#016EBE"/>
</linearGradient>
<linearGradient id="paint3_linear_1445_6180" x1="58.8668" y1="119.435" x2="8.98777" y2="40.5944" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C9E7FF"/>
</linearGradient>
<linearGradient id="paint4_linear_1445_6180" x1="47.2574" y1="83.7915" x2="40.3358" y2="71.1641" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C9E7FF"/>
</linearGradient>
<linearGradient id="paint5_linear_1445_6180" x1="43.1476" y1="68.9459" x2="45.7035" y2="78.7934" gradientUnits="userSpaceOnUse">
<stop stop-color="#16ACFF"/>
<stop offset="1" stop-color="#016EBE"/>
</linearGradient>
</defs>
</svg>
