import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:note_x/lib.dart';
import 'package:share_plus/share_plus.dart';

/// A base sharing service that provides common sharing functionality
/// for different types of content across the app.
class ShareService {
  /// Singleton instance
  static final ShareService _instance = ShareService._internal();

  /// Factory constructor to return the singleton instance
  factory ShareService() => _instance;

  /// Private constructor
  ShareService._internal();

  /// Share a file that already exists locally
  ///
  /// [filePath] - Path to the local file
  /// [subject] - Optional subject for the share
  /// [text] - Optional text to share along with the file
  Future<void> shareFile({
    required String filePath,
    String? subject,
    Rect? sharePositionOrigin,
    String? text,
    String? eventName,
    void Function()? onSuccess,
  }) async {
    try {
      final result = await SharePlus.instance.share(
        ShareParams(
          text: text,
          files: [XFile(filePath)],
          subject: subject,
          sharePositionOrigin: sharePositionOrigin,
        ),
      );

      _handleShareResult(
        status: result.status,
        eventName: eventName,
        onSuccess: onSuccess,
      );
    } catch (e) {
      debugPrint('Error sharing file: $e');
    }
  }

  Future<void> shareLink({
    required String link,
    String? subject,
    required Rect sharePositionOrigin,
    String? eventName,
    void Function()? onSuccess,
  }) async {
    try {
      final result = await SharePlus.instance.share(
        ShareParams(
          uri: Uri.parse(link),
          subject: subject,
          sharePositionOrigin: sharePositionOrigin,
        ),
      );
      _handleShareResult(
        status: result.status,
        eventName: eventName,
        onSuccess: onSuccess,
      );
    } catch (e) {
      debugPrint('Error sharing file: $e');
    }
  }

  Future<void> shareText({
    required String text,
    String? subject,
    required Rect sharePositionOrigin,
    String? eventName,
    void Function()? onSuccess,
  }) async {
    try {
      final result = await SharePlus.instance.share(
        ShareParams(
          text: text,
          subject: subject,
          sharePositionOrigin: sharePositionOrigin,
        ),
      );
      _handleShareResult(
        status: result.status,
        eventName: eventName,
        onSuccess: onSuccess,
      );
    } catch (e) {
      debugPrint('Error sharing file: $e');
    }
  }

  void _handleShareResult({
    required ShareResultStatus status,
    String? eventName,
    void Function()? onSuccess,
  }) {
    switch (status) {
      case ShareResultStatus.success:
        if (eventName != null) {
          AnalyticsService.logAnalyticsEventNoParam(
            eventName: eventName,
          );
        }
        CommonDialogs.showToast(
          S.current.successfully,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        onSuccess?.call();
      case ShareResultStatus.dismissed:
        debugPrint('Share dismissed');
      case ShareResultStatus.unavailable:
        CommonDialogs.showToast(
          S.current.fail,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
    }
  }
}
