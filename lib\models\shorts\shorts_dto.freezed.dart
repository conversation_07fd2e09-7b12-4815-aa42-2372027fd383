// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shorts_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ShortsDto _$ShortsDtoFromJson(Map<String, dynamic> json) {
  return _ShortsDto.fromJson(json);
}

/// @nodoc
mixin _$ShortsDto {
  @JsonKey(name: 'audio_url')
  String get audioUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'subtitles')
  String get subtitleRawData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShortsDtoCopyWith<ShortsDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShortsDtoCopyWith<$Res> {
  factory $ShortsDtoCopyWith(ShortsDto value, $Res Function(ShortsDto) then) =
      _$ShortsDtoCopyWithImpl<$Res, ShortsDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'subtitles') String subtitleRawData});
}

/// @nodoc
class _$ShortsDtoCopyWithImpl<$Res, $Val extends ShortsDto>
    implements $ShortsDtoCopyWith<$Res> {
  _$ShortsDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioUrl = null,
    Object? subtitleRawData = null,
  }) {
    return _then(_value.copyWith(
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subtitleRawData: null == subtitleRawData
          ? _value.subtitleRawData
          : subtitleRawData // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShortsDtoImplCopyWith<$Res>
    implements $ShortsDtoCopyWith<$Res> {
  factory _$$ShortsDtoImplCopyWith(
          _$ShortsDtoImpl value, $Res Function(_$ShortsDtoImpl) then) =
      __$$ShortsDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'subtitles') String subtitleRawData});
}

/// @nodoc
class __$$ShortsDtoImplCopyWithImpl<$Res>
    extends _$ShortsDtoCopyWithImpl<$Res, _$ShortsDtoImpl>
    implements _$$ShortsDtoImplCopyWith<$Res> {
  __$$ShortsDtoImplCopyWithImpl(
      _$ShortsDtoImpl _value, $Res Function(_$ShortsDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioUrl = null,
    Object? subtitleRawData = null,
  }) {
    return _then(_$ShortsDtoImpl(
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subtitleRawData: null == subtitleRawData
          ? _value.subtitleRawData
          : subtitleRawData // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShortsDtoImpl implements _ShortsDto {
  const _$ShortsDtoImpl(
      {@JsonKey(name: 'audio_url') this.audioUrl = '',
      @JsonKey(name: 'subtitles') this.subtitleRawData = ''});

  factory _$ShortsDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShortsDtoImplFromJson(json);

  @override
  @JsonKey(name: 'audio_url')
  final String audioUrl;
  @override
  @JsonKey(name: 'subtitles')
  final String subtitleRawData;

  @override
  String toString() {
    return 'ShortsDto(audioUrl: $audioUrl, subtitleRawData: $subtitleRawData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortsDtoImpl &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.subtitleRawData, subtitleRawData) ||
                other.subtitleRawData == subtitleRawData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, audioUrl, subtitleRawData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortsDtoImplCopyWith<_$ShortsDtoImpl> get copyWith =>
      __$$ShortsDtoImplCopyWithImpl<_$ShortsDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShortsDtoImplToJson(
      this,
    );
  }
}

abstract class _ShortsDto implements ShortsDto {
  const factory _ShortsDto(
          {@JsonKey(name: 'audio_url') final String audioUrl,
          @JsonKey(name: 'subtitles') final String subtitleRawData}) =
      _$ShortsDtoImpl;

  factory _ShortsDto.fromJson(Map<String, dynamic> json) =
      _$ShortsDtoImpl.fromJson;

  @override
  @JsonKey(name: 'audio_url')
  String get audioUrl;
  @override
  @JsonKey(name: 'subtitles')
  String get subtitleRawData;
  @override
  @JsonKey(ignore: true)
  _$$ShortsDtoImplCopyWith<_$ShortsDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
