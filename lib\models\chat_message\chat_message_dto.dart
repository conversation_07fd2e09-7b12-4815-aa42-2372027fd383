// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_message_dto.freezed.dart';

part 'chat_message_dto.g.dart';

@freezed
class ChatMessageDto with _$ChatMessageDto {
  const factory ChatMessageDto({
    @Json<PERSON>ey(name: 'id') @Default('') String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'note_id') @Default('') String noteId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id') @Default('') String userId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') @Default('') String title,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'answer') @Default('') String answer,
    @J<PERSON><PERSON><PERSON>(name: 'created_at') @Default('') String createdAt,
    @Json<PERSON>ey(name: 'updated_at') @Default('') String updatedAt,
  }) = _ChatMessageDto;

  factory ChatMessageDto.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageDtoFromJson(json);
}
