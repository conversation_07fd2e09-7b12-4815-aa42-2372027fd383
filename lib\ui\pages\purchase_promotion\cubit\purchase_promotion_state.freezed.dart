// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_promotion_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PurchasePromotionState {
  Package? get quarterPackage => throw _privateConstructorUsedError;
  QuarterlyOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PurchasePromotionStateCopyWith<PurchasePromotionState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchasePromotionStateCopyWith<$Res> {
  factory $PurchasePromotionStateCopyWith(PurchasePromotionState value,
          $Res Function(PurchasePromotionState) then) =
      _$PurchasePromotionStateCopyWithImpl<$Res, PurchasePromotionState>;
  @useResult
  $Res call({Package? quarterPackage, QuarterlyOneShotEvent oneShotEvent});

  $PackageCopyWith<$Res>? get quarterPackage;
}

/// @nodoc
class _$PurchasePromotionStateCopyWithImpl<$Res,
        $Val extends PurchasePromotionState>
    implements $PurchasePromotionStateCopyWith<$Res> {
  _$PurchasePromotionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quarterPackage = freezed,
    Object? oneShotEvent = null,
  }) {
    return _then(_value.copyWith(
      quarterPackage: freezed == quarterPackage
          ? _value.quarterPackage
          : quarterPackage // ignore: cast_nullable_to_non_nullable
              as Package?,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as QuarterlyOneShotEvent,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PackageCopyWith<$Res>? get quarterPackage {
    if (_value.quarterPackage == null) {
      return null;
    }

    return $PackageCopyWith<$Res>(_value.quarterPackage!, (value) {
      return _then(_value.copyWith(quarterPackage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PurchasePromotionStateImplCopyWith<$Res>
    implements $PurchasePromotionStateCopyWith<$Res> {
  factory _$$PurchasePromotionStateImplCopyWith(
          _$PurchasePromotionStateImpl value,
          $Res Function(_$PurchasePromotionStateImpl) then) =
      __$$PurchasePromotionStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Package? quarterPackage, QuarterlyOneShotEvent oneShotEvent});

  @override
  $PackageCopyWith<$Res>? get quarterPackage;
}

/// @nodoc
class __$$PurchasePromotionStateImplCopyWithImpl<$Res>
    extends _$PurchasePromotionStateCopyWithImpl<$Res,
        _$PurchasePromotionStateImpl>
    implements _$$PurchasePromotionStateImplCopyWith<$Res> {
  __$$PurchasePromotionStateImplCopyWithImpl(
      _$PurchasePromotionStateImpl _value,
      $Res Function(_$PurchasePromotionStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quarterPackage = freezed,
    Object? oneShotEvent = null,
  }) {
    return _then(_$PurchasePromotionStateImpl(
      freezed == quarterPackage
          ? _value.quarterPackage
          : quarterPackage // ignore: cast_nullable_to_non_nullable
              as Package?,
      null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as QuarterlyOneShotEvent,
    ));
  }
}

/// @nodoc

class _$PurchasePromotionStateImpl implements _PurchasePromotionState {
  const _$PurchasePromotionStateImpl(
      [this.quarterPackage, this.oneShotEvent = QuarterlyOneShotEvent.none]);

  @override
  final Package? quarterPackage;
  @override
  @JsonKey()
  final QuarterlyOneShotEvent oneShotEvent;

  @override
  String toString() {
    return 'PurchasePromotionState(quarterPackage: $quarterPackage, oneShotEvent: $oneShotEvent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchasePromotionStateImpl &&
            (identical(other.quarterPackage, quarterPackage) ||
                other.quarterPackage == quarterPackage) &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent));
  }

  @override
  int get hashCode => Object.hash(runtimeType, quarterPackage, oneShotEvent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchasePromotionStateImplCopyWith<_$PurchasePromotionStateImpl>
      get copyWith => __$$PurchasePromotionStateImplCopyWithImpl<
          _$PurchasePromotionStateImpl>(this, _$identity);
}

abstract class _PurchasePromotionState implements PurchasePromotionState {
  const factory _PurchasePromotionState(
      [final Package? quarterPackage,
      final QuarterlyOneShotEvent oneShotEvent]) = _$PurchasePromotionStateImpl;

  @override
  Package? get quarterPackage;
  @override
  QuarterlyOneShotEvent get oneShotEvent;
  @override
  @JsonKey(ignore: true)
  _$$PurchasePromotionStateImplCopyWith<_$PurchasePromotionStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
