import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

/// Mixin that provides helper methods for handling colors based on theme and enabled state
mixin ThemeAwareColorHelper {
  /// Whether the widget is enabled
  bool get isEnabled;

  /// Returns a color adjusted based on both theme and enabled state
  Color getThemeAwareColor(
    BuildContext context, {
    required Color darkModeColor,
    required Color lightModeColor,
    double disabledOpacity = 0.5,
  }) {
    final baseColor = context.isDarkMode ? darkModeColor : lightModeColor;
    return isEnabled ? baseColor : baseColor.withOpacity(disabledOpacity);
  }

  /// Returns background color for selected items based on theme and enabled state
  Color getSelectedBackgroundColor(BuildContext context,
      {double disabledOpacity = 0.5}) {
    // In dark mode, selected items have white background
    // In light mode, selected items have accent color background
    final baseColor = context.isDarkMode ? AppColors.white : AppColors.black;
    return isEnabled ? baseColor : baseColor.withOpacity(disabledOpacity);
  }

  /// Returns background color for unselected items based on theme and enabled state
  Color getUnselectedBackgroundColor(BuildContext context,
      {double disabledOpacity = 0.5}) {
    // In dark mode, unselected items have dark background
    // In light mode, unselected items have light gray background
    final baseColor = context.isDarkMode
        ? context.colorScheme.mainSecondary
        : AppColors.white;
    return isEnabled ? baseColor : baseColor.withOpacity(disabledOpacity);
  }

  /// Returns text color for selected items based on theme and enabled state
  Color getSelectedTextColor(BuildContext context,
      {double disabledOpacity = 0.5}) {
    // In dark mode, text on selected items is black
    // In light mode, text on selected items is white
    final baseColor = context.isDarkMode ? AppColors.black : AppColors.white;
    return isEnabled ? baseColor : baseColor.withOpacity(disabledOpacity);
  }

  /// Returns text color for unselected items based on theme and enabled state
  Color getUnselectedTextColor(BuildContext context,
      {double disabledOpacity = 0.5}) {
    // In dark mode, text on unselected items is white
    // In light mode, text on unselected items is dark
    final baseColor = context.isDarkMode ? AppColors.white : AppColors.black;
    return isEnabled ? baseColor : baseColor.withOpacity(disabledOpacity);
  }
}

/// A widget that displays difficulty level options as a horizontal list of selectable items
class DifficultySelectorWidget extends StatefulWidget {
  /// Whether the selector is interactive
  final bool enabled;

  /// Callback triggered when a difficulty level is selected
  final Function(String) onDifficultyChanged;

  /// Initially selected difficulty level
  final String? initialDifficulty;

  const DifficultySelectorWidget({
    super.key,
    this.enabled = true,
    required this.onDifficultyChanged,
    this.initialDifficulty,
  });

  @override
  State<DifficultySelectorWidget> createState() =>
      _DifficultySelectorWidgetState();
}

class _DifficultySelectorWidgetState extends State<DifficultySelectorWidget>
    with ThemeAwareColorHelper {
  int selectedIndex = 0;
  final Map<String, String> difficulties = {
    'mixed': S.current.mixed,
    'easy': S.current.easy,
    'medium': S.current.medium,
    'hard': S.current.hard,
  };

  @override
  bool get isEnabled => widget.enabled;

  @override
  void initState() {
    super.initState();
    if (widget.initialDifficulty != null) {
      selectedIndex = difficulties.keys
          .toList()
          .indexWhere((key) => key == widget.initialDifficulty);
      if (selectedIndex == -1) selectedIndex = 0;
    }
  }

  @override
  void didUpdateWidget(DifficultySelectorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialDifficulty != oldWidget.initialDifficulty) {
      selectedIndex = difficulties.keys
          .toList()
          .indexWhere((key) => key == widget.initialDifficulty);
      if (selectedIndex == -1) selectedIndex = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 36.h,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: difficulties.length,
        separatorBuilder: (context, index) => SizedBox(width: 8.w),
        itemBuilder: (context, index) {
          final key = difficulties.keys.elementAt(index);
          final value = difficulties.values.elementAt(index);
          final isSelected = selectedIndex == index;

          return GestureDetector(
            onTap: widget.enabled
                ? () {
                    setState(() {
                      selectedIndex = index;
                    });
                    widget.onDifficultyChanged(key);
                  }
                : null,
            child: DifficultyOption(
              label: value,
              isSelected: isSelected,
              colorHelper: this,
            ),
          );
        },
      ),
    );
  }
}

/// A single difficulty option item with theme-aware styling
class DifficultyOption extends StatelessWidget {
  final String label;
  final bool isSelected;
  final ThemeAwareColorHelper colorHelper;

  const DifficultyOption({
    Key? key,
    required this.label,
    required this.isSelected,
    required this.colorHelper,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 22.w),
      decoration: BoxDecoration(
        color: isSelected
            ? colorHelper.getSelectedBackgroundColor(context)
            : colorHelper.getUnselectedBackgroundColor(context),
        borderRadius: BorderRadius.circular(24.r),
      ),
      child: CommonText(
        label,
        style: TextStyle(
          color: isSelected
              ? colorHelper.getSelectedTextColor(context)
              : colorHelper.getUnselectedTextColor(context),
          fontWeight: FontWeight.w500,
          fontSize: context.isTablet ? 16 : 14.sp,
        ),
      ),
    );
  }
}
