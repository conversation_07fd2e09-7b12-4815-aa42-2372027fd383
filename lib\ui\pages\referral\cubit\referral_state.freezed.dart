// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'referral_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ReferralState {
  int get yourReferrals => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ReferralStateCopyWith<ReferralState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReferralStateCopyWith<$Res> {
  factory $ReferralStateCopyWith(
          ReferralState value, $Res Function(ReferralState) then) =
      _$ReferralStateCopyWithImpl<$Res, ReferralState>;
  @useResult
  $Res call({int yourReferrals});
}

/// @nodoc
class _$ReferralStateCopyWithImpl<$Res, $Val extends ReferralState>
    implements $ReferralStateCopyWith<$Res> {
  _$ReferralStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? yourReferrals = null,
  }) {
    return _then(_value.copyWith(
      yourReferrals: null == yourReferrals
          ? _value.yourReferrals
          : yourReferrals // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReferralStateImplCopyWith<$Res>
    implements $ReferralStateCopyWith<$Res> {
  factory _$$ReferralStateImplCopyWith(
          _$ReferralStateImpl value, $Res Function(_$ReferralStateImpl) then) =
      __$$ReferralStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int yourReferrals});
}

/// @nodoc
class __$$ReferralStateImplCopyWithImpl<$Res>
    extends _$ReferralStateCopyWithImpl<$Res, _$ReferralStateImpl>
    implements _$$ReferralStateImplCopyWith<$Res> {
  __$$ReferralStateImplCopyWithImpl(
      _$ReferralStateImpl _value, $Res Function(_$ReferralStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? yourReferrals = null,
  }) {
    return _then(_$ReferralStateImpl(
      yourReferrals: null == yourReferrals
          ? _value.yourReferrals
          : yourReferrals // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ReferralStateImpl implements _ReferralState {
  const _$ReferralStateImpl({this.yourReferrals = 0});

  @override
  @JsonKey()
  final int yourReferrals;

  @override
  String toString() {
    return 'ReferralState(yourReferrals: $yourReferrals)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferralStateImpl &&
            (identical(other.yourReferrals, yourReferrals) ||
                other.yourReferrals == yourReferrals));
  }

  @override
  int get hashCode => Object.hash(runtimeType, yourReferrals);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferralStateImplCopyWith<_$ReferralStateImpl> get copyWith =>
      __$$ReferralStateImplCopyWithImpl<_$ReferralStateImpl>(this, _$identity);
}

abstract class _ReferralState implements ReferralState {
  const factory _ReferralState({final int yourReferrals}) = _$ReferralStateImpl;

  @override
  int get yourReferrals;
  @override
  @JsonKey(ignore: true)
  _$$ReferralStateImplCopyWith<_$ReferralStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
