// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_status.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NoteStatusAdapter extends TypeAdapter<NoteStatus> {
  @override
  final int typeId = 5;

  @override
  NoteStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return NoteStatus.init;
      case 1:
        return NoteStatus.loading;
      case 2:
        return NoteStatus.success;
      case 3:
        return NoteStatus.error;
      default:
        return NoteStatus.init;
    }
  }

  @override
  void write(BinaryWriter writer, NoteStatus obj) {
    switch (obj) {
      case NoteStatus.init:
        writer.writeByte(0);
        break;
      case NoteStatus.loading:
        writer.writeByte(1);
        break;
      case NoteStatus.success:
        writer.writeByte(2);
        break;
      case NoteStatus.error:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
