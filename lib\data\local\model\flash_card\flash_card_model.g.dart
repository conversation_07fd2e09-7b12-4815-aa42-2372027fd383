// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flash_card_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FlashCardModelAdapter extends TypeAdapter<FlashCardModel> {
  @override
  final int typeId = 3;

  @override
  FlashCardModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FlashCardModel(
      question: fields[0] == null ? '' : fields[0] as String,
      answer: fields[1] == null ? '' : fields[1] as String,
      transcriptJson:
          fields[2] == null ? [] : (fields[2] as List).cast<TranscriptModel>(),
      difficulty: fields[3] == null ? '' : fields[3] as String,
      context: fields[4] == null ? '' : fields[4] as String,
    );
  }

  @override
  void write(BinaryWriter writer, FlashCardModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.question)
      ..writeByte(1)
      ..write(obj.answer)
      ..writeByte(2)
      ..write(obj.transcriptJson)
      ..writeByte(3)
      ..write(obj.difficulty)
      ..writeByte(4)
      ..write(obj.context);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FlashCardModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
