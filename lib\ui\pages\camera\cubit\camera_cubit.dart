import 'dart:async';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'camera_state.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraCubit extends BaseCubit<CameraState> {
  CameraCubit(super.initialState);

  final _fileUploadService = GetIt.instance.get<FileUploadServiceImpl>();
  final _createNoteApiService = GetIt.instance.get<CreateNoteApiServiceImpl>();
  final _taskResultApiService = GetIt.instance.get<ResultTaskApiServiceImpl>();
  final _creditApiServiceImpl = GetIt.instance.get<CreditApiServiceImpl>();
  final _localService = GetIt.instance.get<LocalService>();
  final _notificationService = GetIt.instance.get<FirebaseNotificationService>();

  final List<String> _selectedImagePaths = [];
  NoteModel _note = NoteDataMapper().mapToEntity(const NoteDto());
  bool _isAdvancedEnabled = false;
  Language? _targetCameraLanguage;
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  Timer? _bulkCaptureTimer;
  static const int maxImageCount = 10;

  FocusNode focusNodeCreateNote = FocusNode();
  final ValueNotifier<FolderModel> selectFolderNotifier = ValueNotifier(
    FolderModel(
      id: 'all_notes',
      folderName: S.current.all_note,
      backendId: '',
    ),
  );

  NoteModel getImageNote() => _note;
  CameraController? get cameraController => _cameraController;
  bool get hasUnsavedChanges => _selectedImagePaths.isNotEmpty;

  void setCameraLanguage(Language? language) {
    if (language != null) {
      _targetCameraLanguage = language;
    }
  }

  Future<void> initializeCamera() async {
    try {
      await disposeCamera();

      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        _emitCameraInitFailed();
        return;
      }

      _cameraController = CameraController(
        _cameras![0],
        ResolutionPreset.max,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _cameraController!.initialize();
      _emitCameraInitialized();

    } catch (e) {
      debugPrint('Error initializing camera: $e');
      await disposeCamera();
      _emitCameraInitFailed();
    }
  }

  void _emitCameraInitialized() {
    if (!isClosed) {
      emit(state.copyWith(
        isCameraInitialized: true,
        isCameraActive: true,
        oneShotEvent: CreateNoteWithCameraOneShotEvent.cameraInitialized,
      ));
    }
  }

  void _emitCameraInitFailed() {
    if (!isClosed) {
      emit(state.copyWith(
        oneShotEvent: CreateNoteWithCameraOneShotEvent.cameraInitFailed,
      ));
    }
  }

  Future<void> disposeCamera() async {
    if (isClosed) return;

    emit(state.copyWith(isCameraActive: false));
    stopBulkCapture();

    if (_cameraController != null) {
      await _cameraController!.dispose();
      _cameraController = null;
    }

    emit(state.copyWith(
      isCameraInitialized: false,
      isCameraActive: false,
    ));
  }

  Future<bool> takeSinglePhoto({int existingImagesCount = 0}) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return false;
    }

    try {
      final int totalImageCount = _selectedImagePaths.length + existingImagesCount;
      if (totalImageCount >= maxImageCount) {
        if (!isClosed) {
          emit(state.copyWith(
            oneShotEvent: CreateNoteWithCameraOneShotEvent.maxImagesReached,
          ));
        }
        return false;
      }

      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.cameraPhotoTaken,
      );

      final XFile photo = await _cameraController!.takePicture();
      _selectedImagePaths.add(photo.path);

      if (!isClosed) {
        emit(state.copyWith(
          selectedImagePaths: List<String>.from(_selectedImagePaths),
          hasSelectedImages: true,
        ));
      }

      // Check if we've reached the max after adding this photo
      if ((existingImagesCount + _selectedImagePaths.length) >= maxImageCount) {
        if (!isClosed) {
          emit(state.copyWith(
            oneShotEvent: CreateNoteWithCameraOneShotEvent.maxImagesReached,
          ));
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error taking picture: $e');
      return false;
    }
  }

  void showCameraPermissionDialog(BuildContext context) {
    if (context.mounted) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.cameraShowDialogDenied
      );

      showNewCupertinoDialog(
        context: context,
        title: S.current.camera_access,
        message: S.current.content_camera_access,
        image: Assets.icons.icCameraMascot,
        confirmButton: S.current.allow,
        cancelButton: S.current.donotallow,
        onConfirm: () => openAppSettings(),
        onCancel: () {
          if (!isClosed) {
            Navigator.pop(context);
            emit(state.copyWith(
              oneShotEvent: CreateNoteWithCameraOneShotEvent.none,
            ));
          }
        },
      );
    }
  }

  void stopBulkCapture() {
    _bulkCaptureTimer?.cancel();
    _bulkCaptureTimer = null;
  }

  void removeImage(int index) {
    if (index >= 0 && index < _selectedImagePaths.length) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.cameraDeleteImage,
      );

      _selectedImagePaths.removeAt(index);
      if (!isClosed) {
        emit(state.copyWith(
          selectedImagePaths: List.from(_selectedImagePaths),
          hasSelectedImages: _selectedImagePaths.isNotEmpty,
        ));
      }
    }
  }

  void swapImages(int oldIndex, int newIndex) {
    if (oldIndex < 0 || oldIndex >= _selectedImagePaths.length ||
        newIndex < 0 || newIndex >= _selectedImagePaths.length) {
      return;
    }

    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.cameraSwapImages,
    );

    final image = _selectedImagePaths.removeAt(oldIndex);
    _selectedImagePaths.insert(newIndex, image);

    if (!isClosed) {
      emit(state.copyWith(
        selectedImagePaths: List.from(_selectedImagePaths),
      ));
    }
  }

  void clearImages() {
    _selectedImagePaths.clear();
    if (!isClosed) {
      emit(state.copyWith(
        selectedImagePaths: [],
        hasSelectedImages: false,
      ));
    }
  }

  void refreshState() {
    if (!isClosed) {
      emit(state.copyWith(
        selectedImagePaths: List.from(_selectedImagePaths),
        hasSelectedImages: _selectedImagePaths.isNotEmpty,
      ));
    }
  }

  void updateImagePaths(List<String> imagePaths) {
    if (!isClosed && imagePaths.isNotEmpty) {
      final int remainingSlots = maxImageCount - _selectedImagePaths.length;

      if (remainingSlots <= 0) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithCameraOneShotEvent.maxImagesReached,
        ));
        return;
      }

      // Only add up to the remaining slots
      final List<String> pathsToAdd = imagePaths.length <= remainingSlots
          ? imagePaths
          : imagePaths.sublist(0, remainingSlots);

      _selectedImagePaths.addAll(pathsToAdd);
      emit(state.copyWith(
        selectedImagePaths: List<String>.from(_selectedImagePaths),
        hasSelectedImages: true,
      ));

      if (imagePaths.length > remainingSlots) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithCameraOneShotEvent.maxImagesReached,
        ));
      }
    }
  }

  Future<void> onSubmitImages({bool useOcr = true}) async {
    if (_selectedImagePaths.isEmpty) return;

    _logEventTrackingImage();
    if (_isAdvancedEnabled) {
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.advanced_on,
      );
    }

    final user = appCubit.getAppUser();
    if (user.documentCredit > 0 || user.rewardCredits > 0) {
      // Process images
      if (!isClosed) {
        emit(state.copyWith(isMergingImages: true));
      }

      final pdfPath = await mergeImagesToPdf(_selectedImagePaths);

      if (!isClosed) {
        emit(state.copyWith(isMergingImages: false));
      }

      if (pdfPath == null) {
        if (!isClosed) {
          emit(state.copyWith(
            oneShotEvent: CreateNoteWithCameraOneShotEvent.mergePdfFailed,
          ));
        }
        return;
      }

      if (!isClosed) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithCameraOneShotEvent.mergePdfCompleted,
        ));
      }

      // Create note
      final idNoteLocal = MyUtils.generateUniqueId();
      final noteModel = await _createInitialNoteModel(idNoteLocal, pdfPath);
      _note = noteModel;
      const stepIndex = ProcessStep.uploadingToServer;
      await HiveService().createOrUpdateNote(idNoteLocal, noteModel);

      if (!isClosed) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithCameraOneShotEvent.createNoteSuccessfully,
        ));
      }

      // Upload and process
      runCubitCatching(
        handleErrorMessage: true,
        handleLoading: false,
        action: () async {
          // Upload file
          final presignedDto = await _fileUploadService.getPresignedUrl(
            fileUniqueName: MyUtils.getFileNameFromPath(pdfPath),
          );

          await _fileUploadService.uploadFileAWS(
            note: noteModel,
            presignedDto: presignedDto,
            filePath: pdfPath,
          );

          // Create note on server
          final taskDto = await _createNoteApiService.createNotes(
            fileUrl: presignedDto.fileUrl,
            isRecord: false,
            targetLanguage: noteModel.targetLanguage,
            summaryStyle: state.summaryStyle,
            writingStyle: state.writingStyle,
            humanStyle: state.additionalInstructions,
            useOcr: useOcr,
          );

          // Get task result
          await _taskResultApiService.getTaskResult(
            taskId: taskDto.taskId,
            localNoteId: idNoteLocal,
            onStatusSuccess: () => _refreshUserCredits(),
          );

          // Update folder
          final note = HiveService().noteBox.get(idNoteLocal);
          if (note != null) {
            try {
              await _createNoteApiService.updateNote(
                backendNoteId: note.backendNoteId,
                folderId: selectFolderNotifier.value.backendId
              );

              await HiveService().noteBox.put(
                idNoteLocal,
                note.copyWith(
                  folderId: selectFolderNotifier.value.backendId,
                  folderName: selectFolderNotifier.value.backendId.isNotEmpty
                      ? selectFolderNotifier.value.folderName
                      : ''
                ),
              );
            } catch (e) {
              debugPrint('Error updating note folder: $e');
            }
          }
        },
        doOnError: (exception) =>
            Future(() => _getTaskFail(idNoteLocal, noteModel, stepIndex)),
      );
    } else {
      if (!isClosed) {
        emit(state.copyWith(
          oneShotEvent: CreateNoteWithCameraOneShotEvent.onShowIAPFromCamera,
        ));
      }
    }
  }

  Future<String?> mergeImagesToPdf(List<String> imagePaths) async {
    try {
      final pdf = pw.Document();

      for (final imagePath in imagePaths) {
        final image = File(imagePath);
        final imageBytes = await image.readAsBytes();
        final pdfImage = pw.MemoryImage(imageBytes);

        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(
                  pdfImage,
                  fit: pw.BoxFit.contain,
                  width: PdfPageFormat.a4.availableWidth,
                  height: PdfPageFormat.a4.availableHeight,
                ),
              );
            },
          ),
        );
      }

      final outputDir = await getTemporaryDirectory();
      final pdfFile = File(
        '${outputDir.path}/merged_images_${DateTime.now().millisecondsSinceEpoch}.pdf'
      );
      await pdfFile.writeAsBytes(await pdf.save());
      return pdfFile.path;
    } catch (e) {
      debugPrint('Error merging images to PDF: $e');
      return null;
    }
  }

  Future<NoteModel> _createInitialNoteModel(
    String idNoteLocal,
    String? filePath,
  ) async {
    final currentTime = DateTime.now();

    return NoteDataMapper().mapToEntity(const NoteDto()).copyWith(
      id: idNoteLocal,
      title: 'Camera',
      subtitle: MyUtils.formatDateTimeV2(currentTime.millisecondsSinceEpoch),
      type: NoteType.document.backendType,
      timeStamp: currentTime.add(const Duration(days: 2)).millisecondsSinceEpoch,
      audioFilePath: filePath,
      noteStatus: NoteStatus.loading,
      targetLanguage: _targetCameraLanguage?.code ?? 'auto',
      uploadingToServer: ProcessModel(
        stepName: S.current.uploading_to_server,
        status: ProcessStatus.inProgress,
        progressValue: 0.0,
      ),
      transcribing: ProcessModel(
        stepName: S.current.transcribing,
        status: ProcessStatus.initial,
        progressValue: 0.0,
      ),
      generatingAINote: ProcessModel(
        stepName: S.current.generating_ai_note,
        status: ProcessStatus.initial,
        progressValue: 0.0,
      ),
      userId: appCubit.getAppUser().id,
      folderId: selectFolderNotifier.value.backendId,
      folderName: selectFolderNotifier.value.backendId.isNotEmpty
          ? selectFolderNotifier.value.folderName
          : '',
    );
  }

  Future<void> _getTaskFail(
    String idNoteLocal,
    NoteModel noteModel,
    ProcessStep stepIndex,
  ) async {
    _logEventTrackingImageFail();

    final note = HiveService().noteBox.get(idNoteLocal);
    if (note == null) return;

    final currentTime = DateTime.now().millisecondsSinceEpoch;
    await HiveService().createOrUpdateNote(
      idNoteLocal,
      note.copyWith(
        noteStatus: NoteStatus.error,
        title: "Camera",
        subtitle: MyUtils.formatDateTimeV2(currentTime),
        timeStamp: currentTime,
        currentStep: stepIndex,
        uploadingToServer: _updateProcessIfMatch(
          stepIndex, note.uploadingToServer, ProcessStep.uploadingToServer
        ),
        transcribing: stepIndex == ProcessStep.generatingAINote
            ? note.transcribing.copyWith(
                status: ProcessStatus.completed,
                progressValue: 1.0,
              )
            : note.transcribing,
        generatingAINote: _updateProcessIfMatch(
          stepIndex, note.generatingAINote, ProcessStep.generatingAINote
        ),
      ),
    );

    await _notificationService.showLocalNotification(NotificationPayload(
      title: S.current.title_error_note,
      body: noteTypeToErrorMessage[noteModel.type] ??
          S.current.body_error_note_document,
      data: DataPayload(
        type: 'error_note',
        title: S.current.title_error_note,
        id: idNoteLocal,
      ),
    ));

    _refreshUserCredits();
  }

  ProcessModel _updateProcessIfMatch(
    ProcessStep stepIndex,
    ProcessModel process,
    ProcessStep step,
  ) {
    return stepIndex == step
        ? process.copyWith(status: ProcessStatus.failed)
        : process;
  }

  Future<void> _refreshUserCredits() async {
    final creditInfo = await _creditApiServiceImpl.fetchDataCredit();
    final user = appCubit.getAppUser().copyWith(
      youtubeCredit: creditInfo.youtubeCredit,
      webCredit: creditInfo.webCredit,
      audioCredit: creditInfo.audioCredit,
      rewardCredits: creditInfo.rewardCredits,
      shortsCredit: creditInfo.shortsCredit,
      documentCredit: creditInfo.documentCredit,
      userType: mapUserTypeStringToEnum(creditInfo.userType)
    );
    appCubit.updateAppUser(user);
    _localService.saveAppUser(user);
  }

  void resetEnumState() {
    if (!isClosed) {
      emit(state.copyWith(
        oneShotEvent: CreateNoteWithCameraOneShotEvent.none
      ));
    }
  }

  void resetState() {
    if (!isClosed) {
      stopBulkCapture();
      _selectedImagePaths.clear();

      final isCameraActive = _cameraController?.value.isInitialized ?? false;
      emit(CameraState.initial().copyWith(
        isCameraInitialized: isCameraActive,
        isCameraActive: isCameraActive,
      ));
    }
  }

  void discardChanges() {
    _selectedImagePaths.clear();
    if (!isClosed) {
      emit(state.copyWith(
        selectedImagePaths: [],
        hasSelectedImages: false,
      ));
    }
  }

  void setAdvancedEnabled(bool enabled) {
    _isAdvancedEnabled = enabled;
  }

  void updateSummaryStyle(String style) {
    if (!isClosed) {
      emit(state.copyWith(summaryStyle: style));
    }
  }

  void updateWritingStyle(String style) {
    if (!isClosed) {
      emit(state.copyWith(writingStyle: style));
    }
  }

  void updateAdditionalInstructions(String instructions) {
    if (!isClosed) {
      emit(state.copyWith(additionalInstructions: instructions));
    }
  }

  Future<void> setFlashMode(FlashMode mode) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      await _cameraController!.setFlashMode(mode);
      if (!isClosed) {
        emit(state.copyWith(flashMode: mode));
      }
    } catch (e) {
      debugPrint('Error setting flash mode: $e');
    }
  }

  Future<void> setZoomLevel(double zoomLevel) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      await _cameraController!.setZoomLevel(zoomLevel);
    } catch (e) {
      debugPrint('Error setting zoom level: $e');
    }
  }

  void _logEventTrackingImage() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.cameraSubmitClicked,
    );
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.general_event_create_note,
    );
  }

  void _logEventTrackingImageFail() {
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.cameraUploadFailed,
    );
    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.general_event_create_note_fail,
    );
  }

  // Event tracking methods
  void logEventTrackingOpenCameraPage() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.home_camera);
    AnalyticsService.logEventScreenView(
      screenName: EventScreenClass.cameraPage,
      screenClass: EventScreenClass.cameraPage
    );
  }

  void logEventTrackingCameraBack() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.cameraBackClicked);
  }

  void logEventTrackingPreviewImage() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.cameraPreviewImage);
  }

  void logEventTrackingCameraCaptureDone() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.takePhotoDoneClicked);
  }

  void logEventTrackingCameraCapturePreview() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.takePhotoPreviewClicked);
  }

  void logEventTrackingCameraCaptureZoom() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.takePhotoZoom);
  }

  void logEventTrackingCameraCaptureInitFailed() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.takePhotoInitFailed);
  }

  void logEventTrackingCameraCaptureMaxImagesReached() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.takePhotoMaxImagesReached);
  }

  void logEventTrackingCameraCaptureFlashToggled() {
    AnalyticsService.logAnalyticsEventNoParam(eventName: EventName.takePhotoFlashToggled);
  }
}
