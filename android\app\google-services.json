{"project_info": {"project_number": "604976496590", "project_id": "notex-ce7b0", "storage_bucket": "notex-ce7b0.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:604976496590:android:357cbce615e6d6e1d6f59f", "android_client_info": {"package_name": "com.sota.ainotex"}}, "oauth_client": [{"client_id": "604976496590-elv0opu3cbgfmkntepgc2fkd15ghqkjs.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sota.ainotex", "certificate_hash": "539dec6eca375f4294ef1338824feff468c64688"}}, {"client_id": "604976496590-p394c2ref2jhpc51apf2d165ippju8v7.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sota.ainotex", "certificate_hash": "4d9871d2f43e1d2a27a843d8c29a4002f641f218"}}, {"client_id": "604976496590-8uh0clcaovaj76an38fou1ru0nbb8soq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIobIwvmtelsycH_I4taYTCrnBlNmShak"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "604976496590-8uh0clcaovaj76an38fou1ru0nbb8soq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "604976496590-6igsrc37cvhnve6q60uf0fp7kcouuf4c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sota.ainotex.stg"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:604976496590:android:2bbc40a06f1295c1d6f59f", "android_client_info": {"package_name": "com.sota.ainotex.dev"}}, "oauth_client": [{"client_id": "604976496590-me0l5q89n00cn0loagdpk83uh3cbvmd1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sota.ainotex.dev", "certificate_hash": "4d9871d2f43e1d2a27a843d8c29a4002f641f218"}}, {"client_id": "604976496590-onb007igljlfcpcg17afg6cd9kc2roeg.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sota.ainotex.dev", "certificate_hash": "539dec6eca375f4294ef1338824feff468c64688"}}, {"client_id": "604976496590-rib93gjc1q9pboktfha3b59gci7i0ik0.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sota.ainotex.dev", "certificate_hash": "3dae64a729b9b9c0d0ee6b4b1031e56377e05082"}}, {"client_id": "604976496590-8uh0clcaovaj76an38fou1ru0nbb8soq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIobIwvmtelsycH_I4taYTCrnBlNmShak"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "604976496590-8uh0clcaovaj76an38fou1ru0nbb8soq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "604976496590-6igsrc37cvhnve6q60uf0fp7kcouuf4c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sota.ainotex.stg"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:604976496590:android:5c81477532f69f4dd6f59f", "android_client_info": {"package_name": "com.sota.ainotex.stg"}}, "oauth_client": [{"client_id": "604976496590-8uh0clcaovaj76an38fou1ru0nbb8soq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBIobIwvmtelsycH_I4taYTCrnBlNmShak"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "604976496590-8uh0clcaovaj76an38fou1ru0nbb8soq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "604976496590-6igsrc37cvhnve6q60uf0fp7kcouuf4c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sota.ainotex.stg"}}]}}}], "configuration_version": "1"}