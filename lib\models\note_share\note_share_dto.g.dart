// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_share_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NoteShareDtoImpl _$$NoteShareDtoImplFromJson(Map<String, dynamic> json) =>
    _$NoteShareDtoImpl(
      isPublic: json['is_public'] as bool? ?? false,
      isPasswordProtected: json['is_password_protected'] as bool? ?? false,
      sharePassword: json['share_password'] as String? ?? '',
      shareLink: json['share_link'] as String? ?? '',
    );

Map<String, dynamic> _$$NoteShareDtoImplToJson(_$NoteShareDtoImpl instance) =>
    <String, dynamic>{
      'is_public': instance.isPublic,
      'is_password_protected': instance.isPasswordProtected,
      'share_password': instance.sharePassword,
      'share_link': instance.shareLink,
    };
