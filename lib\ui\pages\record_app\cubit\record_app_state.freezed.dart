// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_app_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RecordAppState {
  RecordOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;
  bool get isRecording => throw _privateConstructorUsedError;
  bool get isPausing => throw _privateConstructorUsedError;
  String get filePath => throw _privateConstructorUsedError;
  String get fileName => throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RecordAppStateCopyWith<RecordAppState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordAppStateCopyWith<$Res> {
  factory $RecordAppStateCopyWith(
          RecordAppState value, $Res Function(RecordAppState) then) =
      _$RecordAppStateCopyWithImpl<$Res, RecordAppState>;
  @useResult
  $Res call(
      {RecordOneShotEvent oneShotEvent,
      bool isRecording,
      bool isPausing,
      String filePath,
      String fileName,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class _$RecordAppStateCopyWithImpl<$Res, $Val extends RecordAppState>
    implements $RecordAppStateCopyWith<$Res> {
  _$RecordAppStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? isRecording = null,
    Object? isPausing = null,
    Object? filePath = null,
    Object? fileName = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as RecordOneShotEvent,
      isRecording: null == isRecording
          ? _value.isRecording
          : isRecording // ignore: cast_nullable_to_non_nullable
              as bool,
      isPausing: null == isPausing
          ? _value.isPausing
          : isPausing // ignore: cast_nullable_to_non_nullable
              as bool,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecordAppStateImplCopyWith<$Res>
    implements $RecordAppStateCopyWith<$Res> {
  factory _$$RecordAppStateImplCopyWith(_$RecordAppStateImpl value,
          $Res Function(_$RecordAppStateImpl) then) =
      __$$RecordAppStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {RecordOneShotEvent oneShotEvent,
      bool isRecording,
      bool isPausing,
      String filePath,
      String fileName,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class __$$RecordAppStateImplCopyWithImpl<$Res>
    extends _$RecordAppStateCopyWithImpl<$Res, _$RecordAppStateImpl>
    implements _$$RecordAppStateImplCopyWith<$Res> {
  __$$RecordAppStateImplCopyWithImpl(
      _$RecordAppStateImpl _value, $Res Function(_$RecordAppStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? isRecording = null,
    Object? isPausing = null,
    Object? filePath = null,
    Object? fileName = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_$RecordAppStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as RecordOneShotEvent,
      isRecording: null == isRecording
          ? _value.isRecording
          : isRecording // ignore: cast_nullable_to_non_nullable
              as bool,
      isPausing: null == isPausing
          ? _value.isPausing
          : isPausing // ignore: cast_nullable_to_non_nullable
              as bool,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      fileName: null == fileName
          ? _value.fileName
          : fileName // ignore: cast_nullable_to_non_nullable
              as String,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RecordAppStateImpl implements _RecordAppState {
  const _$RecordAppStateImpl(
      {this.oneShotEvent = RecordOneShotEvent.none,
      this.isRecording = false,
      this.isPausing = false,
      this.filePath = '',
      this.fileName = '',
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = ''});

  @override
  @JsonKey()
  final RecordOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final bool isRecording;
  @override
  @JsonKey()
  final bool isPausing;
  @override
  @JsonKey()
  final String filePath;
  @override
  @JsonKey()
  final String fileName;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;

  @override
  String toString() {
    return 'RecordAppState(oneShotEvent: $oneShotEvent, isRecording: $isRecording, isPausing: $isPausing, filePath: $filePath, fileName: $fileName, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecordAppStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.isRecording, isRecording) ||
                other.isRecording == isRecording) &&
            (identical(other.isPausing, isPausing) ||
                other.isPausing == isPausing) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.fileName, fileName) ||
                other.fileName == fileName) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      isRecording,
      isPausing,
      filePath,
      fileName,
      summaryStyle,
      writingStyle,
      additionalInstructions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RecordAppStateImplCopyWith<_$RecordAppStateImpl> get copyWith =>
      __$$RecordAppStateImplCopyWithImpl<_$RecordAppStateImpl>(
          this, _$identity);
}

abstract class _RecordAppState implements RecordAppState {
  const factory _RecordAppState(
      {final RecordOneShotEvent oneShotEvent,
      final bool isRecording,
      final bool isPausing,
      final String filePath,
      final String fileName,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions}) = _$RecordAppStateImpl;

  @override
  RecordOneShotEvent get oneShotEvent;
  @override
  bool get isRecording;
  @override
  bool get isPausing;
  @override
  String get filePath;
  @override
  String get fileName;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  @JsonKey(ignore: true)
  _$$RecordAppStateImplCopyWith<_$RecordAppStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
