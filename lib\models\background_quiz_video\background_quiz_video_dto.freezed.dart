// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'background_quiz_video_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

BackgroundQuizVideoDto _$BackgroundQuizVideoDtoFromJson(
    Map<String, dynamic> json) {
  return _BackgroundQuizVideoDto.fromJson(json);
}

/// @nodoc
mixin _$BackgroundQuizVideoDto {
  @JsonKey(name: 'template_id')
  String get templateId => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'thumbnail_url')
  String get thumbnailUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'video_url')
  String get videoUrl => throw _privateConstructorUsedError;
  int get width => throw _privateConstructorUsedError;
  int get height => throw _privateConstructorUsedError;
  @JsonKey(name: 'display_order')
  int get displayOrder => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BackgroundQuizVideoDtoCopyWith<BackgroundQuizVideoDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackgroundQuizVideoDtoCopyWith<$Res> {
  factory $BackgroundQuizVideoDtoCopyWith(BackgroundQuizVideoDto value,
          $Res Function(BackgroundQuizVideoDto) then) =
      _$BackgroundQuizVideoDtoCopyWithImpl<$Res, BackgroundQuizVideoDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'template_id') String templateId,
      String title,
      @JsonKey(name: 'thumbnail_url') String thumbnailUrl,
      @JsonKey(name: 'video_url') String videoUrl,
      int width,
      int height,
      @JsonKey(name: 'display_order') int displayOrder});
}

/// @nodoc
class _$BackgroundQuizVideoDtoCopyWithImpl<$Res,
        $Val extends BackgroundQuizVideoDto>
    implements $BackgroundQuizVideoDtoCopyWith<$Res> {
  _$BackgroundQuizVideoDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? templateId = null,
    Object? title = null,
    Object? thumbnailUrl = null,
    Object? videoUrl = null,
    Object? width = null,
    Object? height = null,
    Object? displayOrder = null,
  }) {
    return _then(_value.copyWith(
      templateId: null == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      videoUrl: null == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BackgroundQuizVideoDtoImplCopyWith<$Res>
    implements $BackgroundQuizVideoDtoCopyWith<$Res> {
  factory _$$BackgroundQuizVideoDtoImplCopyWith(
          _$BackgroundQuizVideoDtoImpl value,
          $Res Function(_$BackgroundQuizVideoDtoImpl) then) =
      __$$BackgroundQuizVideoDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'template_id') String templateId,
      String title,
      @JsonKey(name: 'thumbnail_url') String thumbnailUrl,
      @JsonKey(name: 'video_url') String videoUrl,
      int width,
      int height,
      @JsonKey(name: 'display_order') int displayOrder});
}

/// @nodoc
class __$$BackgroundQuizVideoDtoImplCopyWithImpl<$Res>
    extends _$BackgroundQuizVideoDtoCopyWithImpl<$Res,
        _$BackgroundQuizVideoDtoImpl>
    implements _$$BackgroundQuizVideoDtoImplCopyWith<$Res> {
  __$$BackgroundQuizVideoDtoImplCopyWithImpl(
      _$BackgroundQuizVideoDtoImpl _value,
      $Res Function(_$BackgroundQuizVideoDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? templateId = null,
    Object? title = null,
    Object? thumbnailUrl = null,
    Object? videoUrl = null,
    Object? width = null,
    Object? height = null,
    Object? displayOrder = null,
  }) {
    return _then(_$BackgroundQuizVideoDtoImpl(
      templateId: null == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      videoUrl: null == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackgroundQuizVideoDtoImpl implements _BackgroundQuizVideoDto {
  const _$BackgroundQuizVideoDtoImpl(
      {@JsonKey(name: 'template_id') this.templateId = '',
      this.title = '',
      @JsonKey(name: 'thumbnail_url') this.thumbnailUrl = '',
      @JsonKey(name: 'video_url') this.videoUrl = '',
      this.width = 0,
      this.height = 0,
      @JsonKey(name: 'display_order') this.displayOrder = 0});

  factory _$BackgroundQuizVideoDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackgroundQuizVideoDtoImplFromJson(json);

  @override
  @JsonKey(name: 'template_id')
  final String templateId;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey(name: 'thumbnail_url')
  final String thumbnailUrl;
  @override
  @JsonKey(name: 'video_url')
  final String videoUrl;
  @override
  @JsonKey()
  final int width;
  @override
  @JsonKey()
  final int height;
  @override
  @JsonKey(name: 'display_order')
  final int displayOrder;

  @override
  String toString() {
    return 'BackgroundQuizVideoDto(templateId: $templateId, title: $title, thumbnailUrl: $thumbnailUrl, videoUrl: $videoUrl, width: $width, height: $height, displayOrder: $displayOrder)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackgroundQuizVideoDtoImpl &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.displayOrder, displayOrder) ||
                other.displayOrder == displayOrder));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, templateId, title, thumbnailUrl,
      videoUrl, width, height, displayOrder);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BackgroundQuizVideoDtoImplCopyWith<_$BackgroundQuizVideoDtoImpl>
      get copyWith => __$$BackgroundQuizVideoDtoImplCopyWithImpl<
          _$BackgroundQuizVideoDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BackgroundQuizVideoDtoImplToJson(
      this,
    );
  }
}

abstract class _BackgroundQuizVideoDto implements BackgroundQuizVideoDto {
  const factory _BackgroundQuizVideoDto(
          {@JsonKey(name: 'template_id') final String templateId,
          final String title,
          @JsonKey(name: 'thumbnail_url') final String thumbnailUrl,
          @JsonKey(name: 'video_url') final String videoUrl,
          final int width,
          final int height,
          @JsonKey(name: 'display_order') final int displayOrder}) =
      _$BackgroundQuizVideoDtoImpl;

  factory _BackgroundQuizVideoDto.fromJson(Map<String, dynamic> json) =
      _$BackgroundQuizVideoDtoImpl.fromJson;

  @override
  @JsonKey(name: 'template_id')
  String get templateId;
  @override
  String get title;
  @override
  @JsonKey(name: 'thumbnail_url')
  String get thumbnailUrl;
  @override
  @JsonKey(name: 'video_url')
  String get videoUrl;
  @override
  int get width;
  @override
  int get height;
  @override
  @JsonKey(name: 'display_order')
  int get displayOrder;
  @override
  @JsonKey(ignore: true)
  _$$BackgroundQuizVideoDtoImplCopyWith<_$BackgroundQuizVideoDtoImpl>
      get copyWith => throw _privateConstructorUsedError;
}
