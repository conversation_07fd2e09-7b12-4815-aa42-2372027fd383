import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class SuggestedTagsList extends StatelessWidget {
  final bool isTablet;
  final Function(String) onTagSelected;

  const SuggestedTagsList({
    super.key,
    required this.isTablet,
    required this.onTagSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 16 : 16.0.w),
              child: Wrap(
                spacing: 12.w,
                children: [
                  for (var index = 1; index < FilterType.values.length; index++)
                    SuggestedTagItem(
                      tag: FilterType.values[index].text,
                      onTagSelected: () => onTagSelected(FilterType.values[index].text),
                      isTablet: isTablet,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
