// ignore_for_file: constant_identifier_names

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppConstants {
  static const String fontSFPro = "SF-Pro";

  static Radius kDefaultRadius = Radius.circular(4.r);

  static double padding = 16.w;
  static double appWidth = 375.w;
  static double appHeight = 812.h;
  static SizedBox kSpacingItem0 = SizedBox(height: 0.h);
  static SizedBox kSpacingItem1 = SizedBox(height: 1.h);
  static SizedBox kSpacingItem2 = SizedBox(height: 2.h);
  static SizedBox kSpacingItem3 = SizedBox(height: 3.h);
  static SizedBox kSpacingItem4 = SizedBox(height: 4.h);
  static SizedBox kSpacingItem5 = SizedBox(height: 5.h);
  static SizedBox kSpacingItem6 = SizedBox(height: 6.h);
  static SizedBox kSpacingItem7 = SizedBox(height: 7.h);
  static SizedBox kSpacingItem8 = SizedBox(height: 8.h);
  static SizedBox kSpacingItem10 = SizedBox(height: 10.h);
  static SizedBox kSpacingItem12 = SizedBox(height: 12.h);
  static SizedBox kSpacingItem15 = SizedBox(height: 15.h);
  static SizedBox kSpacingItem16 = SizedBox(height: 16.h);
  static SizedBox kSpacingItem18 = SizedBox(height: 18.h);
  static SizedBox kSpacingItem20 = SizedBox(height: 20.h);
  static SizedBox kSpacingItem22 = SizedBox(height: 22.h);
  static SizedBox kSpacingItem24 = SizedBox(height: 24.h);
  static SizedBox kSpacingItem25 = SizedBox(height: 25.h);
  static SizedBox kSpacingItem30 = SizedBox(height: 30.h);
  static SizedBox kSpacingItem34 = SizedBox(height: 34.h);
  static SizedBox kSpacingItem36 = SizedBox(height: 36.h);
  static SizedBox kSpacingItem40 = SizedBox(height: 40.h);
  static SizedBox kSpacingItem32 = SizedBox(height: 32.h);
  static SizedBox kSpacingItem35 = SizedBox(height: 35.h);
  static SizedBox kSpacingItem41 = SizedBox(height: 41.h);
  static SizedBox kSpacingItem48 = SizedBox(height: 48.h);
  static SizedBox kSpacingItem50 = SizedBox(height: 50.h);
  static SizedBox kSpacingItem52 = SizedBox(height: 52.h);
  static SizedBox kSpacingItem56 = SizedBox(height: 56.h);
  static SizedBox kSpacingItem60 = SizedBox(height: 60.h);
  static SizedBox kSpacingItem65 = SizedBox(height: 65.h);
  static SizedBox kSpacingItem68 = SizedBox(height: 68.h);
  static SizedBox kSpacingItem84 = SizedBox(height: 84.h);
  static SizedBox kSpacingItem100 = SizedBox(height: 100.h);
  static SizedBox kSpacingItem120 = SizedBox(height: 120.h);
  static SizedBox kSpacingItem130 = SizedBox(height: 130.h);
  static SizedBox kSpacingItem150 = SizedBox(height: 150.h);
  static SizedBox kSpacingItem152 = SizedBox(height: 152.h);
  static SizedBox kSpacingItem200 = SizedBox(height: 200.h);
  static SizedBox kSpacingItem220 = SizedBox(height: 300.h);
  static SizedBox kSpacingItem45 = SizedBox(height: 45.h);

  static SizedBox kSpacingItemW2 = SizedBox(width: 2.w);
  static SizedBox kSpacingItemW4 = SizedBox(width: 4.w);
  static SizedBox kSpacingItemW5 = SizedBox(width: 5.w);

  static SizedBox kSpacingItemW6 = SizedBox(width: 6.w);
  static SizedBox kSpacingItemW8 = SizedBox(width: 8.w);
  static SizedBox kSpacingItemW10 = SizedBox(width: 10.w);
  static SizedBox kSpacingItemW12 = SizedBox(width: 12.w);
  static SizedBox kSpacingItemW14 = SizedBox(width: 14.w);
  static SizedBox kSpacingItemW16 = SizedBox(width: 16.w);
  static SizedBox kSpacingItemW20 = SizedBox(width: 20.w);
  static SizedBox kSpacingItemW24 = SizedBox(width: 24.w);
  static SizedBox kSpacingItemW40 = SizedBox(width: 40.w);

  static int badRequestStatus = 400;
  static int badRequestForbidden = 403;

  // Purchase
  static String idProductWeekly = "sota_01";
  static String idProductMonthly = "sota_02";
  static String idProductYearly = "sota_03";
  static const String idGoogleStore = 'goog_MUsOYhiRHYQzYtWuDnLfNWLaKDz';
  static const String idGoogleStoreProd = 'goog_MUsOYhiRHYQzYtWuDnLfNWLaKDz';
  static const String idAppleStoreDev = 'appl_jXOnkYDoXZLFFTbJjBxLTGJuSHZ';
  static const String idAppleStoreProd = 'appl_jXOnkYDoXZLFFTbJjBxLTGJuSHZ';
  static const String entitlementID = 'pro';
  static const String privacyURL = 'https://notexapp.com/privacy';
  static const String termUrl = 'https://notexapp.com/tos';
  static const String paymentWebhookPending = 'payment_webhook_pending';
  static const String paymentWebhookSuccess = 'payment_webhook_success';
  static const String portfolioGenerateSuccess = 'portfolio_generate_success';
  static const String filePathSuccess = 'file_path_success';
  static const String albumName = 'NoteX';
  static const String advertisingRoute = 'AdvertisingRoute';
  static const int receiveTimeout = 180;
  static const String urlShareNoteDev = 'https://dev.notexapp.com/app/note/';
  static const String urlShareNotePro = 'https://notexapp.com/app/note/';
  static const String urlNotexPageOnPlayStore =
      'https://play.google.com/store/apps/details?id=com.sota.ainotex&hl=en';
  static const String urlItunesLookup =
      'https://itunes.apple.com/lookup?id=6654910983';
  static const String urlAppleStore =
      'https://apps.apple.com/app/ai-note-taker-notex-ai/id6654910983?';
  static const String urlAndroidStore =
      'https://play.google.com/store/apps/details?id=com.sota.ainotex';

  static const String appStoreId = '6654910983';
  static const String revenueCatProEntitlementId = 'pro - production';
  static const String revenueCatEssentialLifetimeEntitlementId =
      'lifetime-essential';
  static const String proUser = 'pro';
  static const String freeUser = 'free';
  static const String proLiteUser = 'pro_lite';
  static const String referralUrl = ' https://notex.onelink.me/8OE5/jjh7jqem';
  static const List<DeviceOrientation> allOrientations = [
    DeviceOrientation.landscapeRight,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ];

  static const List<DeviceOrientation> portraitOnly = [
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ];

  static int maxLengthInputText = 20000;
  static int numberOfLimitMessages = 2;

  static const String kCardCountKey = 'cardCount';
  static const String kDifficultyKey = 'difficulty';
  static const String kTopicKey = 'topic';
  static const String kAdvancedModeKey = 'isAdvancedMode';

  // Constants for default values
  static const String kDefaultCardCount = 'auto';
  static const String kDefaultDifficulty = 'mixed';
  static const String kDefaultTopic = '';
  static const bool kDefaultAdvancedMode = false;

}
