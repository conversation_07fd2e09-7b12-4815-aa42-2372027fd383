import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';
import 'package:pull_down_button/pull_down_button.dart';

class AudioItem extends StatelessWidget {
  final File file;
  final String fileName;
  final String lastModified;
  final String fileSize;
  final AudioController controller;
  final Function(File) onCreateNote;
  final Function(File) onDelete;
  final bool isInFailedBox;
  final NoteModel? noteFail;

  const AudioItem({
    Key? key,
    required this.file,
    required this.fileName,
    required this.lastModified,
    required this.fileSize,
    required this.controller,
    required this.onCreateNote,
    required this.onDelete,
    this.isInFailedBox = false,
    this.noteFail,
  }) : super(key: key);

  bool get isCurrentlySelected =>
      controller.currentPlayingFile?.path == file.path;

  bool get isPlaying => isCurrentlySelected && controller.isPlaying;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: context.colorScheme.mainNeutral,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildMainContent(context),
          if (isCurrentlySelected) ...[
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: isCurrentlySelected ? 70.h : 0,
              child: _buildPlayerControls(context),
            ),
            AppConstants.kSpacingItem8,
          ]
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => controller.handleAudioTap(file),
      child: Container(
        padding: EdgeInsets.only(
          top: 12.h,
          bottom: 12.h,
          left: 16.w,
          right: 2.w,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: 44.w,
                  height: 44.w,
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainGray.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    CupertinoIcons.waveform,
                    color: context.colorScheme.mainPrimary,
                  ),
                ),
                if (isInFailedBox)
                  Positioned(
                    right: 6,
                    bottom: 0,
                    child: Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
            AppConstants.kSpacingItemW12,
            _buildFileInfo(context),
            _buildMoreButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFileInfo(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  fileName.replaceAll('.m4a', ''),
                  style: TextStyle(
                    color: context.colorScheme.mainPrimary,
                    fontWeight: FontWeight.w500,
                    fontSize: 16.sp,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          AppConstants.kSpacingItem4,
          Row(
            children: [
              Flexible(
                child: Text(
                  lastModified,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: context.colorScheme.mainGray,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                ' • ',
                style: TextStyle(
                  fontSize: 10.sp,
                  color: context.colorScheme.mainGray,
                ),
              ),
              Text(
                fileSize,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: context.colorScheme.mainGray,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMoreButton(BuildContext context) {
    return PullDownButton(
      itemBuilder: (context) => [
        PullDownMenuItem(
          title: S.current.Regenerate,
          iconWidget: SvgPicture.asset(
            Assets.icons.icGenAi,
            width: 24.w,
            height: 24.w,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
          onTap: () async {
            final savedTabs =
                await GetIt.instance.get<LocalService>().loadSelectedItems();
            AnalyticsService.logAnalyticsEventNoParam(
              eventName:
                  EventName.setting_manage_recordings_Regenerate_button_click,
            );
            if (!isInFailedBox) {
              onCreateNote(file);
            } else {
              Navigator.of(context).popUntil((route) => route.isFirst);
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => MyNoteDetailPage(
                    noteModel: noteFail!,
                    isTablet: context.isTablet,
                    from: NoteDetailPageFrom.homeScreen,
                    savedTabs: savedTabs,
                  ),
                ),
              );
            }
          },
        ),
        PullDownMenuItem(
          title: S.current.share_audio_file,
          iconWidget: SvgPicture.asset(
            Assets.icons.icShare3,
            width: 24.w,
            height: 24.w,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainPrimary,
              BlendMode.srcIn,
            ),
          ),
          onTap: () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName: EventName.setting_manage_recordings_Share_button_click,
            );
            final box = context.findRenderObject() as RenderBox?;
            ShareService().shareFile(
              filePath: file.path,
              sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
            );
          },
        ),
        PullDownMenuItem(
          title: S.current.delete,
          iconWidget: SvgPicture.asset(
            Assets.icons.icDelete,
            width: 24.w,
            height: 24.w,
            colorFilter: const ColorFilter.mode(
              Colors.red,
              BlendMode.srcIn,
            ),
          ),
          isDestructive: true,
          onTap: () {
            AnalyticsService.logAnalyticsEventNoParam(
              eventName:
                  EventName.setting_manage_recordings_Delete_button_click,
            );
            onDelete(file);
          },
        ),
      ],
      buttonBuilder: (context, showMenu) => IconButton(
        padding: EdgeInsets.all(context.isTablet ? 8 : 4.w),
        icon: SvgPicture.asset(
          Assets.icons.icMore,
          width: 24.w,
          height: 24.w,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainPrimary,
            BlendMode.srcIn,
          ),
        ),
        onPressed: showMenu,
      ),
    );
  }

  Widget _buildPlayerControls(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 30.h,
          child: Row(
            children: [
              SizedBox(
                width: 68.w,
                child: _buildTimeLabel(true),
              ),
              Expanded(
                child: _buildProgressBar(),
              ),
              SizedBox(
                width: 68.w,
                child: _buildTimeLabel(false),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 40.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildSkipBackButton(context),
              SizedBox(width: 16.w),
              InkWell(
                onTap: () => controller.handleAudioTap(file),
                child: Container(
                  width: 28.w,
                  height: 28.w,
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainNeutral,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      isPlaying
                          ? CupertinoIcons.pause_fill
                          : CupertinoIcons.play_fill,
                      color: context.colorScheme.mainPrimary,
                      size: 24.sp,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              _buildSkipForwardButton(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimeLabel(bool isStart) {
    return ValueListenableBuilder<Duration>(
      valueListenable: isStart ? controller.position : controller.duration,
      builder: (context, duration, _) {
        return Text(
          _formatDuration(duration),
          style: TextStyle(
            fontSize: 12.sp,
            color: context.colorScheme.mainGray,
          ),
          textAlign: TextAlign.center,
        );
      },
    );
  }

  Widget _buildProgressBar() {
    return ValueListenableBuilder<Duration>(
      valueListenable: controller.position,
      builder: (context, position, _) {
        return ValueListenableBuilder<Duration>(
          valueListenable: controller.duration,
          builder: (context, duration, _) {
            return SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 4.0,
                thumbShape:
                    const RoundSliderThumbShape(enabledThumbRadius: 6.0),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 8.0),
                activeTrackColor: context.colorScheme.mainBlue,
                inactiveTrackColor:
                    context.colorScheme.mainPrimary.withOpacity(0.3),
                thumbColor: AppColors.white,
                overlayColor: Colors.blue.withOpacity(0.4),
              ),
              child: Slider(
                min: 0.0,
                max: duration.inMilliseconds.toDouble(),
                value: position.inMilliseconds
                    .toDouble()
                    .clamp(0.0, duration.inMilliseconds.toDouble()),
                onChanged: (value) {
                  controller.seekTo(Duration(milliseconds: value.toInt()));
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSkipBackButton(BuildContext context) {
    return _buildControlButton(
      context,
      onTap: () {
        final newPosition =
            controller.position.value - const Duration(seconds: 5);
        controller.seekTo(newPosition.isNegative ? Duration.zero : newPosition);
      },
      icon: Assets.icons.icPlayLeft,
    );
  }

  Widget _buildSkipForwardButton(BuildContext context) {
    return _buildControlButton(
      context,
      onTap: () {
        final newPosition =
            controller.position.value + const Duration(seconds: 5);
        final maxDuration = controller.duration.value;
        controller
            .seekTo(newPosition > maxDuration ? maxDuration : newPosition);
      },
      icon: Assets.icons.icPlayRight,
    );
  }

  Widget _buildControlButton(
    BuildContext context, {
    required VoidCallback onTap,
    required String icon,
  }) {
    return InkWell(
      onTap: onTap,
      child: Center(
        child: SvgPicture.asset(
          icon,
          width: 24.w,
          height: 24.w,
          colorFilter: ColorFilter.mode(
            context.colorScheme.mainPrimary,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return hours > 0 ? '$hours:$minutes:$seconds' : '$minutes:$seconds';
  }
}
