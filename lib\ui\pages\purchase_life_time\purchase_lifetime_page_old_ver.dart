import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/purchase_life_time/cubit/purchase_life_time_state.dart';

class PurchaseLifeTimePageOldVer extends StatefulWidget {
  final PurchaseLifetimeFrom from;
  final PurchaseLifetimeType type;

  const PurchaseLifeTimePageOldVer({
    super.key,
    required this.from,
    this.type = PurchaseLifetimeType.pro,
  });

  @override
  State<PurchaseLifeTimePageOldVer> createState() =>
      _PurchaseLifeTimePageOldVerState();
}

class _PurchaseLifeTimePageOldVerState extends BasePageStateDelegate<
    PurchaseLifeTimePageOldVer,
    PurchaseLifeTimeCubitOldVer> with SingleTickerProviderStateMixin {
  static const _animationDuration = Duration(seconds: 5);
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _initializeState();
  }

  void _initializeState() {
    cubit.initData(type: widget.type, from: widget.from);
    _controller = AnimationController(
      duration: _animationDuration,
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget buildPageListeners({required Widget child}) {
    return BlocListener<PurchaseLifeTimeCubitOldVer, PurchaseLifeTimeState>(
      listenWhen: (previous, current) =>
          previous.oneShotEvent != current.oneShotEvent &&
          current.oneShotEvent != PurchaseLifeTimeStateOneShotEvent.none,
      listener: _handlePurchaseEvents,
      child: child,
    );
  }

  void _handlePurchaseEvents(
      BuildContext context, PurchaseLifeTimeState state) {
    CommonDialogs.closeLoading();
    switch (state.oneShotEvent) {
      case PurchaseLifeTimeStateOneShotEvent.purchaseSuccess:
        _showPurchaseSuccessDialog(context);
        break;
      case PurchaseLifeTimeStateOneShotEvent.purchaseInitFail:
        CommonDialogs.showErrorDialog(context,
            title: S.current.purchase_init_fail);
        break;
      case PurchaseLifeTimeStateOneShotEvent.none:
        break;
      case PurchaseLifeTimeStateOneShotEvent.purchaseLifetimeProSuccess:
        break;
      case PurchaseLifeTimeStateOneShotEvent.purchaseLifetimeProFail:
        break;
    }
    cubit.resetEnumState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF03031B),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              Assets.images.imgBlackFridayv2.path,
              fit: BoxFit.fill,
            ),
          ),
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: MediaQuery.of(context).padding.top),
                _buildHeader(context),
                _buildLimitedTimeOffer(),
                context.isLandscape
                    ? AppConstants.kSpacingItem84
                    : AppConstants.kSpacingItem100,
                AppConstants.kSpacingItem32,
                _buildBoxLifeTime(context),

                if (!cubit.isThemeEssential()) ...[
                  cubit.appCubit.isTablet
                      ? context.isLandscape
                          ? AppConstants.kSpacingItem30
                          : AppConstants.kSpacingItem60
                      : AppConstants.kSpacingItem40,
                ],
                if (cubit.isThemeEssential()) ...[
                  AppConstants.kSpacingItem4,
                  _buildMaxTimePerFile(),
                  cubit.appCubit.isTablet
                      ? AppConstants.kSpacingItem40
                      : AppConstants.kSpacingItem12,
                ],
                AppConstants.kSpacingItem32,

                // const CountdownTimerWidget(),
                AppConstants.kSpacingItem32,
                _buildUnlockButton(),
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLimitedTimeOffer() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: GradientText(
          text: S.current.upgrade_to_full_pro_access,
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF9BBFE),
              Color(0xFF8085FE),
              Color(0xFF138AFE),
            ],
          ),
          style: TextStyle(
            fontSize: cubit.appCubit.isTablet
                ? context.isLandscape
                    ? 32
                    : 52
                : 32.sp,
            fontWeight: FontWeight.w600,
            height: 1.50,
            letterSpacing: -1.60,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildMaxTimePerFile() {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: cubit.appCubit.isTablet ? 225 : 64.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CommonText(
            S.current.max_60_min_per_file,
            style: TextStyle(
              color: context.colorScheme.mainNeutral,
              fontSize: cubit.appCubit.isTablet ? 13 : 13.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnlockButton() {
    return AnimatedPulse(
      child: Stack(
        children: [
          AppCommonButton(
            margin: EdgeInsets.symmetric(horizontal: 24.w),
            borderRadius: BorderRadius.circular(48.r),
            width: cubit.appCubit.isTablet ? 394 : double.infinity,
            height: cubit.appCubit.isTablet ? 64 : 56.h,
            gradient: const LinearGradient(
              colors: [Color(0xFFF8BAFE), Color(0xFF8085FE), Color(0xFF138AFE)],
            ),
            textWidget: Text(
              S.current.get_offer_now,
              style: TextStyle(
                fontSize: cubit.appCubit.isTablet ? 17 : 17.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
            onPressed: () {
              if (cubit.appCubit.getLifetimeProPackage().isEmpty) {
                CommonDialogs.showToast(
                    'We couldn\'t connect to the App Store/Play Store at this time. Please close this screen or relaunch the application to try again.');
              } else {
                cubit.purchaseLifeTimeProduct();
              }
              cubit.logEventOnConfirmPurchase();
            },
          ),
          Positioned(
            top: 0,
            bottom: 0,
            right: context.isTablet
                ? context.isLandscape
                    ? 80
                    : 60
                : 30.w,
            child: const ArrowAnimation(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          //const Spacer(),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              HapticFeedback.mediumImpact();
              cubit.onCloseButtonPressed(context);
            },
            child: SvgPicture.asset(
              Assets.icons.icCloseCircle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBoxLifeTime(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
              horizontal: cubit.appCubit.isTablet ? 121 : 38.w),
          child: CustomPaint(
            painter: AnimatedBorderPainter(
              animation: _controller,
              color: const [
                Color(0xFFF9BBFE),
                Color(0xFF8588FE),
                Color(0xFF8588FE),
              ],
            ),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              foregroundDecoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(Assets.images.imgFrameIapLifetime.path),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppConstants.kSpacingItem60,
                  _buildPriceSection(),
                  CommonText(
                    S.current.one_time_payment,
                    style: TextStyle(
                      color: context.colorScheme.mainGray,
                      fontSize: cubit.appCubit.isTablet ? 14 : 14.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  AppConstants.kSpacingItem12,
                  _buildFeatureItem(cubit.isThemeEssential()
                      ? S.current.unlimited_youtube_document_ai_notes
                      : S.current.unlock_unlimited_access_to_all_ai_features),
                  AppConstants.kSpacingItem10,
                  _buildFeatureItem(cubit.isThemeEssential()
                      ? S.current.audio_recording_ai_notes_daily
                      : S.current.free_updates),
                  AppConstants.kSpacingItem10,
                  _buildFeatureItem(S.current.early_supporters_exclusive_offer),
                  if (cubit.isThemeEssential()) ...[
                    AppConstants.kSpacingItem10,
                    _buildFeatureItem(
                      S.current.upgrade_to_pro_tier_at_a_special_price,
                    ),
                  ],
                  AppConstants.kSpacingItem22,
                ],
              ),
            ),
          ),
        ),
        Positioned(
          top: -80,
          left: 0,
          right: 0,
          child: Container(
            width: cubit.appCubit.isTablet ? 140 : 120.w,
            height: cubit.appCubit.isTablet ? 140 : 120.w,
            decoration: const BoxDecoration(
              color: Color(0xFF03031B),
              shape: BoxShape.circle,
            ),
            child: SvgPicture.asset(
              Assets.images.imgLifetimeIap,
              width: cubit.appCubit.isTablet ? 140 : 120.w,
              height: cubit.appCubit.isTablet ? 140 : 120.w,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        cubit.appCubit.isTablet
            ? const SizedBox(width: 50)
            : const SizedBox.shrink(),
        SvgPicture.asset(
          cubit.isThemeEssential()
              ? Assets.icons.icCheckRadianGreen
              : Assets.icons.icCheckRadian,
          width: cubit.appCubit.isTablet ? 20 : 20.w,
          height: cubit.appCubit.isTablet ? 20 : 20.w,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: CommonText(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: cubit.appCubit.isTablet ? 16 : 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return BlocBuilder<PurchaseLifeTimeCubitOldVer, PurchaseLifeTimeState>(
      buildWhen: (previous, current) =>
          previous.listPackage != current.listPackage,
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CommonText(
                  cubit.getOriginalPriceString(),
                  style: TextStyle(
                    color: context.colorScheme.mainGray,
                    decoration: TextDecoration.lineThrough,
                    decorationColor: context.colorScheme.mainGray,
                    fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GradientText(
                  text: cubit.getCurrentPriceString(),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFF9BBFE),
                      Color(0xFFF9BBFE),
                    ],
                    stops: [0.6, 1.0],
                  ),
                  style: TextStyle(
                    fontSize: cubit.appCubit.isTablet ? 40 : 32.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  void _showPurchaseSuccessDialog(BuildContext context) {
    CommonDialogs.showCongratulationDialog(
      context,
      S.current.payment_successfully,
      S.current.content_payment_successfully,
      S.current.ok,
      () => cubit.popOnPurchaseLifeTimeSuccess(context),
    );
  }
}
