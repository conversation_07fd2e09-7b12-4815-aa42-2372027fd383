import 'package:flutter/material.dart';

/// Manager class for handling menu overlays throughout the app
class HomeOverlayManager {
  // Singleton pattern
  static final HomeOverlayManager _instance = HomeOverlayManager._internal();
  factory HomeOverlayManager() => _instance;
  HomeOverlayManager._internal();

  // Track all active menu overlays
  final List<_MenuOverlayEntry> _activeOverlays = [];

  /// Registers a new menu overlay
  void registerOverlay({
    required OverlayEntry? menuOverlay,
    required OverlayEntry? subMenuOverlay,
    required VoidCallback removeCallback,
  }) {
    // Only register if there are actual overlays to track
    if (menuOverlay == null && subMenuOverlay == null) return;
    
    // Find existing entry and update it, or create new one
    final existingIndex = _activeOverlays.indexWhere(
      (entry) => entry.removeCallback == removeCallback,
    );
    
    if (existingIndex != -1) {
      // Update existing entry
      _activeOverlays[existingIndex] = _MenuOverlayEntry(
        menuOverlay: menuOverlay,
        subMenuOverlay: subMenuOverlay,
        removeCallback: removeCallback,
      );
    } else {
      // Add new entry
      _activeOverlays.add(_MenuOverlayEntry(
        menuOverlay: menuOverlay,
        subMenuOverlay: subMenuOverlay,
        removeCallback: removeCallback,
      ));
    }
  }

  /// Unregisters a specific menu overlay
  void unregisterOverlay(VoidCallback removeCallback) {
    _activeOverlays.removeWhere((entry) => entry.removeCallback == removeCallback);
  }

  /// Closes all active overlays
  void closeAllOverlays() {
    if (_activeOverlays.isEmpty) return;
    
    // Create a copy of the list to avoid modification during iteration
    final overlays = List<_MenuOverlayEntry>.from(_activeOverlays);
    _activeOverlays.clear(); // Clear first to prevent recursive calls
    
    for (var overlay in overlays) {
      overlay.removeCallback();
    }
  }

  /// Checks if any overlay is currently active
  bool get hasActiveOverlays => _activeOverlays.isNotEmpty;
  
  /// Returns the count of active overlays
  int get activeOverlaysCount => _activeOverlays.length;
}

/// Helper class to store overlay entries with their removal callbacks
class _MenuOverlayEntry {
  final OverlayEntry? menuOverlay;
  final OverlayEntry? subMenuOverlay;
  final VoidCallback removeCallback;

  _MenuOverlayEntry({
    required this.menuOverlay,
    required this.subMenuOverlay,
    required this.removeCallback,
  });
}
