// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_audio_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$UploadAudioState {
  CreateNoteOneShotEventUploadAudio get oneShotEvent =>
      throw _privateConstructorUsedError;
  String get audioFilePath => throw _privateConstructorUsedError;
  String get audioFileName => throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $UploadAudioStateCopyWith<UploadAudioState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadAudioStateCopyWith<$Res> {
  factory $UploadAudioStateCopyWith(
          UploadAudioState value, $Res Function(UploadAudioState) then) =
      _$UploadAudioStateCopyWithImpl<$Res, UploadAudioState>;
  @useResult
  $Res call(
      {CreateNoteOneShotEventUploadAudio oneShotEvent,
      String audioFilePath,
      String audioFileName,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class _$UploadAudioStateCopyWithImpl<$Res, $Val extends UploadAudioState>
    implements $UploadAudioStateCopyWith<$Res> {
  _$UploadAudioStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? audioFilePath = null,
    Object? audioFileName = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteOneShotEventUploadAudio,
      audioFilePath: null == audioFilePath
          ? _value.audioFilePath
          : audioFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      audioFileName: null == audioFileName
          ? _value.audioFileName
          : audioFileName // ignore: cast_nullable_to_non_nullable
              as String,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadAudioStateImplCopyWith<$Res>
    implements $UploadAudioStateCopyWith<$Res> {
  factory _$$UploadAudioStateImplCopyWith(_$UploadAudioStateImpl value,
          $Res Function(_$UploadAudioStateImpl) then) =
      __$$UploadAudioStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreateNoteOneShotEventUploadAudio oneShotEvent,
      String audioFilePath,
      String audioFileName,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class __$$UploadAudioStateImplCopyWithImpl<$Res>
    extends _$UploadAudioStateCopyWithImpl<$Res, _$UploadAudioStateImpl>
    implements _$$UploadAudioStateImplCopyWith<$Res> {
  __$$UploadAudioStateImplCopyWithImpl(_$UploadAudioStateImpl _value,
      $Res Function(_$UploadAudioStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? audioFilePath = null,
    Object? audioFileName = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_$UploadAudioStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteOneShotEventUploadAudio,
      audioFilePath: null == audioFilePath
          ? _value.audioFilePath
          : audioFilePath // ignore: cast_nullable_to_non_nullable
              as String,
      audioFileName: null == audioFileName
          ? _value.audioFileName
          : audioFileName // ignore: cast_nullable_to_non_nullable
              as String,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UploadAudioStateImpl implements _UploadAudioState {
  const _$UploadAudioStateImpl(
      {this.oneShotEvent = CreateNoteOneShotEventUploadAudio.none,
      this.audioFilePath = '',
      this.audioFileName = '',
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = ''});

  @override
  @JsonKey()
  final CreateNoteOneShotEventUploadAudio oneShotEvent;
  @override
  @JsonKey()
  final String audioFilePath;
  @override
  @JsonKey()
  final String audioFileName;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;

  @override
  String toString() {
    return 'UploadAudioState(oneShotEvent: $oneShotEvent, audioFilePath: $audioFilePath, audioFileName: $audioFileName, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadAudioStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.audioFilePath, audioFilePath) ||
                other.audioFilePath == audioFilePath) &&
            (identical(other.audioFileName, audioFileName) ||
                other.audioFileName == audioFileName) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions));
  }

  @override
  int get hashCode => Object.hash(runtimeType, oneShotEvent, audioFilePath,
      audioFileName, summaryStyle, writingStyle, additionalInstructions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadAudioStateImplCopyWith<_$UploadAudioStateImpl> get copyWith =>
      __$$UploadAudioStateImplCopyWithImpl<_$UploadAudioStateImpl>(
          this, _$identity);
}

abstract class _UploadAudioState implements UploadAudioState {
  const factory _UploadAudioState(
      {final CreateNoteOneShotEventUploadAudio oneShotEvent,
      final String audioFilePath,
      final String audioFileName,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions}) = _$UploadAudioStateImpl;

  @override
  CreateNoteOneShotEventUploadAudio get oneShotEvent;
  @override
  String get audioFilePath;
  @override
  String get audioFileName;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  @JsonKey(ignore: true)
  _$$UploadAudioStateImplCopyWith<_$UploadAudioStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
