// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NoteDtoImpl _$$NoteDtoImplFromJson(Map<String, dynamic> json) =>
    _$NoteDtoImpl(
      noteId: json['note_id'] as String? ?? '',
      userId: json['user'] as String? ?? '',
      title: json['title'] as String? ?? '',
      subTitle: json['sub_title'] as String? ?? '',
      hashtags: (json['hashtags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      summary: json['summary'] as String? ?? '',
      transcript: json['transcript'] as String? ?? '',
      type: json['type'] as String? ?? '',
      youtubeUrl: json['youtube_url'] as String? ?? '',
      youtubeId: json['youtube_id'] as String? ?? '',
      quiz: (json['quiz'] as List<dynamic>?)
              ?.map((e) => QuizDetailDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      flashCards: (json['flashcards'] as List<dynamic>?)
              ?.map(
                  (e) => FlashCarDetailDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      userName: json['user_name'] as String? ?? '',
      views: json['views'] as String? ?? '',
      transcriptJson: (json['transcript_json'] as List<dynamic>?)
              ?.map((e) => TranscriptDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      audioUrl: json['audio_url'] as String? ?? '',
      folderId: json['folder_id'] as String? ?? '',
      mindMap: json['mindmap'] as String? ?? '',
      createdAt: json['createdAt'] as String? ?? '',
      duration: (json['duration'] as num?)?.toDouble() ?? 0.0,
      documentUrl: json['document_url'] as String? ?? '',
      isFeedbackSubmitted: json['is_feedback_submitted'] as bool? ?? false,
      isInsufficientContent: json['is_insufficient_content'] as bool? ?? false,
      clipDurations: (json['clip_durations'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      voices: (json['voices'] as List<dynamic>?)
              ?.map((e) => VoiceDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      webUrl: json['web_url'] as String? ?? '',
      isPublic: json['is_public'] as bool? ?? false,
      isPasswordProtected: json['is_password_protected'] as bool? ?? false,
      sharePassword: json['share_password'] as String? ?? '',
      shareLink: json['share_link'] as String? ?? '',
      suggestions: (json['suggestions'] as List<dynamic>?)
              ?.map((e) => SuggestionDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      quizSets: (json['quiz_sets'] as List<dynamic>?)
              ?.map((e) => QuizSetsDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      flashcardSets: (json['flashcard_sets'] as List<dynamic>?)
              ?.map((e) => FlashcardSetsDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      flashcardSetData: json['flashcard_set_data'] == null
          ? null
          : FlashcardSetDataDto.fromJson(
              json['flashcard_set_data'] as Map<String, dynamic>),
      quizSetDataDto: json['quiz_set_data'] == null
          ? null
          : QuizSetDataDto.fromJson(
              json['quiz_set_data'] as Map<String, dynamic>),
      saveToNotes: (json['save_to_notes'] as List<dynamic>?)
              ?.map((e) => ChatMessageDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      slideUrl: json['slide_url'] as String? ?? '',
      slidePdfUrl: json['slide_pdf_url'] as String? ?? '',
      chatTemplateSuggestions: (json['chat_template_suggestions']
                  as List<dynamic>?)
              ?.map((e) => ChatTemplateDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$NoteDtoImplToJson(_$NoteDtoImpl instance) =>
    <String, dynamic>{
      'note_id': instance.noteId,
      'user': instance.userId,
      'title': instance.title,
      'sub_title': instance.subTitle,
      'hashtags': instance.hashtags,
      'summary': instance.summary,
      'transcript': instance.transcript,
      'type': instance.type,
      'youtube_url': instance.youtubeUrl,
      'youtube_id': instance.youtubeId,
      'quiz': instance.quiz,
      'flashcards': instance.flashCards,
      'user_name': instance.userName,
      'views': instance.views,
      'transcript_json': instance.transcriptJson,
      'audio_url': instance.audioUrl,
      'folder_id': instance.folderId,
      'mindmap': instance.mindMap,
      'createdAt': instance.createdAt,
      'duration': instance.duration,
      'document_url': instance.documentUrl,
      'is_feedback_submitted': instance.isFeedbackSubmitted,
      'is_insufficient_content': instance.isInsufficientContent,
      'clip_durations': instance.clipDurations,
      'voices': instance.voices,
      'web_url': instance.webUrl,
      'is_public': instance.isPublic,
      'is_password_protected': instance.isPasswordProtected,
      'share_password': instance.sharePassword,
      'share_link': instance.shareLink,
      'suggestions': instance.suggestions,
      'quiz_sets': instance.quizSets,
      'flashcard_sets': instance.flashcardSets,
      'flashcard_set_data': instance.flashcardSetData,
      'quiz_set_data': instance.quizSetDataDto,
      'save_to_notes': instance.saveToNotes,
      'slide_url': instance.slideUrl,
      'slide_pdf_url': instance.slidePdfUrl,
      'chat_template_suggestions': instance.chatTemplateSuggestions,
    };
