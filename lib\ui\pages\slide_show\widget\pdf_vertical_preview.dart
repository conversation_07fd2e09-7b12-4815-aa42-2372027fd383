import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pdfx/pdfx.dart';

class PdfVerticalPreview extends StatefulWidget {
  final String pdfPath;
  final Function(int) onPageChanged;
  final ScrollController scrollController;

  const PdfVerticalPreview({
    Key? key,
    required this.pdfPath,
    required this.onPageChanged,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<PdfVerticalPreview> createState() => _PdfVerticalPreviewState();
}

class _PdfVerticalPreviewState extends State<PdfVerticalPreview> {
  List<PdfPageImage?> _images = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadAllPages();
  }

  Future<void> _loadAllPages() async {
    final doc = await PdfDocument.openFile(widget.pdfPath);
    final images = <PdfPageImage?>[];
    for (int i = 1; i <= doc.pagesCount; i++) {
      final page = await doc.getPage(i);
      final image = await page.render(
        width: page.width * 2,
        height: page.height * 2,
        format: PdfPageImageFormat.png,
        backgroundColor: '#FFFFFF',
      );
      await page.close();
      images.add(image);
    }
    if (!mounted) return;
    setState(() {
      _images = images;
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CupertinoActivityIndicator());
    }
    return InteractiveViewer(
      minScale: 1,
      maxScale: 5.0,
      child: ListView.builder(
        padding: EdgeInsets.only(bottom: 80.h),
        controller: widget.scrollController,
        itemCount: _images.length,
        itemBuilder: (context, index) {
          final image = _images[index];
          if (image == null) {
            return const Center(child: Text('Failed to load page'));
          }
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60),
            ),
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.memory(image.bytes),
            ),
          );
        },
      ),
    );
  }
}
