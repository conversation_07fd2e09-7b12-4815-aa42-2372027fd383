import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

class CommonDialogsOldVer {
  static bool _isDialogLoadingOpened = false;

  static BuildContext? get _context =>
      GetIt.instance.get<NavigationService>().context;

  static bool get isLoadingDialogOpen => _isDialogLoadingOpened;

  static void closeLoading() {
    if (CommonDialogsOldVer._isDialogLoadingOpened) {
      CommonDialogsOldVer._isDialogLoadingOpened = false;
      Navigator.of(_context!).pop();
    }
  }

  static Future<dynamic> buildConfirmDialogOldVer(
    BuildContext context, {
    required String title,
    required String content,
    Color? colorButtonYes,
    required Function()? onPressedYesButton,
    String? headerImageAssetFile,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final dialogWidthTablet = MediaQuery.of(context).size.width * 0.5;
        final dialogWidth = MediaQuery.of(context).size.width;
        return Dialog(
          backgroundColor: context.colorScheme.mainNeutral,
          child: Container(
            width: context.isTablet ? dialogWidthTablet : dialogWidth,
            constraints: BoxConstraints(
              maxWidth: context.isTablet ? dialogWidthTablet : dialogWidth,
            ),
            child: Padding(
              padding: EdgeInsets.all(context.isTablet ? 48 : 24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    headerImageAssetFile ?? Assets.icons.icTagSave,
                    width: 105,
                    height: 93,
                  ),
                  context.isTablet
                      ? AppConstants.kSpacingItem12
                      : AppConstants.kSpacingItem24,
                  CommonText(
                    title,
                    style: TextStyle(fontSize: context.isTablet ? 20 : 16.sp),
                    textColor: context.colorScheme.mainPrimary,
                    appFontWeight: AppFontWeight.semiBold,
                  ),
                  context.isTablet
                      ? AppConstants.kSpacingItem8
                      : AppConstants.kSpacingItem16,
                  CommonText(
                    content,
                    style: TextStyle(fontSize: context.isTablet ? 16 : 13.sp),
                    textColor: context.colorScheme.mainGray,
                    appFontWeight: AppFontWeight.regular,
                    textAlign: TextAlign.center,
                  ),
                  AppConstants.kSpacingItem24,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: AppCommonButton(
                          height: context.isLandscape ? 56.h : 36.h,
                          borderRadius: BorderRadius.circular(10.r),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          textWidget: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              S.current.cancel,
                              style: TextStyle(
                                  fontSize: context.isTablet ? 18 : 14.sp,
                                  fontWeight: FontWeight.w500),
                            ),
                          ),
                          border:
                              Border.all(color: context.colorScheme.themeWhite),
                        ),
                      ),
                      AppConstants.kSpacingItemW8,
                      Expanded(
                        child: AppCommonButton(
                          height: context.isLandscape ? 56.h : 36.h,
                          borderRadius: BorderRadius.circular(10.r),
                          onPressed: () {
                            Navigator.of(context).pop();
                            onPressedYesButton?.call();
                          },
                          textWidget: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              S.current.yes,
                              style: TextStyle(
                                fontSize: context.isTablet ? 18 : 14.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          backgroundColor:
                              colorButtonYes ?? context.colorScheme.mainBlue,
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static Future<dynamic> showInfoDialogOldVer(
    BuildContext context, {
    String? headerImageAssetFile,
    String? mainButtonTitle,
    String? subButtonTitle,
    required String title,
    required String content,
    Func0? onPressMainButton,
    Func0? onPressSubButton,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final dialogWidthTablet = MediaQuery.of(context).size.width * 0.5;
        final dialogWidth = MediaQuery.of(context).size.width;
        return Dialog(
          child: Container(
            width: context.isTablet ? dialogWidthTablet : dialogWidth,
            constraints: BoxConstraints(
              maxWidth: context.isTablet ? dialogWidthTablet : dialogWidth,
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    headerImageAssetFile ??
                        Assets.icons.icHeaderImageInfoDialog,
                    width: 83,
                    height: 93,
                  ),
                  context.isTablet
                      ? AppConstants.kSpacingItem12
                      : AppConstants.kSpacingItem24,
                  CommonText(
                    title,
                    style: TextStyle(fontSize: context.isTablet ? 20 : 16.sp),
                    textColor: context.colorScheme.mainPrimary,
                    appFontWeight: AppFontWeight.semiBold,
                  ),
                  context.isTablet
                      ? AppConstants.kSpacingItem8
                      : AppConstants.kSpacingItem16,
                  CommonText(
                    content,
                    style: TextStyle(fontSize: context.isTablet ? 16 : 13.sp),
                    textColor: context.colorScheme.mainGray,
                    appFontWeight: AppFontWeight.regular,
                    textAlign: TextAlign.center,
                  ),
                  AppConstants.kSpacingItem24,
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppCommonButton(
                        height: context.isTablet ? 48 : 36.h,
                        width: context.isTablet
                            ? MediaQuery.of(context).size.width * 0.35
                            : double.infinity,
                        borderRadius: BorderRadius.circular(10.r),
                        backgroundColor: context.colorScheme.mainBlue,
                        onPressed: () {
                          Navigator.pop(context);
                          onPressMainButton?.call();
                        },
                        textWidget: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Text(
                            mainButtonTitle ?? S.current.got_it,
                            style: TextStyle(
                              fontSize: context.isTablet ? 22 : 14.sp,
                            ),
                          ),
                        ),
                      ),
                      AppConstants.kSpacingItem8,
                      subButtonTitle != null
                          ? GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                                onPressSubButton?.call();
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: CommonText(
                                  textColor: context.colorScheme.mainGray,
                                  subButtonTitle,
                                  style: TextStyle(
                                      fontSize: context.isTablet ? 22 : 14.sp),
                                ),
                              ),
                            )
                          : const SizedBox.shrink()
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static Future<dynamic> showCongratulationDialogOldVer(
      BuildContext context,
      String title,
      String content,
      String textButton,
      VoidCallback actionButton) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          final dialogWidthTablet = MediaQuery.of(context).size.width * 0.5;
          final dialogWidth = MediaQuery.of(context).size.width;
          return Dialog(
            insetPadding:
                EdgeInsets.symmetric(horizontal: context.isTablet ? 140 : 36),
            child: Container(
              width: context.isTablet ? dialogWidthTablet : dialogWidth,
              constraints: BoxConstraints(
                maxWidth: context.isTablet ? dialogWidthTablet : dialogWidth,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: context.colorScheme.mainNeutral,
              ),
              child: Padding(
                padding: EdgeInsets.all(context.isTablet ? 48 : 24.0),
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Center(
                            child: SvgPicture.asset(
                              Assets.icons.icWelcomeGift,
                              width: context.isTablet ? 36 : 24.w,
                              height: context.isTablet ? 36 : 24.h,
                            ),
                          ),
                          AppConstants.kSpacingItemW8,
                          Center(
                            child: CommonText(
                              title,
                              style: TextStyle(
                                fontSize: context.isTablet ? 22 : 18.sp,
                              ),
                              appFontWeight: AppFontWeight.bold,
                              textColor: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                      AppConstants.kSpacingItem8,
                      CommonText(
                        content,
                        style: TextStyle(
                          fontSize: context.isTablet ? 15 : 10.sp,
                        ),
                        textColor: AppColors.white,
                        textAlign: TextAlign.center,
                      ),
                      AppConstants.kSpacingItem16,
                      CommonButton(
                        onPress: () {
                          Navigator.pop(context);
                          actionButton();
                        },
                        radiusSize: 10.r,
                        backgroundColor: context.colorScheme.mainBlue,
                        height: context.isLandscape ? 56.h : 36.h,
                        minWidth: MediaQuery.of(context).size.width,
                        btnText: textButton,
                        btnTextStyle: TextStyle(
                          fontSize: context.isTablet ? 11.sp : 14.sp,
                          color: AppColors.white,
                          fontWeight: context.isLandscape
                              ? FontWeight.w500
                              : FontWeight.w600,
                        ),
                      ),
                    ]),
              ),
            ),
          );
        });
  }

  static void showErrorDialogOldVer(
    BuildContext context, {
    required String title,
    String contentButton = 'OK',
    String? content,
    Func0? onPressed,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final dialogWidthTablet = MediaQuery.of(context).size.width * 0.5;
        final dialogWidth = MediaQuery.of(context).size.width;
        return Dialog(
          backgroundColor: context.colorScheme.mainNeutral,
          child: Container(
            width: context.isTablet ? dialogWidthTablet : dialogWidth,
            constraints: BoxConstraints(
              maxWidth: context.isTablet ? dialogWidthTablet : dialogWidth,
            ),
            child: Padding(
              padding: EdgeInsets.all(context.isTablet ? 48 : 32.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    Assets.icons.icError,
                    width: 124,
                    height: 68,
                  ),
                  context.isTablet
                      ? AppConstants.kSpacingItem12
                      : AppConstants.kSpacingItem24,
                  CommonText(
                    title,
                    style: TextStyle(fontSize: context.isTablet ? 18 : 16.sp),
                    appFontWeight: AppFontWeight.regular,
                    textColor: context.colorScheme.mainGray,
                    textAlign: TextAlign.center,
                  ),
                  content != null
                      ? CommonText(
                          content,
                          style: TextStyle(
                            fontSize: context.isTablet ? 16 : 13.sp,
                          ),
                          appFontWeight: AppFontWeight.regular,
                          textColor: context.colorScheme.mainGray,
                          textAlign: TextAlign.center,
                        )
                      : const SizedBox.shrink(),
                  AppConstants.kSpacingItem24,
                  AppCommonButton(
                    backgroundColor: context.colorScheme.mainBlue,
                    height: context.isLandscape ? 56.h : 36.h,
                    width: context.isTablet
                        ? MediaQuery.of(context).size.width * 0.35
                        : double.infinity,
                    borderRadius: BorderRadius.circular(10.r),
                    textWidget: Text(
                      contentButton,
                      style: TextStyle(
                        fontSize: context.isTablet ? 22 : 14.sp,
                        fontWeight: FontWeight.w500,
                        color: context.colorScheme.mainPrimary,
                      ),
                      maxLines: 1,
                      textAlign: TextAlign.center,
                    ),
                    onPressed: () =>
                        {Navigator.pop(context), onPressed?.call()},
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
