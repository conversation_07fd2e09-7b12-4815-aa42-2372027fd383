import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/base/base_state.dart';
import 'package:note_x/models/slide_teamplate/slide_template_dto.dart';

part 'slide_show_create_cubit_state.freezed.dart';

@freezed
class SlideShowCreateCubitState extends BaseState
    with _$SlideShowCreateCubitState {
  const factory SlideShowCreateCubitState({
    @Default('auto') String cardCount,
    @Default(false) bool isAdvancedMode,
    @Default(-1) int selectedTemplateIndex,
    @Default([]) List<SlideTemplateDto> slideTemplates,
  }) = _SlideShowCreateCubitState;
}
