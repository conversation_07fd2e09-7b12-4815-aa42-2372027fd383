// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcript_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TranscriptModelAdapter extends TypeAdapter<TranscriptModel> {
  @override
  final int typeId = 4;

  @override
  TranscriptModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TranscriptModel(
      text: fields[0] == null ? '' : fields[0] as String,
      start: fields[1] == null ? 0.0 : fields[1] as double,
      duration: fields[2] == null ? 0.0 : fields[2] as double,
      speaker: fields[3] == null ? '' : fields[3] as String,
    );
  }

  @override
  void write(BinaryWriter writer, TranscriptModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.text)
      ..writeByte(1)
      ..write(obj.start)
      ..writeByte(2)
      ..write(obj.duration)
      ..writeByte(3)
      ..write(obj.speaker);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TranscriptModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
