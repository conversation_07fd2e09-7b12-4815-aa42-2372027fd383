// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_data_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseDataDto<T> _$BaseDataDtoFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseDataDto<T>(
      status: json['status'] as String?,
      message: json['message'] as String?,
      data: fromJsonT(json['data']),
    );

Map<String, dynamic> _$BaseDataDtoToJson<T>(
  BaseDataDto<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': toJsonT(instance.data),
    };
