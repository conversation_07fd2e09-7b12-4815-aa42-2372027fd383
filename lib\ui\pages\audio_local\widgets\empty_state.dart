import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:note_x/lib.dart';

class EmptyState extends StatelessWidget {
  const EmptyState({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      hasScrollBody: false,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                CupertinoIcons.waveform_circle,
                size: 64,
                color: context.colorScheme.mainGray,
              ),
              AppConstants.kSpacingItem16,
              Text(
                S.current.no_recordings,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: context.colorScheme.mainGray,
                ),
              ),
              AppConstants.kSpacingItem4,
            ],
          ),
        ),
      ),
    );
  }
}
