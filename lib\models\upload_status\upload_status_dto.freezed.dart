// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_status_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

UploadStatusDto _$UploadStatusDtoFromJson(Map<String, dynamic> json) {
  return _UploadStatusDto.fromJson(json);
}

/// @nodoc
mixin _$UploadStatusDto {
  @JsonKey(name: 'upload_id')
  String get uploadId => throw _privateConstructorUsedError;
  @JsonKey(name: 'file_type')
  String get fileType => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_chunks')
  int get totalChunks => throw _privateConstructorUsedError;
  @JsonKey(name: 'uploaded_chunks')
  int get uploadedChunks => throw _privateConstructorUsedError;
  @JsonKey(name: 'uploaded_chunk_numbers')
  List<int> get uploadedChunkNumbers => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_completed')
  bool get isCompleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  double get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'missing_chunks')
  List<int> get missingChunks => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UploadStatusDtoCopyWith<UploadStatusDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadStatusDtoCopyWith<$Res> {
  factory $UploadStatusDtoCopyWith(
          UploadStatusDto value, $Res Function(UploadStatusDto) then) =
      _$UploadStatusDtoCopyWithImpl<$Res, UploadStatusDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'upload_id') String uploadId,
      @JsonKey(name: 'file_type') String fileType,
      @JsonKey(name: 'total_chunks') int totalChunks,
      @JsonKey(name: 'uploaded_chunks') int uploadedChunks,
      @JsonKey(name: 'uploaded_chunk_numbers') List<int> uploadedChunkNumbers,
      @JsonKey(name: 'is_completed') bool isCompleted,
      @JsonKey(name: 'created_at') double createdAt,
      @JsonKey(name: 'missing_chunks') List<int> missingChunks});
}

/// @nodoc
class _$UploadStatusDtoCopyWithImpl<$Res, $Val extends UploadStatusDto>
    implements $UploadStatusDtoCopyWith<$Res> {
  _$UploadStatusDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uploadId = null,
    Object? fileType = null,
    Object? totalChunks = null,
    Object? uploadedChunks = null,
    Object? uploadedChunkNumbers = null,
    Object? isCompleted = null,
    Object? createdAt = null,
    Object? missingChunks = null,
  }) {
    return _then(_value.copyWith(
      uploadId: null == uploadId
          ? _value.uploadId
          : uploadId // ignore: cast_nullable_to_non_nullable
              as String,
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String,
      totalChunks: null == totalChunks
          ? _value.totalChunks
          : totalChunks // ignore: cast_nullable_to_non_nullable
              as int,
      uploadedChunks: null == uploadedChunks
          ? _value.uploadedChunks
          : uploadedChunks // ignore: cast_nullable_to_non_nullable
              as int,
      uploadedChunkNumbers: null == uploadedChunkNumbers
          ? _value.uploadedChunkNumbers
          : uploadedChunkNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double,
      missingChunks: null == missingChunks
          ? _value.missingChunks
          : missingChunks // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadStatusDtoImplCopyWith<$Res>
    implements $UploadStatusDtoCopyWith<$Res> {
  factory _$$UploadStatusDtoImplCopyWith(_$UploadStatusDtoImpl value,
          $Res Function(_$UploadStatusDtoImpl) then) =
      __$$UploadStatusDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'upload_id') String uploadId,
      @JsonKey(name: 'file_type') String fileType,
      @JsonKey(name: 'total_chunks') int totalChunks,
      @JsonKey(name: 'uploaded_chunks') int uploadedChunks,
      @JsonKey(name: 'uploaded_chunk_numbers') List<int> uploadedChunkNumbers,
      @JsonKey(name: 'is_completed') bool isCompleted,
      @JsonKey(name: 'created_at') double createdAt,
      @JsonKey(name: 'missing_chunks') List<int> missingChunks});
}

/// @nodoc
class __$$UploadStatusDtoImplCopyWithImpl<$Res>
    extends _$UploadStatusDtoCopyWithImpl<$Res, _$UploadStatusDtoImpl>
    implements _$$UploadStatusDtoImplCopyWith<$Res> {
  __$$UploadStatusDtoImplCopyWithImpl(
      _$UploadStatusDtoImpl _value, $Res Function(_$UploadStatusDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uploadId = null,
    Object? fileType = null,
    Object? totalChunks = null,
    Object? uploadedChunks = null,
    Object? uploadedChunkNumbers = null,
    Object? isCompleted = null,
    Object? createdAt = null,
    Object? missingChunks = null,
  }) {
    return _then(_$UploadStatusDtoImpl(
      uploadId: null == uploadId
          ? _value.uploadId
          : uploadId // ignore: cast_nullable_to_non_nullable
              as String,
      fileType: null == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String,
      totalChunks: null == totalChunks
          ? _value.totalChunks
          : totalChunks // ignore: cast_nullable_to_non_nullable
              as int,
      uploadedChunks: null == uploadedChunks
          ? _value.uploadedChunks
          : uploadedChunks // ignore: cast_nullable_to_non_nullable
              as int,
      uploadedChunkNumbers: null == uploadedChunkNumbers
          ? _value._uploadedChunkNumbers
          : uploadedChunkNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as double,
      missingChunks: null == missingChunks
          ? _value._missingChunks
          : missingChunks // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UploadStatusDtoImpl implements _UploadStatusDto {
  const _$UploadStatusDtoImpl(
      {@JsonKey(name: 'upload_id') this.uploadId = '',
      @JsonKey(name: 'file_type') this.fileType = '',
      @JsonKey(name: 'total_chunks') this.totalChunks = 0,
      @JsonKey(name: 'uploaded_chunks') this.uploadedChunks = 0,
      @JsonKey(name: 'uploaded_chunk_numbers')
      final List<int> uploadedChunkNumbers = const [],
      @JsonKey(name: 'is_completed') this.isCompleted = false,
      @JsonKey(name: 'created_at') this.createdAt = 0.0,
      @JsonKey(name: 'missing_chunks')
      final List<int> missingChunks = const []})
      : _uploadedChunkNumbers = uploadedChunkNumbers,
        _missingChunks = missingChunks;

  factory _$UploadStatusDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$UploadStatusDtoImplFromJson(json);

  @override
  @JsonKey(name: 'upload_id')
  final String uploadId;
  @override
  @JsonKey(name: 'file_type')
  final String fileType;
  @override
  @JsonKey(name: 'total_chunks')
  final int totalChunks;
  @override
  @JsonKey(name: 'uploaded_chunks')
  final int uploadedChunks;
  final List<int> _uploadedChunkNumbers;
  @override
  @JsonKey(name: 'uploaded_chunk_numbers')
  List<int> get uploadedChunkNumbers {
    if (_uploadedChunkNumbers is EqualUnmodifiableListView)
      return _uploadedChunkNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_uploadedChunkNumbers);
  }

  @override
  @JsonKey(name: 'is_completed')
  final bool isCompleted;
  @override
  @JsonKey(name: 'created_at')
  final double createdAt;
  final List<int> _missingChunks;
  @override
  @JsonKey(name: 'missing_chunks')
  List<int> get missingChunks {
    if (_missingChunks is EqualUnmodifiableListView) return _missingChunks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_missingChunks);
  }

  @override
  String toString() {
    return 'UploadStatusDto(uploadId: $uploadId, fileType: $fileType, totalChunks: $totalChunks, uploadedChunks: $uploadedChunks, uploadedChunkNumbers: $uploadedChunkNumbers, isCompleted: $isCompleted, createdAt: $createdAt, missingChunks: $missingChunks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadStatusDtoImpl &&
            (identical(other.uploadId, uploadId) ||
                other.uploadId == uploadId) &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.totalChunks, totalChunks) ||
                other.totalChunks == totalChunks) &&
            (identical(other.uploadedChunks, uploadedChunks) ||
                other.uploadedChunks == uploadedChunks) &&
            const DeepCollectionEquality()
                .equals(other._uploadedChunkNumbers, _uploadedChunkNumbers) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            const DeepCollectionEquality()
                .equals(other._missingChunks, _missingChunks));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      uploadId,
      fileType,
      totalChunks,
      uploadedChunks,
      const DeepCollectionEquality().hash(_uploadedChunkNumbers),
      isCompleted,
      createdAt,
      const DeepCollectionEquality().hash(_missingChunks));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadStatusDtoImplCopyWith<_$UploadStatusDtoImpl> get copyWith =>
      __$$UploadStatusDtoImplCopyWithImpl<_$UploadStatusDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UploadStatusDtoImplToJson(
      this,
    );
  }
}

abstract class _UploadStatusDto implements UploadStatusDto {
  const factory _UploadStatusDto(
          {@JsonKey(name: 'upload_id') final String uploadId,
          @JsonKey(name: 'file_type') final String fileType,
          @JsonKey(name: 'total_chunks') final int totalChunks,
          @JsonKey(name: 'uploaded_chunks') final int uploadedChunks,
          @JsonKey(name: 'uploaded_chunk_numbers')
          final List<int> uploadedChunkNumbers,
          @JsonKey(name: 'is_completed') final bool isCompleted,
          @JsonKey(name: 'created_at') final double createdAt,
          @JsonKey(name: 'missing_chunks') final List<int> missingChunks}) =
      _$UploadStatusDtoImpl;

  factory _UploadStatusDto.fromJson(Map<String, dynamic> json) =
      _$UploadStatusDtoImpl.fromJson;

  @override
  @JsonKey(name: 'upload_id')
  String get uploadId;
  @override
  @JsonKey(name: 'file_type')
  String get fileType;
  @override
  @JsonKey(name: 'total_chunks')
  int get totalChunks;
  @override
  @JsonKey(name: 'uploaded_chunks')
  int get uploadedChunks;
  @override
  @JsonKey(name: 'uploaded_chunk_numbers')
  List<int> get uploadedChunkNumbers;
  @override
  @JsonKey(name: 'is_completed')
  bool get isCompleted;
  @override
  @JsonKey(name: 'created_at')
  double get createdAt;
  @override
  @JsonKey(name: 'missing_chunks')
  List<int> get missingChunks;
  @override
  @JsonKey(ignore: true)
  _$$UploadStatusDtoImplCopyWith<_$UploadStatusDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
