import 'package:note_x/lib.dart';

class HiveSlideShowService {
  static Future<void> saveSlideShowInNote({
    required String noteId,
    required NoteModel newNote,
  }) async {
    final noteBox = HiveService().noteBox;
    final note = noteBox.get(noteId);
    if (note != null) {
      final updatedNote = note.copyWith(
        slideUrl: newNote.slideUrl,
        slidePdfUrl: newNote.slidePdfUrl,
        isGeneratingSlide: false,
      );
      await noteBox.put(noteId, updatedNote);
    }
  }
}
