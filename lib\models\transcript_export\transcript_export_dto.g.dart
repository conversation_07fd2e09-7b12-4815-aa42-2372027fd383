// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcript_export_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TranscriptExportDtoImpl _$$TranscriptExportDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$TranscriptExportDtoImpl(
      url: json['url'] as String?,
      noteId: json['note_id'] as String?,
    );

Map<String, dynamic> _$$TranscriptExportDtoImplToJson(
        _$TranscriptExportDtoImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'note_id': instance.noteId,
    };
