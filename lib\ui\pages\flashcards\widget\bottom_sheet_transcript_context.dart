import 'dart:io';
import 'package:flutter/material.dart';
import 'package:note_x/lib.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'dart:ui' as ui;

void showModalBottomSheetTranscriptContext(
  BuildContext context,
  String audioFilePath,
  String audioUrl,
  String contextTrans,
  bool isCommunityNote,
) {
  showMaterialModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    backgroundColor: context.colorScheme.mainNeutral,
    enableDrag: true,
    isDismissible: true,
    builder: (context) {
      return DraggableScrollableSheet(
        initialChildSize: _calculateInitialChildSize(context, contextTrans),
        minChildSize: 0.3,
        maxChildSize: 0.9,
        expand: false,
        builder: (BuildContext context, ScrollController scrollController) {
          return SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  width: 40.w,
                  height: 4.h,
                  margin: EdgeInsets.symmetric(vertical: 8.h),
                  decoration: BoxDecoration(
                    color: context.colorScheme.mainGray,
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
                CommonText(
                  S.current.transcript_context,
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: context.isTablet ? 16 : 14.sp,
                    color: context.colorScheme.mainPrimary,
                  ),
                  textAlign: TextAlign.left,
                ),
                // Hiển thị context
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 24.h,
                  ),
                  child: Directionality(
                    textDirection: RtlUtils.getTextDirectionForContent(contextTrans),
                    child: CommonText(
                      contextTrans,
                      style: TextStyle(
                        fontSize: context.isTablet ? 16 : 14.sp,
                        color: context.colorScheme.mainPrimary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

double _calculateInitialChildSize(BuildContext context, String contextTrans) {
  // Fixed heights
  double titleHeight =
      24.h + 19.sp + 24.h; // Title padding + font size + bottom padding
  double bottomPadding = 4.h;

  // Calculate total content height
  double contentHeight = titleHeight;

  // Estimate text height for the context
  final textStyle =
      TextStyle(fontSize: 15.sp); // Adjust this to match your text style
  final maxWidth = MediaQuery.of(context).size.width -
      40.w -
      62.w; // Subtracting horizontal padding

  // Tính chiều cao của context
  contentHeight += _estimateTextHeight(contextTrans, textStyle, maxWidth) +
      32.h; // Thêm chiều cao của context

  contentHeight += bottomPadding;

  // Get screen height
  double screenHeight = MediaQuery.of(context).size.height;

  // Calculate the ratio
  double ratio = contentHeight / screenHeight;

  // Clamp the ratio between 0.3 and 0.9
  return ratio.clamp(0.3, 0.9);
}

double _estimateTextHeight(String text, TextStyle style, double maxWidth) {
  final textPainter = TextPainter(
    text: TextSpan(text: text, style: style),
    maxLines: null,
    textDirection: ui.TextDirection.ltr,
  )..layout(maxWidth: maxWidth);

  return textPainter.height;
}

Widget buildAudioPlayerContainer(BuildContext context, String audioFilePath,
    String audioUrl, bool isCommunityNote) {
  return Container(
    padding: EdgeInsets.only(
      top: 10.h,
      bottom: Platform.isAndroid
          ? MediaQuery.of(context).viewPadding.bottom + 10.h
          : 20.h,
      left: 20.w,
      right: 20.w,
    ),
    decoration: BoxDecoration(
      color: context.colorScheme.mainNeutral,
      borderRadius: BorderRadius.vertical(top: Radius.circular(10.r)),
    ),
    child: SizedBox(
      height: 48.h,
      child: AudioPlayerWidget(
        isCommunityNote: isCommunityNote,
        audioFilePath: audioFilePath,
        audioUrl: audioUrl.isNotEmpty ? audioUrl : null,
        backgroundColor: context.colorScheme.mainNeutral,
      ),
    ),
  );
}
