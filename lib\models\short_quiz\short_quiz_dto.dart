// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
part 'short_quiz_dto.freezed.dart';
part 'short_quiz_dto.g.dart';

@freezed
class ShortQuizDto with _$ShortQuizDto {
  const factory ShortQuizDto({
    @J<PERSON><PERSON>ey(name: 'note_id') @Default('') String noteId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'quiz_video_url') @Default('') String quizVideoUrl,
    @<PERSON><PERSON><PERSON>ey(name: 'task_id') @Default('') String taskId,
    @<PERSON><PERSON><PERSON>ey(name: 'user_id') @Default('') String userId,
    @<PERSON><PERSON><PERSON>ey(name: 'redis_data') @Default('') String redisData,
    @Default(0) int duration,
  }) = _ShortQuizDto;

  factory ShortQuizDto.fromJson(Map<String, dynamic> json) =>
      _$ShortQuizDtoFromJson(json);
}
