// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_set_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ExportSetDto _$ExportSetDtoFromJson(Map<String, dynamic> json) {
  return _ExportSetDto.fromJson(json);
}

/// @nodoc
mixin _$ExportSetDto {
  @JsonKey(name: 'url')
  String? get url => throw _privateConstructorUsedError;
  @JsonKey(name: 'set_id')
  String? get setId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportSetDtoCopyWith<ExportSetDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportSetDtoCopyWith<$Res> {
  factory $ExportSetDtoCopyWith(
          ExportSetDto value, $Res Function(ExportSetDto) then) =
      _$ExportSetDtoCopyWithImpl<$Res, ExportSetDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'set_id') String? setId});
}

/// @nodoc
class _$ExportSetDtoCopyWithImpl<$Res, $Val extends ExportSetDto>
    implements $ExportSetDtoCopyWith<$Res> {
  _$ExportSetDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? setId = freezed,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      setId: freezed == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportSetDtoImplCopyWith<$Res>
    implements $ExportSetDtoCopyWith<$Res> {
  factory _$$ExportSetDtoImplCopyWith(
          _$ExportSetDtoImpl value, $Res Function(_$ExportSetDtoImpl) then) =
      __$$ExportSetDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'set_id') String? setId});
}

/// @nodoc
class __$$ExportSetDtoImplCopyWithImpl<$Res>
    extends _$ExportSetDtoCopyWithImpl<$Res, _$ExportSetDtoImpl>
    implements _$$ExportSetDtoImplCopyWith<$Res> {
  __$$ExportSetDtoImplCopyWithImpl(
      _$ExportSetDtoImpl _value, $Res Function(_$ExportSetDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? setId = freezed,
  }) {
    return _then(_$ExportSetDtoImpl(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      setId: freezed == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportSetDtoImpl implements _ExportSetDto {
  const _$ExportSetDtoImpl(
      {@JsonKey(name: 'url') this.url, @JsonKey(name: 'set_id') this.setId});

  factory _$ExportSetDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportSetDtoImplFromJson(json);

  @override
  @JsonKey(name: 'url')
  final String? url;
  @override
  @JsonKey(name: 'set_id')
  final String? setId;

  @override
  String toString() {
    return 'ExportSetDto(url: $url, setId: $setId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportSetDtoImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.setId, setId) || other.setId == setId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url, setId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportSetDtoImplCopyWith<_$ExportSetDtoImpl> get copyWith =>
      __$$ExportSetDtoImplCopyWithImpl<_$ExportSetDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportSetDtoImplToJson(
      this,
    );
  }
}

abstract class _ExportSetDto implements ExportSetDto {
  const factory _ExportSetDto(
      {@JsonKey(name: 'url') final String? url,
      @JsonKey(name: 'set_id') final String? setId}) = _$ExportSetDtoImpl;

  factory _ExportSetDto.fromJson(Map<String, dynamic> json) =
      _$ExportSetDtoImpl.fromJson;

  @override
  @JsonKey(name: 'url')
  String? get url;
  @override
  @JsonKey(name: 'set_id')
  String? get setId;
  @override
  @JsonKey(ignore: true)
  _$$ExportSetDtoImplCopyWith<_$ExportSetDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
