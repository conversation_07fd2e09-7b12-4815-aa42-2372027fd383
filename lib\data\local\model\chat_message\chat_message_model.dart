import 'package:hive/hive.dart';

import '../../../../services/hive/hive_type_id.dart';
part 'chat_message_model.g.dart';

@HiveType(typeId: HiveTypeId.chatMessageTypeId)
class ChatMessageModel extends HiveObject {
  @HiveField(0, defaultValue: "")
  String id;

  @HiveField(1, defaultValue: "")
  String noteId;

  @HiveField(2, defaultValue: "")
  String userId;

  @HiveField(3, defaultValue: "")
  String title;

  @HiveField(4, defaultValue: "")
  String answer;

  @HiveField(5, defaultValue: "")
  String createdAt;

  @HiveField(6, defaultValue: "")
  String updatedAt;

  ChatMessageModel({
    required this.id,
    required this.noteId,
    required this.userId,
    required this.title,
    required this.answer,
    required this.createdAt,
    required this.updatedAt,
  });
}
