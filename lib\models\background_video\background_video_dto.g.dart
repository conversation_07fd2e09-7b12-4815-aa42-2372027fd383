// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'background_video_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BackgroundVideoDtoImpl _$$BackgroundVideoDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$BackgroundVideoDtoImpl(
      backgroundId: json['bg_id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      width: (json['width'] as num?)?.toInt() ?? 0,
      height: (json['height'] as num?)?.toInt() ?? 0,
      thumbnailUrl: json['thumbnail_url'] as String? ?? '',
      videoUrl: json['video_url'] as String? ?? '',
    );

Map<String, dynamic> _$$BackgroundVideoDtoImplToJson(
        _$BackgroundVideoDtoImpl instance) =>
    <String, dynamic>{
      'bg_id': instance.backgroundId,
      'title': instance.title,
      'width': instance.width,
      'height': instance.height,
      'thumbnail_url': instance.thumbnailUrl,
      'video_url': instance.videoUrl,
    };
