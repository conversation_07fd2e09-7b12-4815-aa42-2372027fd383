<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_1408_1172)">
<rect width="40" height="40" rx="6.08" fill="#FF1212" fill-opacity="0.05"/>
<g filter="url(#filter1_iii_1408_1172)">
<circle cx="19.9654" cy="20.0348" r="9.62069" fill="#FF4847"/>
</g>
<g filter="url(#filter2_i_1408_1172)">
<path d="M17.5603 23.2055C17.5603 23.9401 18.3504 24.4037 18.9917 24.0453L24.235 21.1152C24.892 20.7481 24.892 19.8027 24.235 19.4356L18.9917 16.5055C18.3504 16.1471 17.5603 16.6107 17.5603 17.3453V23.2055Z" fill="white"/>
</g>
<circle cx="6.70742" cy="19.1491" r="0.58952" fill="#FFE2E2" stroke="url(#paint0_linear_1408_1172)" stroke-width="0.235808"/>
<circle cx="30.061" cy="26.5" r="0.444444" fill="#FFE2E2" stroke="url(#paint1_linear_1408_1172)" stroke-width="0.111111"/>
<circle cx="20.1486" cy="6.70742" r="0.58952" fill="#FFE2E2" stroke="url(#paint2_linear_1408_1172)" stroke-width="0.235808"/>
<circle cx="13.5" cy="30.5" r="0.416667" fill="#FFE2E2" stroke="url(#paint3_linear_1408_1172)" stroke-width="0.166667"/>
<circle cx="33.425" cy="19.2207" r="0.681671" fill="#FFE2E2" stroke="url(#paint4_linear_1408_1172)" stroke-width="0.194763"/>
</g>
<defs>
<filter id="filter0_ii_1408_1172" x="-1" y="0" width="41.76" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.76"/>
<feGaussianBlur stdDeviation="0.76"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1408_1172"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1408_1172" result="effect2_innerShadow_1408_1172"/>
</filter>
<filter id="filter1_iii_1408_1172" x="8.42059" y="9.93303" width="22.6087" height="21.1656" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.4431" dy="1.4431"/>
<feGaussianBlur stdDeviation="0.962069"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1408_1172"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.92414" dy="-0.481034"/>
<feGaussianBlur stdDeviation="0.962069"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.956863 0 0 0 0 0.329412 0 0 0 0 0.321569 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1408_1172" result="effect2_innerShadow_1408_1172"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.481034"/>
<feGaussianBlur stdDeviation="0.962069"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1408_1172" result="effect3_innerShadow_1408_1172"/>
</filter>
<filter id="filter2_i_1408_1172" x="17.5603" y="16.3818" width="7.16748" height="9.71125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.92414"/>
<feGaussianBlur stdDeviation="0.962069"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.891667 0 0 0 0 0.891667 0 0 0 0 0.891667 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1408_1172"/>
</filter>
<linearGradient id="paint0_linear_1408_1172" x1="5.96457" y1="19.8236" x2="8.76343" y2="18.9252" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.16" stop-color="#FFA2BB"/>
<stop offset="0.35" stop-color="#FE6E96"/>
<stop offset="0.55" stop-color="#FD4C7D"/>
<stop offset="0.82" stop-color="#FF9849"/>
<stop offset="1" stop-color="#FFF0E4"/>
</linearGradient>
<linearGradient id="paint1_linear_1408_1172" x1="29.536" y1="26.9768" x2="31.5142" y2="26.3417" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.16" stop-color="#FFA2BB"/>
<stop offset="0.35" stop-color="#FE6E96"/>
<stop offset="0.55" stop-color="#FD4C7D"/>
<stop offset="0.82" stop-color="#FF9849"/>
<stop offset="1" stop-color="#FFF0E4"/>
</linearGradient>
<linearGradient id="paint2_linear_1408_1172" x1="19.4057" y1="7.38195" x2="22.2046" y2="6.48352" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.16" stop-color="#FFA2BB"/>
<stop offset="0.35" stop-color="#FE6E96"/>
<stop offset="0.55" stop-color="#FD4C7D"/>
<stop offset="0.82" stop-color="#FF9849"/>
<stop offset="1" stop-color="#FFF0E4"/>
</linearGradient>
<linearGradient id="paint3_linear_1408_1172" x1="12.975" y1="30.9768" x2="14.9532" y2="30.3417" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.16" stop-color="#FFA2BB"/>
<stop offset="0.35" stop-color="#FE6E96"/>
<stop offset="0.55" stop-color="#FD4C7D"/>
<stop offset="0.82" stop-color="#FF9849"/>
<stop offset="1" stop-color="#FFF0E4"/>
</linearGradient>
<linearGradient id="paint4_linear_1408_1172" x1="32.607" y1="19.9635" x2="35.6892" y2="18.9741" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.16" stop-color="#FFA2BB"/>
<stop offset="0.35" stop-color="#FE6E96"/>
<stop offset="0.55" stop-color="#FD4C7D"/>
<stop offset="0.82" stop-color="#FF9849"/>
<stop offset="1" stop-color="#FFF0E4"/>
</linearGradient>
</defs>
</svg>
