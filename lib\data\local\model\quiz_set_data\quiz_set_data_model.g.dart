// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_set_data_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class QuizSetDataModelAdapter extends TypeAdapter<QuizSetDataModel> {
  @override
  final int typeId = 22;

  @override
  QuizSetDataModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return QuizSetDataModel(
      setId: fields[0] == null ? '' : fields[0] as String,
      name: fields[1] == null ? '' : fields[1] as String,
      difficulty: fields[3] == null ? '' : fields[3] as String,
      questionsCount: fields[2] == null ? 0 : fields[2] as int,
    );
  }

  @override
  void write(BinaryWriter writer, QuizSetDataModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.setId)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.questionsCount)
      ..writeByte(3)
      ..write(obj.difficulty);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuizSetDataModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
