// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'flashcard_sets_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FlashcardSetsDtoImpl _$$FlashcardSetsDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$FlashcardSetsDtoImpl(
      setId: json['set_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      difficulty: json['difficulty'] as String? ?? '',
      language: json['language'] as String? ?? '',
      cardsCount: (json['cards_count'] as num?)?.toInt() ?? 0,
      cards: (json['cards'] as List<dynamic>?)
              ?.map(
                  (e) => FlashCarDetailDto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
    );

Map<String, dynamic> _$$FlashcardSetsDtoImplToJson(
        _$FlashcardSetsDtoImpl instance) =>
    <String, dynamic>{
      'set_id': instance.setId,
      'name': instance.name,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'language': instance.language,
      'cards_count': instance.cardsCount,
      'cards': instance.cards,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
