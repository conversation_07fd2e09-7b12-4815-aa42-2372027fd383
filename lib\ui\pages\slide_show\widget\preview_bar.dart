import 'package:flutter/material.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/slide_show/widget/pdf_page_thumbnail.dart';

class PreviewBar extends StatelessWidget {
  final String localPdfPath;
  final int totalPages;
  final int currentPage;
  final void Function(int page) onPageTap;
  final ScrollController previewScrollController;
  const PreviewBar({
    Key? key,
    required this.localPdfPath,
    required this.totalPages,
    required this.currentPage,
    required this.onPageTap,
    required this.previewScrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        controller: previewScrollController,
        scrollDirection: Axis.horizontal,
        itemCount: totalPages,
        itemBuilder: (context, index) {
          final isSelected = currentPage == index + 1;
          const double width = 150;
          const double height = width * 9 / 16;
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
            child: PdfPageThumbnail(
              pdfPath: localPdfPath,
              pageNumber: index + 1,
              isSelected: isSelected,
              onPageTap: onPageTap,
              width: width,
              height: height,
            ),
          );
        },
      ),
    );
  }
}
