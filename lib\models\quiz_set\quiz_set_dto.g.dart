// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz_set_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuizSetDtoImpl _$$QuizSetDtoImplFromJson(Map<String, dynamic> json) =>
    _$QuizSetDtoImpl(
      setId: json['set_id'] as String? ?? '',
      noteId: json['note_id'] as String? ?? '',
      userId: json['user_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      description: json['description'] as String? ?? '',
      difficulty: json['difficulty'] as String? ?? '',
      language: json['language'] as String? ?? '',
      questionCount: (json['questions_count'] as num?)?.toInt() ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
    );

Map<String, dynamic> _$$QuizSetDtoImplToJson(_$QuizSetDtoImpl instance) =>
    <String, dynamic>{
      'set_id': instance.setId,
      'note_id': instance.noteId,
      'user_id': instance.userId,
      'name': instance.name,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'language': instance.language,
      'questions_count': instance.questionCount,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
