// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_set_data_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

QuizSetDataDto _$QuizSetDataDtoFromJson(Map<String, dynamic> json) {
  return _QuizSetDataDto.fromJson(json);
}

/// @nodoc
mixin _$QuizSetDataDto {
  @JsonKey(name: 'set_id')
  String get setId => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'questions_count')
  int get questionsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'difficulty')
  String get difficulty => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizSetDataDtoCopyWith<QuizSetDataDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizSetDataDtoCopyWith<$Res> {
  factory $QuizSetDataDtoCopyWith(
          QuizSetDataDto value, $Res Function(QuizSetDataDto) then) =
      _$QuizSetDataDtoCopyWithImpl<$Res, QuizSetDataDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'questions_count') int questionsCount,
      @JsonKey(name: 'difficulty') String difficulty});
}

/// @nodoc
class _$QuizSetDataDtoCopyWithImpl<$Res, $Val extends QuizSetDataDto>
    implements $QuizSetDataDtoCopyWith<$Res> {
  _$QuizSetDataDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? name = null,
    Object? questionsCount = null,
    Object? difficulty = null,
  }) {
    return _then(_value.copyWith(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      questionsCount: null == questionsCount
          ? _value.questionsCount
          : questionsCount // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizSetDataDtoImplCopyWith<$Res>
    implements $QuizSetDataDtoCopyWith<$Res> {
  factory _$$QuizSetDataDtoImplCopyWith(_$QuizSetDataDtoImpl value,
          $Res Function(_$QuizSetDataDtoImpl) then) =
      __$$QuizSetDataDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'questions_count') int questionsCount,
      @JsonKey(name: 'difficulty') String difficulty});
}

/// @nodoc
class __$$QuizSetDataDtoImplCopyWithImpl<$Res>
    extends _$QuizSetDataDtoCopyWithImpl<$Res, _$QuizSetDataDtoImpl>
    implements _$$QuizSetDataDtoImplCopyWith<$Res> {
  __$$QuizSetDataDtoImplCopyWithImpl(
      _$QuizSetDataDtoImpl _value, $Res Function(_$QuizSetDataDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? name = null,
    Object? questionsCount = null,
    Object? difficulty = null,
  }) {
    return _then(_$QuizSetDataDtoImpl(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      questionsCount: null == questionsCount
          ? _value.questionsCount
          : questionsCount // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizSetDataDtoImpl implements _QuizSetDataDto {
  const _$QuizSetDataDtoImpl(
      {@JsonKey(name: 'set_id') this.setId = '',
      @JsonKey(name: 'name') this.name = '',
      @JsonKey(name: 'questions_count') this.questionsCount = 0,
      @JsonKey(name: 'difficulty') this.difficulty = ''});

  factory _$QuizSetDataDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizSetDataDtoImplFromJson(json);

  @override
  @JsonKey(name: 'set_id')
  final String setId;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'questions_count')
  final int questionsCount;
  @override
  @JsonKey(name: 'difficulty')
  final String difficulty;

  @override
  String toString() {
    return 'QuizSetDataDto(setId: $setId, name: $name, questionsCount: $questionsCount, difficulty: $difficulty)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizSetDataDtoImpl &&
            (identical(other.setId, setId) || other.setId == setId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.questionsCount, questionsCount) ||
                other.questionsCount == questionsCount) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, setId, name, questionsCount, difficulty);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizSetDataDtoImplCopyWith<_$QuizSetDataDtoImpl> get copyWith =>
      __$$QuizSetDataDtoImplCopyWithImpl<_$QuizSetDataDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizSetDataDtoImplToJson(
      this,
    );
  }
}

abstract class _QuizSetDataDto implements QuizSetDataDto {
  const factory _QuizSetDataDto(
          {@JsonKey(name: 'set_id') final String setId,
          @JsonKey(name: 'name') final String name,
          @JsonKey(name: 'questions_count') final int questionsCount,
          @JsonKey(name: 'difficulty') final String difficulty}) =
      _$QuizSetDataDtoImpl;

  factory _QuizSetDataDto.fromJson(Map<String, dynamic> json) =
      _$QuizSetDataDtoImpl.fromJson;

  @override
  @JsonKey(name: 'set_id')
  String get setId;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'questions_count')
  int get questionsCount;
  @override
  @JsonKey(name: 'difficulty')
  String get difficulty;
  @override
  @JsonKey(ignore: true)
  _$$QuizSetDataDtoImplCopyWith<_$QuizSetDataDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
