import 'package:flutter/material.dart';
import 'package:note_x/lib.dart';

String buildTextTypeOfNote(NoteModel noteModel) {
  final typeLabels = {
    FilterType.youtube: S.current.youtube,
    FilterType.webLink: S.current.web_link,
    FilterType.record: S.current.record,
    FilterType.uploadAudio: S.current.audio,
    FilterType.document: S.current.document_tab,
  };

  return typeLabels[FilterType.values.firstWhere(
        (type) {
          return type.text == noteModel.type;
        },
        orElse: () {
          return FilterType.youtube;
        },
      )] ??
      S.current.youtube;
}

/// Returns the color for the note type.
Color getColorByNoteType(BuildContext context, NoteModel noteModel) {
  switch (noteModel.type) {
    case 'youtube_url':
      return AppColors.primaryRed;
    case 'website':
      return AppColors.primaryPurple;
    case 'audio_file':
      return AppColors.primaryYellow;
    case 'audio':
      return context.colorScheme.mainBlue;
    case 'document':
      return AppColors.primaryGreen;
    case 'social_media':
      return AppColors.primaryPurple;
    default:
      return context.colorScheme.mainGray;
  }
}

/// Returns the background color for the note type container.
Color getColorByNoteTypeContainer(BuildContext context, NoteModel noteModel) {
  switch (noteModel.type) {
    case 'youtube_url':
      return AppColors.primaryRed.withOpacity(0.24);
    case 'website':
      return AppColors.primaryPurple.withOpacity(0.24);
    case 'audio_file':
      return AppColors.primaryYellow.withOpacity(0.24);
    case 'audio':
      return AppColors.primaryBlue.withOpacity(0.24);
    case 'document':
      return AppColors.primaryGreen.withOpacity(0.24);
    case 'social_media':
      return AppColors.primaryPurple.withOpacity(0.24);
    default:
      return context.colorScheme.mainGray.withOpacity(0.24);
  }
}

/// Returns the icon asset path for the note type.
String getIconByNoteTypeContainer(NoteModel noteModel) {
  switch (noteModel.type) {
    case 'youtube_url':
      return Assets.icons.icWelcomePageYoutube;
    case 'website':
      return Assets.icons.icHomeYt;
    case 'audio_file':
      return Assets.icons.icHomeAudio;
    case 'audio':
      return Assets.icons.icRecord;
    case 'document':
      return Assets.icons.icHomeDocument;
    case 'social_media':
      return Assets.icons.icHomeYt;
    default:
      return Assets.icons.icHomeDocument;
  }
}

/// Builds the date time and duration text.
String buildDateTimeAndDurationText(NoteModel noteModel) {
  if (noteModel.duration > 0) {
    return "${noteModel.subTitle} · ${MyUtils.formatDuration(noteModel.duration)}";
  } else {
    return noteModel.subTitle;
  }
}
