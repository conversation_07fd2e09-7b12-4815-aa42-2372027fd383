import 'package:note_x/base/base.dart';
import 'base_data_mapper.dart';

class ChatTemplateDataMapper
    extends BaseDataMapper<ChatTemplateDto, ChatTemplateModel> {
  @override
  ChatTemplateModel mapToEntity(ChatTemplateDto data) {
    return ChatTemplateModel(
      id: data.id,
      name: data.name,
      prompt: data.prompt,
      type: data.type,
    );
  }

  @override
  ChatTemplateDto mapToDto(ChatTemplateModel entity) {
    return ChatTemplateDto(
      id: entity.id,
      name: entity.name,
      prompt: entity.prompt,
      type: entity.type,
    );
  }
}
