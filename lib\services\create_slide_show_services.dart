import 'package:note_x/lib.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';

// ignore: one_member_abstracts
abstract class SlideShowApiService {
  Future<String> getTaskIdForSlideShow({
    required String noteId,
    required String templateId,
    required String slideCountRange,
  });
  Future<List<SlideTemplateDto>> getThumbnailForSlideShow();
}

class SlideShowApiServiceImpl extends BaseApiService
    implements SlideShowApiService {
  final resultTaskApiService = GetIt.instance.get<ResultTaskApiServiceImpl>();
  @override
  Future<String> getTaskIdForSlideShow({
    required String noteId,
    required String templateId,
    required String slideCountRange,
  }) async {
    try {
      final formData = FormData.fromMap({
        'note_id': noteId,
        'template_id': templateId,
        'slide_count_range': slideCountRange,
      });
      final response = await dio.post('v1/create/slide', data: formData);
      final data = BaseDataDto<TaskDto>.fromJson(response.data,
          (json) => TaskDto.fromJson(json as Map<String, dynamic>));
      return data.data.taskId;
    } catch (err) {
      throw getError(err);
    }
  }

  @override
  Future<List<SlideTemplateDto>> getThumbnailForSlideShow() async {
    try {
      final response = await dio.get('v1/content/slide_templates');
      final data = BaseDataListDto<SlideTemplateDto>.fromJson(response.data,
          (json) => SlideTemplateDto.fromJson(json as Map<String, dynamic>));
      return data.data;
    } catch (err) {
      throw getError(err);
    }
  }
}
