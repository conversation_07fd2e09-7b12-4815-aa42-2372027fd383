import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:note_x/lib.dart';

import '../../../base/base_page_state.dart';

class FolderDetailPage extends StatefulWidget {
  const FolderDetailPage({
    super.key,
    required this.folder,
    this.isFolderFailed = false,
  });

  final FolderModel folder;
  final bool isFolderFailed;

  @override
  State<FolderDetailPage> createState() => _FolderDetailPageState();
}

class _FolderDetailPageState
    extends BasePageStateDelegate<FolderDetailPage, FolderCubit> {
  TextEditingController editFolderController = TextEditingController();

  @override
  void initState() {
    super.initState();
    cubit.init(widget.folder);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(
        title: widget.folder.folderName,
        onPressed: () {
          cubit.onFolderDetailBack();
          Navigator.pop(context);
        },
        actions: [
          widget.isFolderFailed
              ? const SizedBox()
              : Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: FolderMoreOptions(
                    cubit: cubit,
                    folder: widget.folder,
                    editFolderController: editFolderController,
                    icon: Assets.icons.icMore,
                    isInDetailPage: true,
                  ),
                ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ValueListenableBuilder<Box<NoteModel>>(
              valueListenable: widget.isFolderFailed
                  ? HiveService().noteFailedBox.listenable()
                  : HiveService().noteBox.listenable(),
              builder: (context, noteBox, child) {
                return FutureBuilder<List<NoteModel>>(
                  future: widget.isFolderFailed
                      ? HiveFolderService.getNotesFailed(userId: cubit.userId)
                      : HiveFolderService.getNotesInFolder(
                          folderBackendId: widget.folder.backendId,
                        ),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Center(
                        child: Lottie.asset(
                          Assets.videos.commonLoading,
                          width: 50.w,
                          height: 50.h,
                        ),
                      );
                    } else if (snapshot.hasData) {
                      if (snapshot.data == null || snapshot.data!.isEmpty) {
                        return Center(
                          child: Column(
                            children: [
                              AppConstants.kSpacingItem48,
                              SvgPicture.asset(
                                Assets.icons.icEmptyNoteNotexEmpty,
                                width: 160.w,
                                height: 160.h,
                              ),
                              AppConstants.kSpacingItem12,
                              CommonText(
                                S.current.no_notes_in_folder,
                                style: TextStyle(
                                  fontSize:
                                      cubit.appCubit.isTablet ? 18 : 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: context.colorScheme.mainPrimary,
                                ),
                              ),
                            ],
                          ),
                        );
                      } else {
                        return HomeItemNoteListView(
                          isFolderDetail: true,
                          listNote: snapshot.data!,
                        );
                      }
                    } else {
                      return const Center(child: CommonText('No notes found'));
                    }
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
