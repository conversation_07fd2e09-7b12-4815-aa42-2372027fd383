// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slide_template_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SlideTemplateDto _$SlideTemplateDtoFromJson(Map<String, dynamic> json) {
  return _SlideTemplateDto.fromJson(json);
}

/// @nodoc
mixin _$SlideTemplateDto {
  @JsonKey(name: 'id')
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'gif_url')
  String get gifUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'url')
  String get slideUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SlideTemplateDtoCopyWith<SlideTemplateDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SlideTemplateDtoCopyWith<$Res> {
  factory $SlideTemplateDtoCopyWith(
          SlideTemplateDto value, $Res Function(SlideTemplateDto) then) =
      _$SlideTemplateDtoCopyWithImpl<$Res, SlideTemplateDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'gif_url') String gifUrl,
      @JsonKey(name: 'url') String slideUrl});
}

/// @nodoc
class _$SlideTemplateDtoCopyWithImpl<$Res, $Val extends SlideTemplateDto>
    implements $SlideTemplateDtoCopyWith<$Res> {
  _$SlideTemplateDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? gifUrl = null,
    Object? slideUrl = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      gifUrl: null == gifUrl
          ? _value.gifUrl
          : gifUrl // ignore: cast_nullable_to_non_nullable
              as String,
      slideUrl: null == slideUrl
          ? _value.slideUrl
          : slideUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SlideTemplateDtoImplCopyWith<$Res>
    implements $SlideTemplateDtoCopyWith<$Res> {
  factory _$$SlideTemplateDtoImplCopyWith(_$SlideTemplateDtoImpl value,
          $Res Function(_$SlideTemplateDtoImpl) then) =
      __$$SlideTemplateDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String id,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'gif_url') String gifUrl,
      @JsonKey(name: 'url') String slideUrl});
}

/// @nodoc
class __$$SlideTemplateDtoImplCopyWithImpl<$Res>
    extends _$SlideTemplateDtoCopyWithImpl<$Res, _$SlideTemplateDtoImpl>
    implements _$$SlideTemplateDtoImplCopyWith<$Res> {
  __$$SlideTemplateDtoImplCopyWithImpl(_$SlideTemplateDtoImpl _value,
      $Res Function(_$SlideTemplateDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? gifUrl = null,
    Object? slideUrl = null,
  }) {
    return _then(_$SlideTemplateDtoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      gifUrl: null == gifUrl
          ? _value.gifUrl
          : gifUrl // ignore: cast_nullable_to_non_nullable
              as String,
      slideUrl: null == slideUrl
          ? _value.slideUrl
          : slideUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SlideTemplateDtoImpl implements _SlideTemplateDto {
  const _$SlideTemplateDtoImpl(
      {@JsonKey(name: 'id') this.id = '',
      @JsonKey(name: 'name') this.name = '',
      @JsonKey(name: 'gif_url') this.gifUrl = '',
      @JsonKey(name: 'url') this.slideUrl = ''});

  factory _$SlideTemplateDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$SlideTemplateDtoImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'gif_url')
  final String gifUrl;
  @override
  @JsonKey(name: 'url')
  final String slideUrl;

  @override
  String toString() {
    return 'SlideTemplateDto(id: $id, name: $name, gifUrl: $gifUrl, slideUrl: $slideUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SlideTemplateDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.gifUrl, gifUrl) || other.gifUrl == gifUrl) &&
            (identical(other.slideUrl, slideUrl) ||
                other.slideUrl == slideUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, gifUrl, slideUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SlideTemplateDtoImplCopyWith<_$SlideTemplateDtoImpl> get copyWith =>
      __$$SlideTemplateDtoImplCopyWithImpl<_$SlideTemplateDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SlideTemplateDtoImplToJson(
      this,
    );
  }
}

abstract class _SlideTemplateDto implements SlideTemplateDto {
  const factory _SlideTemplateDto(
      {@JsonKey(name: 'id') final String id,
      @JsonKey(name: 'name') final String name,
      @JsonKey(name: 'gif_url') final String gifUrl,
      @JsonKey(name: 'url') final String slideUrl}) = _$SlideTemplateDtoImpl;

  factory _SlideTemplateDto.fromJson(Map<String, dynamic> json) =
      _$SlideTemplateDtoImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String get id;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'gif_url')
  String get gifUrl;
  @override
  @JsonKey(name: 'url')
  String get slideUrl;
  @override
  @JsonKey(ignore: true)
  _$$SlideTemplateDtoImplCopyWith<_$SlideTemplateDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
