// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'folder_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$FolderState {
  FolderOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;
  List<FolderModel> get folders => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FolderStateCopyWith<FolderState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FolderStateCopyWith<$Res> {
  factory $FolderStateCopyWith(
          FolderState value, $Res Function(FolderState) then) =
      _$FolderStateCopyWithImpl<$Res, FolderState>;
  @useResult
  $Res call({FolderOneShotEvent oneShotEvent, List<FolderModel> folders});
}

/// @nodoc
class _$FolderStateCopyWithImpl<$Res, $Val extends FolderState>
    implements $FolderStateCopyWith<$Res> {
  _$FolderStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? folders = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as FolderOneShotEvent,
      folders: null == folders
          ? _value.folders
          : folders // ignore: cast_nullable_to_non_nullable
              as List<FolderModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FolderStateImplCopyWith<$Res>
    implements $FolderStateCopyWith<$Res> {
  factory _$$FolderStateImplCopyWith(
          _$FolderStateImpl value, $Res Function(_$FolderStateImpl) then) =
      __$$FolderStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({FolderOneShotEvent oneShotEvent, List<FolderModel> folders});
}

/// @nodoc
class __$$FolderStateImplCopyWithImpl<$Res>
    extends _$FolderStateCopyWithImpl<$Res, _$FolderStateImpl>
    implements _$$FolderStateImplCopyWith<$Res> {
  __$$FolderStateImplCopyWithImpl(
      _$FolderStateImpl _value, $Res Function(_$FolderStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? folders = null,
  }) {
    return _then(_$FolderStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as FolderOneShotEvent,
      folders: null == folders
          ? _value._folders
          : folders // ignore: cast_nullable_to_non_nullable
              as List<FolderModel>,
    ));
  }
}

/// @nodoc

class _$FolderStateImpl implements _FolderState {
  const _$FolderStateImpl(
      {this.oneShotEvent = FolderOneShotEvent.initial,
      final List<FolderModel> folders = const []})
      : _folders = folders;

  @override
  @JsonKey()
  final FolderOneShotEvent oneShotEvent;
  final List<FolderModel> _folders;
  @override
  @JsonKey()
  List<FolderModel> get folders {
    if (_folders is EqualUnmodifiableListView) return _folders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_folders);
  }

  @override
  String toString() {
    return 'FolderState(oneShotEvent: $oneShotEvent, folders: $folders)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FolderStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            const DeepCollectionEquality().equals(other._folders, _folders));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, oneShotEvent, const DeepCollectionEquality().hash(_folders));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FolderStateImplCopyWith<_$FolderStateImpl> get copyWith =>
      __$$FolderStateImplCopyWithImpl<_$FolderStateImpl>(this, _$identity);
}

abstract class _FolderState implements FolderState {
  const factory _FolderState(
      {final FolderOneShotEvent oneShotEvent,
      final List<FolderModel> folders}) = _$FolderStateImpl;

  @override
  FolderOneShotEvent get oneShotEvent;
  @override
  List<FolderModel> get folders;
  @override
  @JsonKey(ignore: true)
  _$$FolderStateImplCopyWith<_$FolderStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
