// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transcript_export_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TranscriptExportDto _$TranscriptExportDtoFromJson(Map<String, dynamic> json) {
  return _TranscriptExportDto.fromJson(json);
}

/// @nodoc
mixin _$TranscriptExportDto {
  @JsonKey(name: 'url')
  String? get url => throw _privateConstructorUsedError;
  @JsonKey(name: 'note_id')
  String? get noteId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TranscriptExportDtoCopyWith<TranscriptExportDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TranscriptExportDtoCopyWith<$Res> {
  factory $TranscriptExportDtoCopyWith(
          TranscriptExportDto value, $Res Function(TranscriptExportDto) then) =
      _$TranscriptExportDtoCopyWithImpl<$Res, TranscriptExportDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class _$TranscriptExportDtoCopyWithImpl<$Res, $Val extends TranscriptExportDto>
    implements $TranscriptExportDtoCopyWith<$Res> {
  _$TranscriptExportDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_value.copyWith(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TranscriptExportDtoImplCopyWith<$Res>
    implements $TranscriptExportDtoCopyWith<$Res> {
  factory _$$TranscriptExportDtoImplCopyWith(_$TranscriptExportDtoImpl value,
          $Res Function(_$TranscriptExportDtoImpl) then) =
      __$$TranscriptExportDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'url') String? url,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class __$$TranscriptExportDtoImplCopyWithImpl<$Res>
    extends _$TranscriptExportDtoCopyWithImpl<$Res, _$TranscriptExportDtoImpl>
    implements _$$TranscriptExportDtoImplCopyWith<$Res> {
  __$$TranscriptExportDtoImplCopyWithImpl(_$TranscriptExportDtoImpl _value,
      $Res Function(_$TranscriptExportDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_$TranscriptExportDtoImpl(
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TranscriptExportDtoImpl implements _TranscriptExportDto {
  const _$TranscriptExportDtoImpl(
      {@JsonKey(name: 'url') this.url, @JsonKey(name: 'note_id') this.noteId});

  factory _$TranscriptExportDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$TranscriptExportDtoImplFromJson(json);

  @override
  @JsonKey(name: 'url')
  final String? url;
  @override
  @JsonKey(name: 'note_id')
  final String? noteId;

  @override
  String toString() {
    return 'TranscriptExportDto(url: $url, noteId: $noteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TranscriptExportDtoImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.noteId, noteId) || other.noteId == noteId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url, noteId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TranscriptExportDtoImplCopyWith<_$TranscriptExportDtoImpl> get copyWith =>
      __$$TranscriptExportDtoImplCopyWithImpl<_$TranscriptExportDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TranscriptExportDtoImplToJson(
      this,
    );
  }
}

abstract class _TranscriptExportDto implements TranscriptExportDto {
  const factory _TranscriptExportDto(
          {@JsonKey(name: 'url') final String? url,
          @JsonKey(name: 'note_id') final String? noteId}) =
      _$TranscriptExportDtoImpl;

  factory _TranscriptExportDto.fromJson(Map<String, dynamic> json) =
      _$TranscriptExportDtoImpl.fromJson;

  @override
  @JsonKey(name: 'url')
  String? get url;
  @override
  @JsonKey(name: 'note_id')
  String? get noteId;
  @override
  @JsonKey(ignore: true)
  _$$TranscriptExportDtoImplCopyWith<_$TranscriptExportDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
