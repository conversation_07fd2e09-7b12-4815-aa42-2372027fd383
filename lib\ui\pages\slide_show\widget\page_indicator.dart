import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class PageIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  const PageIndicator(
      {Key? key, required this.currentPage, required this.totalPages})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 4, right: 8, top: 4, bottom: 4),
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(40),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: context.colorScheme.mainPrimary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: SvgPicture.asset(
                Assets.icons.icSlide,
                width: 12,
                height: 12,
              ),
            ),
          ),
          AppConstants.kSpacingItemW8,
          Text(
            '$currentPage of $totalPages',
            style: TextStyle(
              color: context.colorScheme.mainPrimary,
              fontWeight: FontWeight.w500,
              fontSize: context.isTablet ? 14.0 : 12.sp,
            ),
          ),
        ],
      ),
    );
  }
}
