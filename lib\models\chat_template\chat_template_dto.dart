// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_template_dto.freezed.dart';

part 'chat_template_dto.g.dart';

@freezed
class ChatTemplateDto with _$ChatTemplateDto {
  const factory ChatTemplateDto({
    @JsonKey(name: 'id') @Default('') String id,
    @J<PERSON><PERSON>ey(name: 'name') @Default('') String name,
    @Json<PERSON><PERSON>(name: 'prompt') @Default('') String prompt,
    @JsonKey(name: 'type') @Default('') String type,
  }) = _ChatTemplateDto;

  factory ChatTemplateDto.fromJson(Map<String, dynamic> json) =>
      _$ChatTemplateDtoFromJson(json);
}
