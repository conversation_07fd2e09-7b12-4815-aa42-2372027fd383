// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flash_card_detail_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FlashCarDetailDto _$FlashCarDetailDtoFromJson(Map<String, dynamic> json) {
  return _FlashCarDetailDto.fromJson(json);
}

/// @nodoc
mixin _$FlashCarDetailDto {
  String get question => throw _privateConstructorUsedError;
  String get answer => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  String get context => throw _privateConstructorUsedError;
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FlashCarDetailDtoCopyWith<FlashCarDetailDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlashCarDetailDtoCopyWith<$Res> {
  factory $FlashCarDetailDtoCopyWith(
          FlashCarDetailDto value, $Res Function(FlashCarDetailDto) then) =
      _$FlashCarDetailDtoCopyWithImpl<$Res, FlashCarDetailDto>;
  @useResult
  $Res call(
      {String question,
      String answer,
      String difficulty,
      String context,
      @JsonKey(name: 'transcript_json') List<TranscriptDto> transcriptJson});
}

/// @nodoc
class _$FlashCarDetailDtoCopyWithImpl<$Res, $Val extends FlashCarDetailDto>
    implements $FlashCarDetailDtoCopyWith<$Res> {
  _$FlashCarDetailDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answer = null,
    Object? difficulty = null,
    Object? context = null,
    Object? transcriptJson = null,
  }) {
    return _then(_value.copyWith(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String,
      transcriptJson: null == transcriptJson
          ? _value.transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptDto>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlashCarDetailDtoImplCopyWith<$Res>
    implements $FlashCarDetailDtoCopyWith<$Res> {
  factory _$$FlashCarDetailDtoImplCopyWith(_$FlashCarDetailDtoImpl value,
          $Res Function(_$FlashCarDetailDtoImpl) then) =
      __$$FlashCarDetailDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String question,
      String answer,
      String difficulty,
      String context,
      @JsonKey(name: 'transcript_json') List<TranscriptDto> transcriptJson});
}

/// @nodoc
class __$$FlashCarDetailDtoImplCopyWithImpl<$Res>
    extends _$FlashCarDetailDtoCopyWithImpl<$Res, _$FlashCarDetailDtoImpl>
    implements _$$FlashCarDetailDtoImplCopyWith<$Res> {
  __$$FlashCarDetailDtoImplCopyWithImpl(_$FlashCarDetailDtoImpl _value,
      $Res Function(_$FlashCarDetailDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? answer = null,
    Object? difficulty = null,
    Object? context = null,
    Object? transcriptJson = null,
  }) {
    return _then(_$FlashCarDetailDtoImpl(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String,
      transcriptJson: null == transcriptJson
          ? _value._transcriptJson
          : transcriptJson // ignore: cast_nullable_to_non_nullable
              as List<TranscriptDto>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FlashCarDetailDtoImpl implements _FlashCarDetailDto {
  const _$FlashCarDetailDtoImpl(
      {this.question = '',
      this.answer = '',
      this.difficulty = '',
      this.context = '',
      @JsonKey(name: 'transcript_json')
      final List<TranscriptDto> transcriptJson = const []})
      : _transcriptJson = transcriptJson;

  factory _$FlashCarDetailDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FlashCarDetailDtoImplFromJson(json);

  @override
  @JsonKey()
  final String question;
  @override
  @JsonKey()
  final String answer;
  @override
  @JsonKey()
  final String difficulty;
  @override
  @JsonKey()
  final String context;
  final List<TranscriptDto> _transcriptJson;
  @override
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson {
    if (_transcriptJson is EqualUnmodifiableListView) return _transcriptJson;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transcriptJson);
  }

  @override
  String toString() {
    return 'FlashCarDetailDto(question: $question, answer: $answer, difficulty: $difficulty, context: $context, transcriptJson: $transcriptJson)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlashCarDetailDtoImpl &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.context, context) || other.context == context) &&
            const DeepCollectionEquality()
                .equals(other._transcriptJson, _transcriptJson));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, question, answer, difficulty,
      context, const DeepCollectionEquality().hash(_transcriptJson));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FlashCarDetailDtoImplCopyWith<_$FlashCarDetailDtoImpl> get copyWith =>
      __$$FlashCarDetailDtoImplCopyWithImpl<_$FlashCarDetailDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FlashCarDetailDtoImplToJson(
      this,
    );
  }
}

abstract class _FlashCarDetailDto implements FlashCarDetailDto {
  const factory _FlashCarDetailDto(
      {final String question,
      final String answer,
      final String difficulty,
      final String context,
      @JsonKey(name: 'transcript_json')
      final List<TranscriptDto> transcriptJson}) = _$FlashCarDetailDtoImpl;

  factory _FlashCarDetailDto.fromJson(Map<String, dynamic> json) =
      _$FlashCarDetailDtoImpl.fromJson;

  @override
  String get question;
  @override
  String get answer;
  @override
  String get difficulty;
  @override
  String get context;
  @override
  @JsonKey(name: 'transcript_json')
  List<TranscriptDto> get transcriptJson;
  @override
  @JsonKey(ignore: true)
  _$$FlashCarDetailDtoImplCopyWith<_$FlashCarDetailDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
