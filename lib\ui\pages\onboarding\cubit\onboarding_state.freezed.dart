// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$OnboardingState {
  int get currentIndex => throw _privateConstructorUsedError;
  bool get isGetStartedScreen => throw _privateConstructorUsedError;
  bool get isChooseTypeScreen => throw _privateConstructorUsedError;
  bool get isCustomTabScreen => throw _privateConstructorUsedError;
  int get selectedTypeIndex => throw _privateConstructorUsedError;
  OnboardingOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OnboardingStateCopyWith<OnboardingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingStateCopyWith<$Res> {
  factory $OnboardingStateCopyWith(
          OnboardingState value, $Res Function(OnboardingState) then) =
      _$OnboardingStateCopyWithImpl<$Res, OnboardingState>;
  @useResult
  $Res call(
      {int currentIndex,
      bool isGetStartedScreen,
      bool isChooseTypeScreen,
      bool isCustomTabScreen,
      int selectedTypeIndex,
      OnboardingOneShotEvent oneShotEvent});
}

/// @nodoc
class _$OnboardingStateCopyWithImpl<$Res, $Val extends OnboardingState>
    implements $OnboardingStateCopyWith<$Res> {
  _$OnboardingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentIndex = null,
    Object? isGetStartedScreen = null,
    Object? isChooseTypeScreen = null,
    Object? isCustomTabScreen = null,
    Object? selectedTypeIndex = null,
    Object? oneShotEvent = null,
  }) {
    return _then(_value.copyWith(
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isGetStartedScreen: null == isGetStartedScreen
          ? _value.isGetStartedScreen
          : isGetStartedScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      isChooseTypeScreen: null == isChooseTypeScreen
          ? _value.isChooseTypeScreen
          : isChooseTypeScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomTabScreen: null == isCustomTabScreen
          ? _value.isCustomTabScreen
          : isCustomTabScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedTypeIndex: null == selectedTypeIndex
          ? _value.selectedTypeIndex
          : selectedTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as OnboardingOneShotEvent,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OnboardingStateImplCopyWith<$Res>
    implements $OnboardingStateCopyWith<$Res> {
  factory _$$OnboardingStateImplCopyWith(_$OnboardingStateImpl value,
          $Res Function(_$OnboardingStateImpl) then) =
      __$$OnboardingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentIndex,
      bool isGetStartedScreen,
      bool isChooseTypeScreen,
      bool isCustomTabScreen,
      int selectedTypeIndex,
      OnboardingOneShotEvent oneShotEvent});
}

/// @nodoc
class __$$OnboardingStateImplCopyWithImpl<$Res>
    extends _$OnboardingStateCopyWithImpl<$Res, _$OnboardingStateImpl>
    implements _$$OnboardingStateImplCopyWith<$Res> {
  __$$OnboardingStateImplCopyWithImpl(
      _$OnboardingStateImpl _value, $Res Function(_$OnboardingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentIndex = null,
    Object? isGetStartedScreen = null,
    Object? isChooseTypeScreen = null,
    Object? isCustomTabScreen = null,
    Object? selectedTypeIndex = null,
    Object? oneShotEvent = null,
  }) {
    return _then(_$OnboardingStateImpl(
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isGetStartedScreen: null == isGetStartedScreen
          ? _value.isGetStartedScreen
          : isGetStartedScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      isChooseTypeScreen: null == isChooseTypeScreen
          ? _value.isChooseTypeScreen
          : isChooseTypeScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomTabScreen: null == isCustomTabScreen
          ? _value.isCustomTabScreen
          : isCustomTabScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedTypeIndex: null == selectedTypeIndex
          ? _value.selectedTypeIndex
          : selectedTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as OnboardingOneShotEvent,
    ));
  }
}

/// @nodoc

class _$OnboardingStateImpl implements _OnboardingState {
  const _$OnboardingStateImpl(
      {this.currentIndex = 0,
      this.isGetStartedScreen = true,
      this.isChooseTypeScreen = false,
      this.isCustomTabScreen = false,
      this.selectedTypeIndex = -1,
      this.oneShotEvent = OnboardingOneShotEvent.none});

  @override
  @JsonKey()
  final int currentIndex;
  @override
  @JsonKey()
  final bool isGetStartedScreen;
  @override
  @JsonKey()
  final bool isChooseTypeScreen;
  @override
  @JsonKey()
  final bool isCustomTabScreen;
  @override
  @JsonKey()
  final int selectedTypeIndex;
  @override
  @JsonKey()
  final OnboardingOneShotEvent oneShotEvent;

  @override
  String toString() {
    return 'OnboardingState(currentIndex: $currentIndex, isGetStartedScreen: $isGetStartedScreen, isChooseTypeScreen: $isChooseTypeScreen, isCustomTabScreen: $isCustomTabScreen, selectedTypeIndex: $selectedTypeIndex, oneShotEvent: $oneShotEvent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingStateImpl &&
            (identical(other.currentIndex, currentIndex) ||
                other.currentIndex == currentIndex) &&
            (identical(other.isGetStartedScreen, isGetStartedScreen) ||
                other.isGetStartedScreen == isGetStartedScreen) &&
            (identical(other.isChooseTypeScreen, isChooseTypeScreen) ||
                other.isChooseTypeScreen == isChooseTypeScreen) &&
            (identical(other.isCustomTabScreen, isCustomTabScreen) ||
                other.isCustomTabScreen == isCustomTabScreen) &&
            (identical(other.selectedTypeIndex, selectedTypeIndex) ||
                other.selectedTypeIndex == selectedTypeIndex) &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentIndex, isGetStartedScreen,
      isChooseTypeScreen, isCustomTabScreen, selectedTypeIndex, oneShotEvent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      __$$OnboardingStateImplCopyWithImpl<_$OnboardingStateImpl>(
          this, _$identity);
}

abstract class _OnboardingState implements OnboardingState {
  const factory _OnboardingState(
      {final int currentIndex,
      final bool isGetStartedScreen,
      final bool isChooseTypeScreen,
      final bool isCustomTabScreen,
      final int selectedTypeIndex,
      final OnboardingOneShotEvent oneShotEvent}) = _$OnboardingStateImpl;

  @override
  int get currentIndex;
  @override
  bool get isGetStartedScreen;
  @override
  bool get isChooseTypeScreen;
  @override
  bool get isCustomTabScreen;
  @override
  int get selectedTypeIndex;
  @override
  OnboardingOneShotEvent get oneShotEvent;
  @override
  @JsonKey(ignore: true)
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
