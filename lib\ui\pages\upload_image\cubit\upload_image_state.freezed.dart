// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'upload_image_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$UploadImageState {
  CreateNoteWithImageOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  List<String> get selectedImagePaths => throw _privateConstructorUsedError;
  bool get hasSelectedImages => throw _privateConstructorUsedError;
  bool get isMergingImages => throw _privateConstructorUsedError;
  String get summaryStyle => throw _privateConstructorUsedError;
  String get writingStyle => throw _privateConstructorUsedError;
  String get additionalInstructions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $UploadImageStateCopyWith<UploadImageState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadImageStateCopyWith<$Res> {
  factory $UploadImageStateCopyWith(
          UploadImageState value, $Res Function(UploadImageState) then) =
      _$UploadImageStateCopyWithImpl<$Res, UploadImageState>;
  @useResult
  $Res call(
      {CreateNoteWithImageOneShotEvent oneShotEvent,
      List<String> selectedImagePaths,
      bool hasSelectedImages,
      bool isMergingImages,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class _$UploadImageStateCopyWithImpl<$Res, $Val extends UploadImageState>
    implements $UploadImageStateCopyWith<$Res> {
  _$UploadImageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? selectedImagePaths = null,
    Object? hasSelectedImages = null,
    Object? isMergingImages = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithImageOneShotEvent,
      selectedImagePaths: null == selectedImagePaths
          ? _value.selectedImagePaths
          : selectedImagePaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      hasSelectedImages: null == hasSelectedImages
          ? _value.hasSelectedImages
          : hasSelectedImages // ignore: cast_nullable_to_non_nullable
              as bool,
      isMergingImages: null == isMergingImages
          ? _value.isMergingImages
          : isMergingImages // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadImageStateImplCopyWith<$Res>
    implements $UploadImageStateCopyWith<$Res> {
  factory _$$UploadImageStateImplCopyWith(_$UploadImageStateImpl value,
          $Res Function(_$UploadImageStateImpl) then) =
      __$$UploadImageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreateNoteWithImageOneShotEvent oneShotEvent,
      List<String> selectedImagePaths,
      bool hasSelectedImages,
      bool isMergingImages,
      String summaryStyle,
      String writingStyle,
      String additionalInstructions});
}

/// @nodoc
class __$$UploadImageStateImplCopyWithImpl<$Res>
    extends _$UploadImageStateCopyWithImpl<$Res, _$UploadImageStateImpl>
    implements _$$UploadImageStateImplCopyWith<$Res> {
  __$$UploadImageStateImplCopyWithImpl(_$UploadImageStateImpl _value,
      $Res Function(_$UploadImageStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? selectedImagePaths = null,
    Object? hasSelectedImages = null,
    Object? isMergingImages = null,
    Object? summaryStyle = null,
    Object? writingStyle = null,
    Object? additionalInstructions = null,
  }) {
    return _then(_$UploadImageStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as CreateNoteWithImageOneShotEvent,
      selectedImagePaths: null == selectedImagePaths
          ? _value._selectedImagePaths
          : selectedImagePaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      hasSelectedImages: null == hasSelectedImages
          ? _value.hasSelectedImages
          : hasSelectedImages // ignore: cast_nullable_to_non_nullable
              as bool,
      isMergingImages: null == isMergingImages
          ? _value.isMergingImages
          : isMergingImages // ignore: cast_nullable_to_non_nullable
              as bool,
      summaryStyle: null == summaryStyle
          ? _value.summaryStyle
          : summaryStyle // ignore: cast_nullable_to_non_nullable
              as String,
      writingStyle: null == writingStyle
          ? _value.writingStyle
          : writingStyle // ignore: cast_nullable_to_non_nullable
              as String,
      additionalInstructions: null == additionalInstructions
          ? _value.additionalInstructions
          : additionalInstructions // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UploadImageStateImpl implements _UploadImageState {
  const _$UploadImageStateImpl(
      {this.oneShotEvent = CreateNoteWithImageOneShotEvent.none,
      final List<String> selectedImagePaths = const [],
      this.hasSelectedImages = false,
      this.isMergingImages = false,
      this.summaryStyle = '',
      this.writingStyle = '',
      this.additionalInstructions = ''})
      : _selectedImagePaths = selectedImagePaths;

  @override
  @JsonKey()
  final CreateNoteWithImageOneShotEvent oneShotEvent;
  final List<String> _selectedImagePaths;
  @override
  @JsonKey()
  List<String> get selectedImagePaths {
    if (_selectedImagePaths is EqualUnmodifiableListView)
      return _selectedImagePaths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedImagePaths);
  }

  @override
  @JsonKey()
  final bool hasSelectedImages;
  @override
  @JsonKey()
  final bool isMergingImages;
  @override
  @JsonKey()
  final String summaryStyle;
  @override
  @JsonKey()
  final String writingStyle;
  @override
  @JsonKey()
  final String additionalInstructions;

  @override
  String toString() {
    return 'UploadImageState(oneShotEvent: $oneShotEvent, selectedImagePaths: $selectedImagePaths, hasSelectedImages: $hasSelectedImages, isMergingImages: $isMergingImages, summaryStyle: $summaryStyle, writingStyle: $writingStyle, additionalInstructions: $additionalInstructions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadImageStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._selectedImagePaths, _selectedImagePaths) &&
            (identical(other.hasSelectedImages, hasSelectedImages) ||
                other.hasSelectedImages == hasSelectedImages) &&
            (identical(other.isMergingImages, isMergingImages) ||
                other.isMergingImages == isMergingImages) &&
            (identical(other.summaryStyle, summaryStyle) ||
                other.summaryStyle == summaryStyle) &&
            (identical(other.writingStyle, writingStyle) ||
                other.writingStyle == writingStyle) &&
            (identical(other.additionalInstructions, additionalInstructions) ||
                other.additionalInstructions == additionalInstructions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      const DeepCollectionEquality().hash(_selectedImagePaths),
      hasSelectedImages,
      isMergingImages,
      summaryStyle,
      writingStyle,
      additionalInstructions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadImageStateImplCopyWith<_$UploadImageStateImpl> get copyWith =>
      __$$UploadImageStateImplCopyWithImpl<_$UploadImageStateImpl>(
          this, _$identity);
}

abstract class _UploadImageState implements UploadImageState {
  const factory _UploadImageState(
      {final CreateNoteWithImageOneShotEvent oneShotEvent,
      final List<String> selectedImagePaths,
      final bool hasSelectedImages,
      final bool isMergingImages,
      final String summaryStyle,
      final String writingStyle,
      final String additionalInstructions}) = _$UploadImageStateImpl;

  @override
  CreateNoteWithImageOneShotEvent get oneShotEvent;
  @override
  List<String> get selectedImagePaths;
  @override
  bool get hasSelectedImages;
  @override
  bool get isMergingImages;
  @override
  String get summaryStyle;
  @override
  String get writingStyle;
  @override
  String get additionalInstructions;
  @override
  @JsonKey(ignore: true)
  _$$UploadImageStateImplCopyWith<_$UploadImageStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
