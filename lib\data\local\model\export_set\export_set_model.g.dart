// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_set_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ExportSetModelAdapter extends TypeAdapter<ExportSetModel> {
  @override
  final int typeId = 19;

  @override
  ExportSetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ExportSetModel(
      url: fields[0] == null ? '' : fields[0] as String,
      setId: fields[1] == null ? '' : fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ExportSetModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.url)
      ..writeByte(1)
      ..write(obj.setId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExportSetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
