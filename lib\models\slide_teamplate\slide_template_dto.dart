// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
part 'slide_template_dto.freezed.dart';
part 'slide_template_dto.g.dart';

@freezed
class SlideTemplateDto with _$SlideTemplateDto {
  const factory SlideTemplateDto({
    @JsonKey(name: 'id') @Default('') String id,
    @J<PERSON><PERSON>ey(name: 'name') @Default('') String name,
    @Json<PERSON>ey(name: 'gif_url') @Default('') String gifUrl,
    @J<PERSON><PERSON>ey(name: 'url') @Default('') String slideUrl,
  }) = _SlideTemplateDto;

  factory SlideTemplateDto.fromJson(Map<String, dynamic> json) =>
      _$SlideTemplateDtoFromJson(json);
}
