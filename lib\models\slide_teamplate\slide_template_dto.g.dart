// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slide_template_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SlideTemplateDtoImpl _$$SlideTemplateDtoImplFromJson(
        Map<String, dynamic> json) =>
    _$SlideTemplateDtoImpl(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      gifUrl: json['gif_url'] as String? ?? '',
      slideUrl: json['url'] as String? ?? '',
    );

Map<String, dynamic> _$$SlideTemplateDtoImplToJson(
        _$SlideTemplateDtoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'gif_url': instance.gifUrl,
      'url': instance.slideUrl,
    };
