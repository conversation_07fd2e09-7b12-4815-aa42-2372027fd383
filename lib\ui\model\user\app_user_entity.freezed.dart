// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_user_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

AppUserEntity _$AppUserEntityFromJson(Map<String, dynamic> json) {
  return _AppUserEntity.fromJson(json);
}

/// @nodoc
mixin _$AppUserEntity {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get youtubeCredit => throw _privateConstructorUsedError;
  int get webCredit => throw _privateConstructorUsedError;
  String get accessToken => throw _privateConstructorUsedError;
  String get refreshToken => throw _privateConstructorUsedError;
  int get audioCredit => throw _privateConstructorUsedError;
  int get rewardCredits => throw _privateConstructorUsedError;
  int get shortsCredit => throw _privateConstructorUsedError;
  int get documentCredit => throw _privateConstructorUsedError;
  int get slideCredit => throw _privateConstructorUsedError;
  UserType get userType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppUserEntityCopyWith<AppUserEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppUserEntityCopyWith<$Res> {
  factory $AppUserEntityCopyWith(
          AppUserEntity value, $Res Function(AppUserEntity) then) =
      _$AppUserEntityCopyWithImpl<$Res, AppUserEntity>;
  @useResult
  $Res call(
      {String id,
      String name,
      int youtubeCredit,
      int webCredit,
      String accessToken,
      String refreshToken,
      int audioCredit,
      int rewardCredits,
      int shortsCredit,
      int documentCredit,
      int slideCredit,
      UserType userType});
}

/// @nodoc
class _$AppUserEntityCopyWithImpl<$Res, $Val extends AppUserEntity>
    implements $AppUserEntityCopyWith<$Res> {
  _$AppUserEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? youtubeCredit = null,
    Object? webCredit = null,
    Object? accessToken = null,
    Object? refreshToken = null,
    Object? audioCredit = null,
    Object? rewardCredits = null,
    Object? shortsCredit = null,
    Object? documentCredit = null,
    Object? slideCredit = null,
    Object? userType = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      youtubeCredit: null == youtubeCredit
          ? _value.youtubeCredit
          : youtubeCredit // ignore: cast_nullable_to_non_nullable
              as int,
      webCredit: null == webCredit
          ? _value.webCredit
          : webCredit // ignore: cast_nullable_to_non_nullable
              as int,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      audioCredit: null == audioCredit
          ? _value.audioCredit
          : audioCredit // ignore: cast_nullable_to_non_nullable
              as int,
      rewardCredits: null == rewardCredits
          ? _value.rewardCredits
          : rewardCredits // ignore: cast_nullable_to_non_nullable
              as int,
      shortsCredit: null == shortsCredit
          ? _value.shortsCredit
          : shortsCredit // ignore: cast_nullable_to_non_nullable
              as int,
      documentCredit: null == documentCredit
          ? _value.documentCredit
          : documentCredit // ignore: cast_nullable_to_non_nullable
              as int,
      slideCredit: null == slideCredit
          ? _value.slideCredit
          : slideCredit // ignore: cast_nullable_to_non_nullable
              as int,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as UserType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppUserEntityImplCopyWith<$Res>
    implements $AppUserEntityCopyWith<$Res> {
  factory _$$AppUserEntityImplCopyWith(
          _$AppUserEntityImpl value, $Res Function(_$AppUserEntityImpl) then) =
      __$$AppUserEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      int youtubeCredit,
      int webCredit,
      String accessToken,
      String refreshToken,
      int audioCredit,
      int rewardCredits,
      int shortsCredit,
      int documentCredit,
      int slideCredit,
      UserType userType});
}

/// @nodoc
class __$$AppUserEntityImplCopyWithImpl<$Res>
    extends _$AppUserEntityCopyWithImpl<$Res, _$AppUserEntityImpl>
    implements _$$AppUserEntityImplCopyWith<$Res> {
  __$$AppUserEntityImplCopyWithImpl(
      _$AppUserEntityImpl _value, $Res Function(_$AppUserEntityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? youtubeCredit = null,
    Object? webCredit = null,
    Object? accessToken = null,
    Object? refreshToken = null,
    Object? audioCredit = null,
    Object? rewardCredits = null,
    Object? shortsCredit = null,
    Object? documentCredit = null,
    Object? slideCredit = null,
    Object? userType = null,
  }) {
    return _then(_$AppUserEntityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      youtubeCredit: null == youtubeCredit
          ? _value.youtubeCredit
          : youtubeCredit // ignore: cast_nullable_to_non_nullable
              as int,
      webCredit: null == webCredit
          ? _value.webCredit
          : webCredit // ignore: cast_nullable_to_non_nullable
              as int,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      audioCredit: null == audioCredit
          ? _value.audioCredit
          : audioCredit // ignore: cast_nullable_to_non_nullable
              as int,
      rewardCredits: null == rewardCredits
          ? _value.rewardCredits
          : rewardCredits // ignore: cast_nullable_to_non_nullable
              as int,
      shortsCredit: null == shortsCredit
          ? _value.shortsCredit
          : shortsCredit // ignore: cast_nullable_to_non_nullable
              as int,
      documentCredit: null == documentCredit
          ? _value.documentCredit
          : documentCredit // ignore: cast_nullable_to_non_nullable
              as int,
      slideCredit: null == slideCredit
          ? _value.slideCredit
          : slideCredit // ignore: cast_nullable_to_non_nullable
              as int,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as UserType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppUserEntityImpl implements _AppUserEntity {
  const _$AppUserEntityImpl(
      {this.id = '',
      this.name = '',
      this.youtubeCredit = 0,
      this.webCredit = 0,
      this.accessToken = '',
      this.refreshToken = '',
      this.audioCredit = 0,
      this.rewardCredits = 0,
      this.shortsCredit = 0,
      this.documentCredit = 0,
      this.slideCredit = 0,
      this.userType = UserType.free});

  factory _$AppUserEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppUserEntityImplFromJson(json);

  @override
  @JsonKey()
  final String id;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final int youtubeCredit;
  @override
  @JsonKey()
  final int webCredit;
  @override
  @JsonKey()
  final String accessToken;
  @override
  @JsonKey()
  final String refreshToken;
  @override
  @JsonKey()
  final int audioCredit;
  @override
  @JsonKey()
  final int rewardCredits;
  @override
  @JsonKey()
  final int shortsCredit;
  @override
  @JsonKey()
  final int documentCredit;
  @override
  @JsonKey()
  final int slideCredit;
  @override
  @JsonKey()
  final UserType userType;

  @override
  String toString() {
    return 'AppUserEntity(id: $id, name: $name, youtubeCredit: $youtubeCredit, webCredit: $webCredit, accessToken: $accessToken, refreshToken: $refreshToken, audioCredit: $audioCredit, rewardCredits: $rewardCredits, shortsCredit: $shortsCredit, documentCredit: $documentCredit, slideCredit: $slideCredit, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppUserEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.youtubeCredit, youtubeCredit) ||
                other.youtubeCredit == youtubeCredit) &&
            (identical(other.webCredit, webCredit) ||
                other.webCredit == webCredit) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.audioCredit, audioCredit) ||
                other.audioCredit == audioCredit) &&
            (identical(other.rewardCredits, rewardCredits) ||
                other.rewardCredits == rewardCredits) &&
            (identical(other.shortsCredit, shortsCredit) ||
                other.shortsCredit == shortsCredit) &&
            (identical(other.documentCredit, documentCredit) ||
                other.documentCredit == documentCredit) &&
            (identical(other.slideCredit, slideCredit) ||
                other.slideCredit == slideCredit) &&
            (identical(other.userType, userType) ||
                other.userType == userType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      youtubeCredit,
      webCredit,
      accessToken,
      refreshToken,
      audioCredit,
      rewardCredits,
      shortsCredit,
      documentCredit,
      slideCredit,
      userType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppUserEntityImplCopyWith<_$AppUserEntityImpl> get copyWith =>
      __$$AppUserEntityImplCopyWithImpl<_$AppUserEntityImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppUserEntityImplToJson(
      this,
    );
  }
}

abstract class _AppUserEntity implements AppUserEntity {
  const factory _AppUserEntity(
      {final String id,
      final String name,
      final int youtubeCredit,
      final int webCredit,
      final String accessToken,
      final String refreshToken,
      final int audioCredit,
      final int rewardCredits,
      final int shortsCredit,
      final int documentCredit,
      final int slideCredit,
      final UserType userType}) = _$AppUserEntityImpl;

  factory _AppUserEntity.fromJson(Map<String, dynamic> json) =
      _$AppUserEntityImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  int get youtubeCredit;
  @override
  int get webCredit;
  @override
  String get accessToken;
  @override
  String get refreshToken;
  @override
  int get audioCredit;
  @override
  int get rewardCredits;
  @override
  int get shortsCredit;
  @override
  int get documentCredit;
  @override
  int get slideCredit;
  @override
  UserType get userType;
  @override
  @JsonKey(ignore: true)
  _$$AppUserEntityImplCopyWith<_$AppUserEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
