import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';
import 'package:flutter/cupertino.dart';

class OutputLanguageDialog extends StatefulWidget {
  final Function(Language?) onPressed;
  final String title;
  final String targetLanguageCode;

  const OutputLanguageDialog(
      {Key? key,
      required this.onPressed,
      required this.title,
      required this.targetLanguageCode})
      : super(key: key);

  @override
  State<OutputLanguageDialog> createState() => _OutputLanguageDialogState();
}

class _OutputLanguageDialogState extends State<OutputLanguageDialog> {
  ValueNotifier<bool> isShowErrorText = ValueNotifier(false);
  Language? _selectedLanguage;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: context.colorScheme.mainSecondary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      insetPadding: EdgeInsets.symmetric(
        horizontal: context.isTablet ? 170 : 16.w,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CommonText(
                  S.current.output_language,
                  style: TextStyle(
                    fontSize: context.isTablet ? 22 : 20.sp,
                    fontWeight: FontWeight.w600,
                    color: context.colorScheme.mainPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            CommonText(
              S.current.speech_language,
              style: TextStyle(
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w400,
                color: context.colorScheme.mainGray,
              ),
            ),
            SizedBox(height: 8.h),
            Container(
              decoration: BoxDecoration(
                color: context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: LanguageDropdownView(
                initialLanguageCode: widget.targetLanguageCode,
                onChanged: (lang) {
                  _selectedLanguage = lang;
                  AnalyticsService.logAnalyticsEventNoParam(
                    eventName: EventName.translate_choose_a_language,
                  );
                },
                onMenuStateChanged: (isShowing) {},
                useCase: LanguageDropdownUseCase.defaultTranslateLanguage,
                from: LanguageDropdownFrom.translate,
              ),
            ),
            SizedBox(height: 16.h),
            GestureDetector(
              onTap: () {
                widget.onPressed(_selectedLanguage);
                Navigator.pop(context);
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: context.colorScheme.mainBlue,
                ),
                width: double.infinity,
                height: context.isTablet ? 48 : 44.h,
                alignment: Alignment.center,
                child: CommonText(
                  S.current.submit_button,
                  style: TextStyle(
                    color: AppColors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: context.isTablet ? 18 : 16.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BlurToastDialog extends StatelessWidget {
  final String message;
  final Duration duration;

  const BlurToastDialog({
    Key? key,
    required this.message,
    required this.duration,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const CommonBlurLayout(),
        Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: EdgeInsets.only(bottom: 50.h),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    Assets.icons.icCheckGreen,
                    width: 18.w,
                    height: 18.h,
                  ),
                  SizedBox(width: 8.w),
                  CommonText(
                    message,
                    style: TextStyle(
                      fontFamily: AppConstants.fontSFPro,
                      fontSize: context.isTablet ? 17 : 15.sp,
                      fontWeight: FontWeight.w500,
                      color: context.colorScheme.mainBlue,
                      decoration: TextDecoration.none,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class TranslatingDialog extends StatelessWidget {
  final String title;

  const TranslatingDialog({
    Key? key,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Dialog(
          backgroundColor: context.colorScheme.mainNeutral,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonText(
                      title,
                      style: TextStyle(
                        fontSize: context.isTablet ? 17 : 17.sp,
                        fontWeight: FontWeight.w600,
                        color: context.colorScheme.mainPrimary,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 16.h),
                SizedBox(
                  width: 16.0,
                  height: 16.0,
                  child: CupertinoActivityIndicator(
                    color: context.colorScheme.mainBlue,
                    radius: 16.0,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
