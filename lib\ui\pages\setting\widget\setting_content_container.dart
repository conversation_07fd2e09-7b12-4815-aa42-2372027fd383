import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:note_x/lib.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:url_launcher/url_launcher.dart';

enum SettingContentType {
  communityFeedback,
  quickAccess,
  account,
  login,
  helpLegal,
  refer,
  aboutUs,
  appearance,
}

enum SettingFunction {
  rate,
  termOfUse,
  privacyPolicy,
  notifications,
  customNoteTabs,
  recording,
  uploadAudio,
  contactUs,
  suggestFeatures,
  account,
  deleteAccount,
  logout,
  whatsNew,
  discord,
  syncNotes,
  credits,
  referral,
  enterReferralCode,
  aboutUs,
  syncWatch,
  toggleTheme,
  audioFile,
}

class SettingContentContainer extends StatelessWidget {
  final SettingContentType contentType;
  final Func1<SettingFunction, void>? onClickFunc;
  final Widget? trailingContent;

  const SettingContentContainer({
    Key? key,
    required this.contentType,
    this.onClickFunc,
    this.trailingContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final functionList = _getFunctionList();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.colorScheme.mainNeutral,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: ListView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        itemCount: functionList.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) =>
            _buildListItem(context, functionList[index]),
      ),
    );
  }

  List<SettingFunctionsViewData> _getFunctionList() {
    switch (contentType) {
      case SettingContentType.communityFeedback:
        return communityFeedbackFunctionList;
      case SettingContentType.quickAccess:
        return quickAccessFunctionList;
      case SettingContentType.account:
        return accountFunc;
      case SettingContentType.login:
        return loginFunc;
      case SettingContentType.helpLegal:
        return helpLegalFunc;
      case SettingContentType.refer:
        return referralFunc;
      case SettingContentType.aboutUs:
        return aboutUsFunc;
      case SettingContentType.appearance:
        // Return an empty list since this section is no longer used
        return [];
    }
  }

  Widget _buildListItem(BuildContext context, SettingFunctionsViewData item) {
    // Special handling for toggle theme function
    if (item.function == SettingFunction.toggleTheme) {
      return PullDownButton(
        position: PullDownMenuPosition.automatic,
        buttonAnchor: PullDownMenuAnchor.end,
        menuOffset: -10,
        routeTheme: PullDownMenuRouteTheme(
          width: 194.w,
          accessibilityWidth: 12.w,
          borderRadius: BorderRadius.circular(16.r),
          backgroundColor: context.colorScheme.mainNeutral,
          shadow: BoxShadow(
            color: context.colorScheme.mainPrimary.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ),
        buttonBuilder: (context, onPressed) => CupertinoButton(
          padding: EdgeInsets.zero,
          pressedOpacity: 1.0,
          onPressed: onPressed,
          child: Padding(
            padding: _getPadding(context),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildIcon(context, item.icon),
                AppConstants.kSpacingItemW12,
                Text(
                  item.title,
                  style: _getTextStyle(context),
                ),
                const Spacer(),
                CommonText(
                  GetIt.instance.get<ThemeNotifier>().themeName,
                  style: TextStyle(
                    fontSize: context.isTablet ? 14 : 14.sp,
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.mainBlue,
                  ),
                ),
                AppConstants.kSpacingItemW8,
                SvgPicture.asset(
                  Assets.icons.icUpDownArrow,
                  width: context.isTablet ? 20 : 20.w,
                  height: context.isTablet ? 20 : 20.w,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainGray.withOpacity(0.6),
                    BlendMode.srcIn,
                  ),
                ),
                AppConstants.kSpacingItemW2,
              ],
            ),
          ),
        ),
        itemBuilder: (context) => [
          PullDownMenuItem(
            title: S.current.system,
            itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
                color: GetIt.instance.get<ThemeNotifier>().value.id == 0
                    ? context.colorScheme.mainBlue
                    : context.colorScheme.mainPrimary,
              ),
            ),
            iconWidget: SvgPicture.asset(
              Assets.icons.icSystemSetting,
              width: 20.w,
              height: 20.h,
              colorFilter: ColorFilter.mode(
                GetIt.instance.get<ThemeNotifier>().value.id == 0
                    ? context.colorScheme.mainBlue
                    : context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
            onTap: () {
              final themeNotifier = GetIt.instance.get<ThemeNotifier>();
              themeNotifier.switchToSystemTheme();
            },
          ),
          PullDownMenuItem(
            itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
                color: GetIt.instance.get<ThemeNotifier>().value.id == 2
                    ? context.colorScheme.mainBlue
                    : context.colorScheme.mainPrimary,
              ),
            ),
            title: S.current.dark,
            iconWidget: SvgPicture.asset(
              Assets.icons.icDarkSetting,
              width: 20.w,
              height: 20.h,
              colorFilter: ColorFilter.mode(
                GetIt.instance.get<ThemeNotifier>().value.id == 2
                    ? context.colorScheme.mainBlue
                    : context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
            onTap: () {
              final themeNotifier = GetIt.instance.get<ThemeNotifier>();
              themeNotifier.switchToDarkTheme();
            },
          ),
          PullDownMenuItem(
            itemTheme: PullDownMenuItemTheme(
              textStyle: TextStyle(
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
                color: GetIt.instance.get<ThemeNotifier>().value.id == 1
                    ? context.colorScheme.mainBlue
                    : context.colorScheme.mainPrimary,
              ),
            ),
            title: S.current.light,
            iconWidget: SvgPicture.asset(
              Assets.icons.icLightSetting,
              width: 20.w,
              height: 20.h,
              colorFilter: ColorFilter.mode(
                GetIt.instance.get<ThemeNotifier>().value.id == 1
                    ? context.colorScheme.mainBlue
                    : context.colorScheme.mainPrimary,
                BlendMode.srcIn,
              ),
            ),
            onTap: () {
              final themeNotifier = GetIt.instance.get<ThemeNotifier>();
              themeNotifier.switchToLightTheme();
            },
          ),
        ],
      );
    }
    // Regular list item
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => _handleSettingTap(
          context, item.function, GetIt.instance.get<SettingCubit>()),
      child: Padding(
        padding: _getPadding(context),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildIcon(context, item.icon),
            AppConstants.kSpacingItemW12,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Stack(
                        clipBehavior: Clip.none,
                        children: [
                          Text(
                            item.title,
                            style: _getTextStyle(context),
                          ),
                          if (item.subIcon != null) ...[
                            Positioned(
                              top: context.isTablet ? -4 : -4.h,
                              right: context.isTablet ? -32 : -32.w,
                              child: SvgPicture.asset(
                                item.subIcon!,
                                width: context.isTablet ? 26 : 26.w,
                                height: context.isTablet ? 26 : 26.h,
                                colorFilter:
                                    item.function == SettingFunction.discord
                                        ? null
                                        : ColorFilter.mode(
                                            context.colorScheme.mainPrimary,
                                            BlendMode.srcIn,
                                          ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (item.function == SettingFunction.credits) ...[
              trailingContent ?? const SizedBox.shrink(),
            ],
            _buildTrailingWidget(context, item),
          ],
        ),
      ),
    );
  }

  EdgeInsets _getPadding(BuildContext context) {
    if (context.isLandscape) {
      return EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 5.w,
      );
    } else if (context.isTablet) {
      return const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 10,
      );
    } else {
      return EdgeInsets.only(
        left: 12.w,
        right: 8.w,
        top: 10.h,
        bottom: 10.h,
      );
    }
  }

  Widget _buildIcon(BuildContext context, String iconPath) {
    return SvgPicture.asset(
      iconPath,
      width: context.isTablet ? 24 : 24.w,
      height: context.isTablet ? 24 : 24.h,
      colorFilter: ColorFilter.mode(
        context.colorScheme.mainPrimary,
        BlendMode.srcIn,
      ),
    );
  }

  TextStyle _getTextStyle(BuildContext context) {
    return TextStyle(
      fontSize: context.isTablet ? 16 : 14.sp,
      fontWeight: FontWeight.w400,
      color: context.colorScheme.mainPrimary,
    );
  }

  Widget _buildTrailingWidget(
      BuildContext context, SettingFunctionsViewData item) {
    return SvgPicture.asset(
      isReverseView()
          ? Assets.icons.icSettingArrowLeft
          : Assets.icons.icSettingArrowRight,
      width: context.isTablet ? 28 : 24.w,
      height: context.isTablet ? 28 : 24.w,
      colorFilter: ColorFilter.mode(
        context.colorScheme.mainGray.withOpacity(0.6),
        BlendMode.srcIn,
      ),
    );
  }

  void _handleSettingTap(
      BuildContext context, SettingFunction function, SettingCubit cubit) {
    onClickFunc?.call(function);
    switch (function) {
      case SettingFunction.termOfUse:
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) =>
                  const TermOfUsePage(url: AppConstants.termUrl)),
        );
        break;
      case SettingFunction.privacyPolicy:
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) =>
                  const PrivacyPolicyPage(url: AppConstants.privacyURL)),
        );
        break;
      case SettingFunction.customNoteTabs:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CustomTabSettingPage()),
        );
        break;
      case SettingFunction.notifications:
        cubit.onNotifications();
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const NotificationPage()),
        );
        break;
      case SettingFunction.rate:
        cubit.onRateApp();
        _openAppStoreRating();
        break;
      case SettingFunction.contactUs:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ContactSupportPage()),
        );
        break;
      case SettingFunction.suggestFeatures:
        _launchNoteXAiWeb();
        break;
      case SettingFunction.account:
        cubit.onAccount();
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const AccountPage()));
        break;
      case SettingFunction.whatsNew:
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => WhatsNewPage(
                  content: GetIt.instance.get<AppCubit>().whatNewsContent)),
        );
        break;
      case SettingFunction.discord:
        cubit.onDiscord();
        _launchDiscord();
        break;
      case SettingFunction.syncNotes:
        cubit.onAccessWeb();
        _launchNoteXWeb();
        break;
      case SettingFunction.credits:
        break;
      case SettingFunction.referral:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ReferralPage()),
        );
        cubit.onReferral();
        break;
      case SettingFunction.enterReferralCode:
        break;
      case SettingFunction.aboutUs:
        Navigator.push(context,
            MaterialPageRoute(builder: (context) => const AboutUsPage()));
        break;
      case SettingFunction.syncWatch:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const WatchSyncPage()),
        );
        break;
      case SettingFunction.toggleTheme:
        break;
      case SettingFunction.audioFile:
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AudioLocalPage()),
        );
        break;
      default:
        break;
    }
  }

  void _openAppStoreRating() {
    InAppReview.instance.openStoreListing(appStoreId: AppConstants.appStoreId);
  }

  Future<void> _launchNoteXAiWeb() async {
    const url = 'https://notex-ai.canny.io/';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      CommonDialogs.showToast(S.current.not_open_web);
    }
  }

  Future<void> _launchNoteXWeb() async {
    const url = 'https://notexapp.com/';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      CommonDialogs.showToast(S.current.not_open_web);
    }
  }

  Future<void> _launchDiscord() async {
    const url = 'https://discord.gg/C4zQEJMNxR';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      CommonDialogs.showToast(S.current.not_open_web);
    }
  }
}

class SettingFunctionsViewData {
  final String icon;
  final String title;
  final String? subIcon;
  final SettingFunction function;

  const SettingFunctionsViewData({
    required this.icon,
    required this.title,
    this.subIcon,
    required this.function,
  });

  factory SettingFunctionsViewData.fromMap(Map<String, dynamic> map) {
    return SettingFunctionsViewData(
      icon: map['icon'] as String,
      title: map['title'] as String,
      subIcon: map['subIcon'] as String,
      function: map['function'] as SettingFunction,
    );
  }

  Map<String, dynamic> toMap() {
    return {'icon': icon, 'title': title, 'function': function};
  }
}

List<SettingFunctionsViewData> get communityFeedbackFunctionList {
  return [
    SettingFunctionsViewData(
      icon: Assets.icons.icStarRate,
      title: S.current.rate_us_on_store,
      function: SettingFunction.rate,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icDiscord,
      title: S.current.join_discord,
      subIcon: Assets.icons.icNew,
      function: SettingFunction.discord,
    ),
  ];
}

List<SettingFunctionsViewData> get quickAccessFunctionList {
  final appCubit = GetIt.instance.get<AppCubit>();

  return [
    SettingFunctionsViewData(
      icon: GetIt.instance.get<ThemeNotifier>().themeIcon,
      title: S.current.appearance,
      function: SettingFunction.toggleTheme,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icSettingCustomTab,
      title: S.current.custom_note_tabs,
      function: SettingFunction.customNoteTabs,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icBell,
      title: S.current.recording_schedule,
      function: SettingFunction.notifications,
    ),
    SettingFunctionsViewData(
      icon: appCubit.isReverseView
          ? Assets.icons.icFlipSyncNotes
          : Assets.icons.icSyncNotes,
      title: S.current.access_notex_web,
      function: SettingFunction.syncNotes,
    ),
    if (!(appCubit.isTablet || Platform.isAndroid)) ...[
      SettingFunctionsViewData(
        icon: Assets.icons.icAppleWatch,
        title: S.current.sync_from_watch,
        function: SettingFunction.syncWatch,
      ),
    ],
    SettingFunctionsViewData(
      icon: Assets.icons.icFolderSetting,
      title: S.current.manage_recordings,
      function: SettingFunction.audioFile,
    ),
  ];
}

final List<SettingFunctionsViewData> accountFunc = [
  SettingFunctionsViewData(
    icon: Assets.icons.icSettingAccount,
    title: S.current.account,
    function: SettingFunction.account,
  ),
];

List<SettingFunctionsViewData> get loginFunc {
  final appCubit = GetIt.instance.get<AppCubit>();
  return [
    SettingFunctionsViewData(
      icon: appCubit.isReverseView
          ? Assets.icons.icFlipDeleteAccount
          : Assets.icons.icDeleteAccount,
      title: S.current.delete_account,
      function: SettingFunction.deleteAccount,
    ),
    SettingFunctionsViewData(
      icon: appCubit.isReverseView
          ? Assets.icons.icFlipLogOut
          : Assets.icons.icLogout,
      title: S.current.logout,
      function: SettingFunction.logout,
    ),
  ];
}

List<SettingFunctionsViewData> get helpLegalFunc {
  return [
    SettingFunctionsViewData(
      icon: Assets.icons.icSettingContactUs,
      title: S.current.contact_support,
      function: SettingFunction.contactUs,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icSettingAboutUs,
      title: S.current.about_us,
      function: SettingFunction.aboutUs,
    ),
  ];
}

List<SettingFunctionsViewData> get referralFunc {
  return [
    SettingFunctionsViewData(
      icon: Assets.icons.icReferralSetting,
      title: S.current.referral,
      function: SettingFunction.referral,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icGiftSetting,
      title: S.current.enter_referral_code,
      function: SettingFunction.enterReferralCode,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icCoinCredits,
      title: S.current.referral_credits,
      function: SettingFunction.credits,
    ),
  ];
}

List<SettingFunctionsViewData> get aboutUsFunc {
  return [
    SettingFunctionsViewData(
      icon: Assets.icons.icWhatsNewSetting,
      title: S.current.whats_new,
      function: SettingFunction.whatsNew,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icDocumentText,
      title: S.current.terms_of_use,
      function: SettingFunction.termOfUse,
    ),
    SettingFunctionsViewData(
      icon: Assets.icons.icShieldTick,
      title: S.current.privacy_policy,
      function: SettingFunction.privacyPolicy,
    ),
  ];
}

// Keeping the enum but removing the unused function
// This section has been moved to quickAccessFunctionList
