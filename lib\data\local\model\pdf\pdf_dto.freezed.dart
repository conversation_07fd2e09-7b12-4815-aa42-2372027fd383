// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pdf_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PdfDto _$PdfDtoFromJson(Map<String, dynamic> json) {
  return _PdfDto.fromJson(json);
}

/// @nodoc
mixin _$PdfDto {
  @JsonKey(name: 'pdf_url')
  String? get pdfUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'note_id')
  String? get noteId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PdfDtoCopyWith<PdfDto> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PdfDtoCopyWith<$Res> {
  factory $PdfDtoCopyWith(PdfDto value, $Res Function(PdfDto) then) =
      _$PdfDtoCopyWithImpl<$Res, PdfDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'pdf_url') String? pdfUrl,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class _$PdfDtoCopyWithImpl<$Res, $Val extends PdfDto>
    implements $PdfDtoCopyWith<$Res> {
  _$PdfDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pdfUrl = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_value.copyWith(
      pdfUrl: freezed == pdfUrl
          ? _value.pdfUrl
          : pdfUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PdfDtoImplCopyWith<$Res> implements $PdfDtoCopyWith<$Res> {
  factory _$$PdfDtoImplCopyWith(
          _$PdfDtoImpl value, $Res Function(_$PdfDtoImpl) then) =
      __$$PdfDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'pdf_url') String? pdfUrl,
      @JsonKey(name: 'note_id') String? noteId});
}

/// @nodoc
class __$$PdfDtoImplCopyWithImpl<$Res>
    extends _$PdfDtoCopyWithImpl<$Res, _$PdfDtoImpl>
    implements _$$PdfDtoImplCopyWith<$Res> {
  __$$PdfDtoImplCopyWithImpl(
      _$PdfDtoImpl _value, $Res Function(_$PdfDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pdfUrl = freezed,
    Object? noteId = freezed,
  }) {
    return _then(_$PdfDtoImpl(
      pdfUrl: freezed == pdfUrl
          ? _value.pdfUrl
          : pdfUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      noteId: freezed == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PdfDtoImpl implements _PdfDto {
  const _$PdfDtoImpl(
      {@JsonKey(name: 'pdf_url') this.pdfUrl,
      @JsonKey(name: 'note_id') this.noteId});

  factory _$PdfDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PdfDtoImplFromJson(json);

  @override
  @JsonKey(name: 'pdf_url')
  final String? pdfUrl;
  @override
  @JsonKey(name: 'note_id')
  final String? noteId;

  @override
  String toString() {
    return 'PdfDto(pdfUrl: $pdfUrl, noteId: $noteId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PdfDtoImpl &&
            (identical(other.pdfUrl, pdfUrl) || other.pdfUrl == pdfUrl) &&
            (identical(other.noteId, noteId) || other.noteId == noteId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, pdfUrl, noteId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PdfDtoImplCopyWith<_$PdfDtoImpl> get copyWith =>
      __$$PdfDtoImplCopyWithImpl<_$PdfDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PdfDtoImplToJson(
      this,
    );
  }
}

abstract class _PdfDto implements PdfDto {
  const factory _PdfDto(
      {@JsonKey(name: 'pdf_url') final String? pdfUrl,
      @JsonKey(name: 'note_id') final String? noteId}) = _$PdfDtoImpl;

  factory _PdfDto.fromJson(Map<String, dynamic> json) = _$PdfDtoImpl.fromJson;

  @override
  @JsonKey(name: 'pdf_url')
  String? get pdfUrl;
  @override
  @JsonKey(name: 'note_id')
  String? get noteId;
  @override
  @JsonKey(ignore: true)
  _$$PdfDtoImplCopyWith<_$PdfDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
