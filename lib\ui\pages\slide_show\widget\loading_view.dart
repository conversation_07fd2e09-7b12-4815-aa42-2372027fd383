import 'package:flutter/cupertino.dart';

class LoadingView extends StatelessWidget {
  final ValueNotifier<int> downloadProgress;
  const LoadingView({Key? key, required this.downloadProgress})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ValueListenableBuilder<int>(
        valueListenable: downloadProgress,
        builder: (context, percent, _) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CupertinoActivityIndicator(),
              const SizedBox(height: 12),
              percent > 0
                  ? Text('$percent%', style: const TextStyle(fontSize: 16))
                  : const SizedBox.shrink(),
            ],
          );
        },
      ),
    );
  }
}
