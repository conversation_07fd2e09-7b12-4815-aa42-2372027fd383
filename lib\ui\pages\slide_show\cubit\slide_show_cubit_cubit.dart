import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/slide_show/cubit/slide_show_cubit_state.dart';

class SlideShowCubit extends BaseCubit<SlideShowCubitState> {
  SlideShowCubit(super.initialState);

  void setViewMode(ViewMode mode) => emit(state.copyWith(viewMode: mode));
  void setLoading(bool isLoading) => emit(state.copyWith(isLoading: isLoading));
  void setPdfLoaded(bool isPdfLoaded) =>
      emit(state.copyWith(isPdfLoaded: isPdfLoaded));
  void setHorizontalCurrentPage(int page) =>
      emit(state.copyWith(horizontalCurrentPage: page));
  void setVerticalCurrentPage(int page) =>
      emit(state.copyWith(verticalCurrentPage: page));
  void setHorizontalTotalPages(int totalPages) =>
      emit(state.copyWith(horizontalTotalPages: totalPages));
  void setVerticalTotalPages(int totalPages) =>
      emit(state.copyWith(verticalTotalPages: totalPages));
}
