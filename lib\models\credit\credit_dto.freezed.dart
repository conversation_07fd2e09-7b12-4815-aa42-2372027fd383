// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CreditDto _$CreditDtoFromJson(Map<String, dynamic> json) {
  return _CreditDto.fromJson(json);
}

/// @nodoc
mixin _$CreditDto {
  @JsonKey(name: 'youtubeCredit')
  int get youtubeCredit => throw _privateConstructorUsedError;
  @JsonKey(name: 'webCredit')
  int get webCredit => throw _privateConstructorUsedError;
  @JsonKey(name: 'audioCredit')
  int get audioCredit => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_type')
  String get userType => throw _privateConstructorUsedError;
  @JsonKey(name: 'reward_credits')
  int get rewardCredits => throw _privateConstructorUsedError;
  @JsonKey(name: 'shortsCredit')
  int get shortsCredit => throw _privateConstructorUsedError;
  @JsonKey(name: 'slideCredit')
  int get slideCredit => throw _privateConstructorUsedError;
  @JsonKey(name: 'documentCredit')
  int get documentCredit => throw _privateConstructorUsedError;
  @JsonKey(name: 'activePurchasePlan')
  ActivePurchasePlan? get activePurchasePlan =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreditDtoCopyWith<CreditDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditDtoCopyWith<$Res> {
  factory $CreditDtoCopyWith(CreditDto value, $Res Function(CreditDto) then) =
      _$CreditDtoCopyWithImpl<$Res, CreditDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'youtubeCredit') int youtubeCredit,
      @JsonKey(name: 'webCredit') int webCredit,
      @JsonKey(name: 'audioCredit') int audioCredit,
      @JsonKey(name: 'user_type') String userType,
      @JsonKey(name: 'reward_credits') int rewardCredits,
      @JsonKey(name: 'shortsCredit') int shortsCredit,
      @JsonKey(name: 'slideCredit') int slideCredit,
      @JsonKey(name: 'documentCredit') int documentCredit,
      @JsonKey(name: 'activePurchasePlan')
      ActivePurchasePlan? activePurchasePlan});

  $ActivePurchasePlanCopyWith<$Res>? get activePurchasePlan;
}

/// @nodoc
class _$CreditDtoCopyWithImpl<$Res, $Val extends CreditDto>
    implements $CreditDtoCopyWith<$Res> {
  _$CreditDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? youtubeCredit = null,
    Object? webCredit = null,
    Object? audioCredit = null,
    Object? userType = null,
    Object? rewardCredits = null,
    Object? shortsCredit = null,
    Object? slideCredit = null,
    Object? documentCredit = null,
    Object? activePurchasePlan = freezed,
  }) {
    return _then(_value.copyWith(
      youtubeCredit: null == youtubeCredit
          ? _value.youtubeCredit
          : youtubeCredit // ignore: cast_nullable_to_non_nullable
              as int,
      webCredit: null == webCredit
          ? _value.webCredit
          : webCredit // ignore: cast_nullable_to_non_nullable
              as int,
      audioCredit: null == audioCredit
          ? _value.audioCredit
          : audioCredit // ignore: cast_nullable_to_non_nullable
              as int,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      rewardCredits: null == rewardCredits
          ? _value.rewardCredits
          : rewardCredits // ignore: cast_nullable_to_non_nullable
              as int,
      shortsCredit: null == shortsCredit
          ? _value.shortsCredit
          : shortsCredit // ignore: cast_nullable_to_non_nullable
              as int,
      slideCredit: null == slideCredit
          ? _value.slideCredit
          : slideCredit // ignore: cast_nullable_to_non_nullable
              as int,
      documentCredit: null == documentCredit
          ? _value.documentCredit
          : documentCredit // ignore: cast_nullable_to_non_nullable
              as int,
      activePurchasePlan: freezed == activePurchasePlan
          ? _value.activePurchasePlan
          : activePurchasePlan // ignore: cast_nullable_to_non_nullable
              as ActivePurchasePlan?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ActivePurchasePlanCopyWith<$Res>? get activePurchasePlan {
    if (_value.activePurchasePlan == null) {
      return null;
    }

    return $ActivePurchasePlanCopyWith<$Res>(_value.activePurchasePlan!,
        (value) {
      return _then(_value.copyWith(activePurchasePlan: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreditDtoImplCopyWith<$Res>
    implements $CreditDtoCopyWith<$Res> {
  factory _$$CreditDtoImplCopyWith(
          _$CreditDtoImpl value, $Res Function(_$CreditDtoImpl) then) =
      __$$CreditDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'youtubeCredit') int youtubeCredit,
      @JsonKey(name: 'webCredit') int webCredit,
      @JsonKey(name: 'audioCredit') int audioCredit,
      @JsonKey(name: 'user_type') String userType,
      @JsonKey(name: 'reward_credits') int rewardCredits,
      @JsonKey(name: 'shortsCredit') int shortsCredit,
      @JsonKey(name: 'slideCredit') int slideCredit,
      @JsonKey(name: 'documentCredit') int documentCredit,
      @JsonKey(name: 'activePurchasePlan')
      ActivePurchasePlan? activePurchasePlan});

  @override
  $ActivePurchasePlanCopyWith<$Res>? get activePurchasePlan;
}

/// @nodoc
class __$$CreditDtoImplCopyWithImpl<$Res>
    extends _$CreditDtoCopyWithImpl<$Res, _$CreditDtoImpl>
    implements _$$CreditDtoImplCopyWith<$Res> {
  __$$CreditDtoImplCopyWithImpl(
      _$CreditDtoImpl _value, $Res Function(_$CreditDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? youtubeCredit = null,
    Object? webCredit = null,
    Object? audioCredit = null,
    Object? userType = null,
    Object? rewardCredits = null,
    Object? shortsCredit = null,
    Object? slideCredit = null,
    Object? documentCredit = null,
    Object? activePurchasePlan = freezed,
  }) {
    return _then(_$CreditDtoImpl(
      youtubeCredit: null == youtubeCredit
          ? _value.youtubeCredit
          : youtubeCredit // ignore: cast_nullable_to_non_nullable
              as int,
      webCredit: null == webCredit
          ? _value.webCredit
          : webCredit // ignore: cast_nullable_to_non_nullable
              as int,
      audioCredit: null == audioCredit
          ? _value.audioCredit
          : audioCredit // ignore: cast_nullable_to_non_nullable
              as int,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      rewardCredits: null == rewardCredits
          ? _value.rewardCredits
          : rewardCredits // ignore: cast_nullable_to_non_nullable
              as int,
      shortsCredit: null == shortsCredit
          ? _value.shortsCredit
          : shortsCredit // ignore: cast_nullable_to_non_nullable
              as int,
      slideCredit: null == slideCredit
          ? _value.slideCredit
          : slideCredit // ignore: cast_nullable_to_non_nullable
              as int,
      documentCredit: null == documentCredit
          ? _value.documentCredit
          : documentCredit // ignore: cast_nullable_to_non_nullable
              as int,
      activePurchasePlan: freezed == activePurchasePlan
          ? _value.activePurchasePlan
          : activePurchasePlan // ignore: cast_nullable_to_non_nullable
              as ActivePurchasePlan?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreditDtoImpl implements _CreditDto {
  const _$CreditDtoImpl(
      {@JsonKey(name: 'youtubeCredit') this.youtubeCredit = 0,
      @JsonKey(name: 'webCredit') this.webCredit = 0,
      @JsonKey(name: 'audioCredit') this.audioCredit = 0,
      @JsonKey(name: 'user_type') this.userType = 'free',
      @JsonKey(name: 'reward_credits') this.rewardCredits = 0,
      @JsonKey(name: 'shortsCredit') this.shortsCredit = 0,
      @JsonKey(name: 'slideCredit') this.slideCredit = 0,
      @JsonKey(name: 'documentCredit') this.documentCredit = 0,
      @JsonKey(name: 'activePurchasePlan') this.activePurchasePlan});

  factory _$CreditDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreditDtoImplFromJson(json);

  @override
  @JsonKey(name: 'youtubeCredit')
  final int youtubeCredit;
  @override
  @JsonKey(name: 'webCredit')
  final int webCredit;
  @override
  @JsonKey(name: 'audioCredit')
  final int audioCredit;
  @override
  @JsonKey(name: 'user_type')
  final String userType;
  @override
  @JsonKey(name: 'reward_credits')
  final int rewardCredits;
  @override
  @JsonKey(name: 'shortsCredit')
  final int shortsCredit;
  @override
  @JsonKey(name: 'slideCredit')
  final int slideCredit;
  @override
  @JsonKey(name: 'documentCredit')
  final int documentCredit;
  @override
  @JsonKey(name: 'activePurchasePlan')
  final ActivePurchasePlan? activePurchasePlan;

  @override
  String toString() {
    return 'CreditDto(youtubeCredit: $youtubeCredit, webCredit: $webCredit, audioCredit: $audioCredit, userType: $userType, rewardCredits: $rewardCredits, shortsCredit: $shortsCredit, slideCredit: $slideCredit, documentCredit: $documentCredit, activePurchasePlan: $activePurchasePlan)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditDtoImpl &&
            (identical(other.youtubeCredit, youtubeCredit) ||
                other.youtubeCredit == youtubeCredit) &&
            (identical(other.webCredit, webCredit) ||
                other.webCredit == webCredit) &&
            (identical(other.audioCredit, audioCredit) ||
                other.audioCredit == audioCredit) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.rewardCredits, rewardCredits) ||
                other.rewardCredits == rewardCredits) &&
            (identical(other.shortsCredit, shortsCredit) ||
                other.shortsCredit == shortsCredit) &&
            (identical(other.slideCredit, slideCredit) ||
                other.slideCredit == slideCredit) &&
            (identical(other.documentCredit, documentCredit) ||
                other.documentCredit == documentCredit) &&
            (identical(other.activePurchasePlan, activePurchasePlan) ||
                other.activePurchasePlan == activePurchasePlan));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      youtubeCredit,
      webCredit,
      audioCredit,
      userType,
      rewardCredits,
      shortsCredit,
      slideCredit,
      documentCredit,
      activePurchasePlan);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditDtoImplCopyWith<_$CreditDtoImpl> get copyWith =>
      __$$CreditDtoImplCopyWithImpl<_$CreditDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreditDtoImplToJson(
      this,
    );
  }
}

abstract class _CreditDto implements CreditDto {
  const factory _CreditDto(
      {@JsonKey(name: 'youtubeCredit') final int youtubeCredit,
      @JsonKey(name: 'webCredit') final int webCredit,
      @JsonKey(name: 'audioCredit') final int audioCredit,
      @JsonKey(name: 'user_type') final String userType,
      @JsonKey(name: 'reward_credits') final int rewardCredits,
      @JsonKey(name: 'shortsCredit') final int shortsCredit,
      @JsonKey(name: 'slideCredit') final int slideCredit,
      @JsonKey(name: 'documentCredit') final int documentCredit,
      @JsonKey(name: 'activePurchasePlan')
      final ActivePurchasePlan? activePurchasePlan}) = _$CreditDtoImpl;

  factory _CreditDto.fromJson(Map<String, dynamic> json) =
      _$CreditDtoImpl.fromJson;

  @override
  @JsonKey(name: 'youtubeCredit')
  int get youtubeCredit;
  @override
  @JsonKey(name: 'webCredit')
  int get webCredit;
  @override
  @JsonKey(name: 'audioCredit')
  int get audioCredit;
  @override
  @JsonKey(name: 'user_type')
  String get userType;
  @override
  @JsonKey(name: 'reward_credits')
  int get rewardCredits;
  @override
  @JsonKey(name: 'shortsCredit')
  int get shortsCredit;
  @override
  @JsonKey(name: 'slideCredit')
  int get slideCredit;
  @override
  @JsonKey(name: 'documentCredit')
  int get documentCredit;
  @override
  @JsonKey(name: 'activePurchasePlan')
  ActivePurchasePlan? get activePurchasePlan;
  @override
  @JsonKey(ignore: true)
  _$$CreditDtoImplCopyWith<_$CreditDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
