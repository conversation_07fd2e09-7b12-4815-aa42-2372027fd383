// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_template_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ChatTemplateModelAdapter extends TypeAdapter<ChatTemplateModel> {
  @override
  final int typeId = 23;

  @override
  ChatTemplateModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChatTemplateModel(
      id: fields[0] == null ? '' : fields[0] as String,
      name: fields[1] == null ? '' : fields[1] as String,
      prompt: fields[2] == null ? '' : fields[2] as String,
      type: fields[3] == null ? '' : fields[3] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ChatTemplateModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.prompt)
      ..writeByte(3)
      ..write(obj.type);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatTemplateModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
