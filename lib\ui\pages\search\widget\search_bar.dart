import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:note_x/lib.dart';

class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final bool isTablet;
  final bool isReverseView;
  final Function(String) onSearchChanged;
  final VoidCallback onCancelPressed;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.isTablet,
    required this.isReverseView,
    required this.onSearchChanged,
    required this.onCancelPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.symmetric(horizontal: isTablet ? 16 : 16.w, vertical: 8.h),
      child: Row(
        children: [
          // Search field container
          Expanded(
            child: Container(
              height: context.isTablet ? 40 : 40.h,
              decoration: BoxDecoration(
                color: context.colorScheme.mainNeutral,
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Row(
                children: [
                  // Search icon
                  Padding(
                    padding: EdgeInsets.only(left: isTablet ? 8 : 8.w),
                    child: SvgPicture.asset(
                      isReverseView
                          ? Assets.icons.icFlipSearchEnable
                          : Assets.icons.icSearchEnable,
                      width: isTablet ? 24 : 20.w,
                      height: isTablet ? 24 : 20.h,
                      colorFilter: ColorFilter.mode(
                          context.colorScheme.mainGray, BlendMode.srcIn),
                    ),
                  ),
                  // Search text field
                  Expanded(
                    child: CupertinoTextField(
                      padding: EdgeInsets.symmetric(
                        horizontal: 4.w,
                        vertical: 6.h,
                      ),
                      controller: controller,
                      focusNode: focusNode,
                      autofocus: false,
                      cursorColor: context.colorScheme.mainBlue,
                      onChanged: (value) {
                        if (value.endsWith('📌')) {
                          controller.clear();
                        } else {
                          onSearchChanged(value);
                        }
                      },
                      placeholder: S.current.search_in_files,
                      placeholderStyle: TextStyle(
                        color: context.colorScheme.mainGray.withOpacity(0.6),
                        fontSize: isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      style: TextStyle(
                        color: context.colorScheme.mainPrimary,
                        fontSize: isTablet ? 16 : 14.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      decoration: null,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Cancel button
          CupertinoButton(
            padding: EdgeInsets.only(left: 8.w),
            minSize: 0,
            onPressed: onCancelPressed,
            child: Text(
              S.current.cancel,
              style: TextStyle(
                color: context.colorScheme.mainBlue,
                fontSize: isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
