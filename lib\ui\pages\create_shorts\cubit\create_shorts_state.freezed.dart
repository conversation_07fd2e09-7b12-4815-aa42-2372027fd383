// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_shorts_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CreateShortsState {
  BackgroundVideoOneShotEvent get oneShotEvent =>
      throw _privateConstructorUsedError;
  BackgroundQuizVideoOneShotEvent get quizOneShotEvent =>
      throw _privateConstructorUsedError;
  List<BackgroundVideoDto> get listBackgroundVideos =>
      throw _privateConstructorUsedError;
  List<BackgroundQuizVideoDto> get listBackgroundQuizVideos =>
      throw _privateConstructorUsedError;
  bool get isCaptionEnabled => throw _privateConstructorUsedError;
  int get selectedDurationIndex => throw _privateConstructorUsedError;
  int get selectedVideoIndex => throw _privateConstructorUsedError;
  int get selectedQuizVideoIndex => throw _privateConstructorUsedError;
  bool get isPlaying => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CreateShortsStateCopyWith<CreateShortsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateShortsStateCopyWith<$Res> {
  factory $CreateShortsStateCopyWith(
          CreateShortsState value, $Res Function(CreateShortsState) then) =
      _$CreateShortsStateCopyWithImpl<$Res, CreateShortsState>;
  @useResult
  $Res call(
      {BackgroundVideoOneShotEvent oneShotEvent,
      BackgroundQuizVideoOneShotEvent quizOneShotEvent,
      List<BackgroundVideoDto> listBackgroundVideos,
      List<BackgroundQuizVideoDto> listBackgroundQuizVideos,
      bool isCaptionEnabled,
      int selectedDurationIndex,
      int selectedVideoIndex,
      int selectedQuizVideoIndex,
      bool isPlaying,
      String? error,
      bool isLoading});
}

/// @nodoc
class _$CreateShortsStateCopyWithImpl<$Res, $Val extends CreateShortsState>
    implements $CreateShortsStateCopyWith<$Res> {
  _$CreateShortsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? quizOneShotEvent = null,
    Object? listBackgroundVideos = null,
    Object? listBackgroundQuizVideos = null,
    Object? isCaptionEnabled = null,
    Object? selectedDurationIndex = null,
    Object? selectedVideoIndex = null,
    Object? selectedQuizVideoIndex = null,
    Object? isPlaying = null,
    Object? error = freezed,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as BackgroundVideoOneShotEvent,
      quizOneShotEvent: null == quizOneShotEvent
          ? _value.quizOneShotEvent
          : quizOneShotEvent // ignore: cast_nullable_to_non_nullable
              as BackgroundQuizVideoOneShotEvent,
      listBackgroundVideos: null == listBackgroundVideos
          ? _value.listBackgroundVideos
          : listBackgroundVideos // ignore: cast_nullable_to_non_nullable
              as List<BackgroundVideoDto>,
      listBackgroundQuizVideos: null == listBackgroundQuizVideos
          ? _value.listBackgroundQuizVideos
          : listBackgroundQuizVideos // ignore: cast_nullable_to_non_nullable
              as List<BackgroundQuizVideoDto>,
      isCaptionEnabled: null == isCaptionEnabled
          ? _value.isCaptionEnabled
          : isCaptionEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedDurationIndex: null == selectedDurationIndex
          ? _value.selectedDurationIndex
          : selectedDurationIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedVideoIndex: null == selectedVideoIndex
          ? _value.selectedVideoIndex
          : selectedVideoIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedQuizVideoIndex: null == selectedQuizVideoIndex
          ? _value.selectedQuizVideoIndex
          : selectedQuizVideoIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isPlaying: null == isPlaying
          ? _value.isPlaying
          : isPlaying // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateShortsStateImplCopyWith<$Res>
    implements $CreateShortsStateCopyWith<$Res> {
  factory _$$CreateShortsStateImplCopyWith(_$CreateShortsStateImpl value,
          $Res Function(_$CreateShortsStateImpl) then) =
      __$$CreateShortsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BackgroundVideoOneShotEvent oneShotEvent,
      BackgroundQuizVideoOneShotEvent quizOneShotEvent,
      List<BackgroundVideoDto> listBackgroundVideos,
      List<BackgroundQuizVideoDto> listBackgroundQuizVideos,
      bool isCaptionEnabled,
      int selectedDurationIndex,
      int selectedVideoIndex,
      int selectedQuizVideoIndex,
      bool isPlaying,
      String? error,
      bool isLoading});
}

/// @nodoc
class __$$CreateShortsStateImplCopyWithImpl<$Res>
    extends _$CreateShortsStateCopyWithImpl<$Res, _$CreateShortsStateImpl>
    implements _$$CreateShortsStateImplCopyWith<$Res> {
  __$$CreateShortsStateImplCopyWithImpl(_$CreateShortsStateImpl _value,
      $Res Function(_$CreateShortsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? quizOneShotEvent = null,
    Object? listBackgroundVideos = null,
    Object? listBackgroundQuizVideos = null,
    Object? isCaptionEnabled = null,
    Object? selectedDurationIndex = null,
    Object? selectedVideoIndex = null,
    Object? selectedQuizVideoIndex = null,
    Object? isPlaying = null,
    Object? error = freezed,
    Object? isLoading = null,
  }) {
    return _then(_$CreateShortsStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as BackgroundVideoOneShotEvent,
      quizOneShotEvent: null == quizOneShotEvent
          ? _value.quizOneShotEvent
          : quizOneShotEvent // ignore: cast_nullable_to_non_nullable
              as BackgroundQuizVideoOneShotEvent,
      listBackgroundVideos: null == listBackgroundVideos
          ? _value._listBackgroundVideos
          : listBackgroundVideos // ignore: cast_nullable_to_non_nullable
              as List<BackgroundVideoDto>,
      listBackgroundQuizVideos: null == listBackgroundQuizVideos
          ? _value._listBackgroundQuizVideos
          : listBackgroundQuizVideos // ignore: cast_nullable_to_non_nullable
              as List<BackgroundQuizVideoDto>,
      isCaptionEnabled: null == isCaptionEnabled
          ? _value.isCaptionEnabled
          : isCaptionEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedDurationIndex: null == selectedDurationIndex
          ? _value.selectedDurationIndex
          : selectedDurationIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedVideoIndex: null == selectedVideoIndex
          ? _value.selectedVideoIndex
          : selectedVideoIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedQuizVideoIndex: null == selectedQuizVideoIndex
          ? _value.selectedQuizVideoIndex
          : selectedQuizVideoIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isPlaying: null == isPlaying
          ? _value.isPlaying
          : isPlaying // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CreateShortsStateImpl implements _CreateShortsState {
  const _$CreateShortsStateImpl(
      {this.oneShotEvent = BackgroundVideoOneShotEvent.initial,
      this.quizOneShotEvent = BackgroundQuizVideoOneShotEvent.initial,
      final List<BackgroundVideoDto> listBackgroundVideos = const [],
      final List<BackgroundQuizVideoDto> listBackgroundQuizVideos = const [],
      this.isCaptionEnabled = true,
      this.selectedDurationIndex = 1,
      this.selectedVideoIndex = -1,
      this.selectedQuizVideoIndex = -1,
      this.isPlaying = false,
      this.error,
      this.isLoading = false})
      : _listBackgroundVideos = listBackgroundVideos,
        _listBackgroundQuizVideos = listBackgroundQuizVideos;

  @override
  @JsonKey()
  final BackgroundVideoOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final BackgroundQuizVideoOneShotEvent quizOneShotEvent;
  final List<BackgroundVideoDto> _listBackgroundVideos;
  @override
  @JsonKey()
  List<BackgroundVideoDto> get listBackgroundVideos {
    if (_listBackgroundVideos is EqualUnmodifiableListView)
      return _listBackgroundVideos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listBackgroundVideos);
  }

  final List<BackgroundQuizVideoDto> _listBackgroundQuizVideos;
  @override
  @JsonKey()
  List<BackgroundQuizVideoDto> get listBackgroundQuizVideos {
    if (_listBackgroundQuizVideos is EqualUnmodifiableListView)
      return _listBackgroundQuizVideos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listBackgroundQuizVideos);
  }

  @override
  @JsonKey()
  final bool isCaptionEnabled;
  @override
  @JsonKey()
  final int selectedDurationIndex;
  @override
  @JsonKey()
  final int selectedVideoIndex;
  @override
  @JsonKey()
  final int selectedQuizVideoIndex;
  @override
  @JsonKey()
  final bool isPlaying;
  @override
  final String? error;
  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'CreateShortsState(oneShotEvent: $oneShotEvent, quizOneShotEvent: $quizOneShotEvent, listBackgroundVideos: $listBackgroundVideos, listBackgroundQuizVideos: $listBackgroundQuizVideos, isCaptionEnabled: $isCaptionEnabled, selectedDurationIndex: $selectedDurationIndex, selectedVideoIndex: $selectedVideoIndex, selectedQuizVideoIndex: $selectedQuizVideoIndex, isPlaying: $isPlaying, error: $error, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateShortsStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.quizOneShotEvent, quizOneShotEvent) ||
                other.quizOneShotEvent == quizOneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._listBackgroundVideos, _listBackgroundVideos) &&
            const DeepCollectionEquality().equals(
                other._listBackgroundQuizVideos, _listBackgroundQuizVideos) &&
            (identical(other.isCaptionEnabled, isCaptionEnabled) ||
                other.isCaptionEnabled == isCaptionEnabled) &&
            (identical(other.selectedDurationIndex, selectedDurationIndex) ||
                other.selectedDurationIndex == selectedDurationIndex) &&
            (identical(other.selectedVideoIndex, selectedVideoIndex) ||
                other.selectedVideoIndex == selectedVideoIndex) &&
            (identical(other.selectedQuizVideoIndex, selectedQuizVideoIndex) ||
                other.selectedQuizVideoIndex == selectedQuizVideoIndex) &&
            (identical(other.isPlaying, isPlaying) ||
                other.isPlaying == isPlaying) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      quizOneShotEvent,
      const DeepCollectionEquality().hash(_listBackgroundVideos),
      const DeepCollectionEquality().hash(_listBackgroundQuizVideos),
      isCaptionEnabled,
      selectedDurationIndex,
      selectedVideoIndex,
      selectedQuizVideoIndex,
      isPlaying,
      error,
      isLoading);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateShortsStateImplCopyWith<_$CreateShortsStateImpl> get copyWith =>
      __$$CreateShortsStateImplCopyWithImpl<_$CreateShortsStateImpl>(
          this, _$identity);
}

abstract class _CreateShortsState implements CreateShortsState {
  const factory _CreateShortsState(
      {final BackgroundVideoOneShotEvent oneShotEvent,
      final BackgroundQuizVideoOneShotEvent quizOneShotEvent,
      final List<BackgroundVideoDto> listBackgroundVideos,
      final List<BackgroundQuizVideoDto> listBackgroundQuizVideos,
      final bool isCaptionEnabled,
      final int selectedDurationIndex,
      final int selectedVideoIndex,
      final int selectedQuizVideoIndex,
      final bool isPlaying,
      final String? error,
      final bool isLoading}) = _$CreateShortsStateImpl;

  @override
  BackgroundVideoOneShotEvent get oneShotEvent;
  @override
  BackgroundQuizVideoOneShotEvent get quizOneShotEvent;
  @override
  List<BackgroundVideoDto> get listBackgroundVideos;
  @override
  List<BackgroundQuizVideoDto> get listBackgroundQuizVideos;
  @override
  bool get isCaptionEnabled;
  @override
  int get selectedDurationIndex;
  @override
  int get selectedVideoIndex;
  @override
  int get selectedQuizVideoIndex;
  @override
  bool get isPlaying;
  @override
  String? get error;
  @override
  bool get isLoading;
  @override
  @JsonKey(ignore: true)
  _$$CreateShortsStateImplCopyWith<_$CreateShortsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
