// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_page_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$AccountPageState {
  AccountOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;
  UpgradeSubscriptionPlan get selectedPlan =>
      throw _privateConstructorUsedError;
  UpgradeSubscriptionPlan get currentPlan => throw _privateConstructorUsedError;
  bool get showSubscriptionOptions => throw _privateConstructorUsedError;
  Map<UpgradeSubscriptionPlan, double> get subscriptionPrices =>
      throw _privateConstructorUsedError;
  Map<String, DateTime>? get subscriptionInfo =>
      throw _privateConstructorUsedError;
  List<UpgradeSubscriptionPlan> get availableUpdatePlans =>
      throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AccountPageStateCopyWith<AccountPageState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountPageStateCopyWith<$Res> {
  factory $AccountPageStateCopyWith(
          AccountPageState value, $Res Function(AccountPageState) then) =
      _$AccountPageStateCopyWithImpl<$Res, AccountPageState>;
  @useResult
  $Res call(
      {AccountOneShotEvent oneShotEvent,
      UpgradeSubscriptionPlan selectedPlan,
      UpgradeSubscriptionPlan currentPlan,
      bool showSubscriptionOptions,
      Map<UpgradeSubscriptionPlan, double> subscriptionPrices,
      Map<String, DateTime>? subscriptionInfo,
      List<UpgradeSubscriptionPlan> availableUpdatePlans,
      bool isLoading});
}

/// @nodoc
class _$AccountPageStateCopyWithImpl<$Res, $Val extends AccountPageState>
    implements $AccountPageStateCopyWith<$Res> {
  _$AccountPageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? selectedPlan = null,
    Object? currentPlan = null,
    Object? showSubscriptionOptions = null,
    Object? subscriptionPrices = null,
    Object? subscriptionInfo = freezed,
    Object? availableUpdatePlans = null,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as AccountOneShotEvent,
      selectedPlan: null == selectedPlan
          ? _value.selectedPlan
          : selectedPlan // ignore: cast_nullable_to_non_nullable
              as UpgradeSubscriptionPlan,
      currentPlan: null == currentPlan
          ? _value.currentPlan
          : currentPlan // ignore: cast_nullable_to_non_nullable
              as UpgradeSubscriptionPlan,
      showSubscriptionOptions: null == showSubscriptionOptions
          ? _value.showSubscriptionOptions
          : showSubscriptionOptions // ignore: cast_nullable_to_non_nullable
              as bool,
      subscriptionPrices: null == subscriptionPrices
          ? _value.subscriptionPrices
          : subscriptionPrices // ignore: cast_nullable_to_non_nullable
              as Map<UpgradeSubscriptionPlan, double>,
      subscriptionInfo: freezed == subscriptionInfo
          ? _value.subscriptionInfo
          : subscriptionInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, DateTime>?,
      availableUpdatePlans: null == availableUpdatePlans
          ? _value.availableUpdatePlans
          : availableUpdatePlans // ignore: cast_nullable_to_non_nullable
              as List<UpgradeSubscriptionPlan>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountPageStateImplCopyWith<$Res>
    implements $AccountPageStateCopyWith<$Res> {
  factory _$$AccountPageStateImplCopyWith(_$AccountPageStateImpl value,
          $Res Function(_$AccountPageStateImpl) then) =
      __$$AccountPageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AccountOneShotEvent oneShotEvent,
      UpgradeSubscriptionPlan selectedPlan,
      UpgradeSubscriptionPlan currentPlan,
      bool showSubscriptionOptions,
      Map<UpgradeSubscriptionPlan, double> subscriptionPrices,
      Map<String, DateTime>? subscriptionInfo,
      List<UpgradeSubscriptionPlan> availableUpdatePlans,
      bool isLoading});
}

/// @nodoc
class __$$AccountPageStateImplCopyWithImpl<$Res>
    extends _$AccountPageStateCopyWithImpl<$Res, _$AccountPageStateImpl>
    implements _$$AccountPageStateImplCopyWith<$Res> {
  __$$AccountPageStateImplCopyWithImpl(_$AccountPageStateImpl _value,
      $Res Function(_$AccountPageStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? selectedPlan = null,
    Object? currentPlan = null,
    Object? showSubscriptionOptions = null,
    Object? subscriptionPrices = null,
    Object? subscriptionInfo = freezed,
    Object? availableUpdatePlans = null,
    Object? isLoading = null,
  }) {
    return _then(_$AccountPageStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as AccountOneShotEvent,
      selectedPlan: null == selectedPlan
          ? _value.selectedPlan
          : selectedPlan // ignore: cast_nullable_to_non_nullable
              as UpgradeSubscriptionPlan,
      currentPlan: null == currentPlan
          ? _value.currentPlan
          : currentPlan // ignore: cast_nullable_to_non_nullable
              as UpgradeSubscriptionPlan,
      showSubscriptionOptions: null == showSubscriptionOptions
          ? _value.showSubscriptionOptions
          : showSubscriptionOptions // ignore: cast_nullable_to_non_nullable
              as bool,
      subscriptionPrices: null == subscriptionPrices
          ? _value._subscriptionPrices
          : subscriptionPrices // ignore: cast_nullable_to_non_nullable
              as Map<UpgradeSubscriptionPlan, double>,
      subscriptionInfo: freezed == subscriptionInfo
          ? _value._subscriptionInfo
          : subscriptionInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, DateTime>?,
      availableUpdatePlans: null == availableUpdatePlans
          ? _value._availableUpdatePlans
          : availableUpdatePlans // ignore: cast_nullable_to_non_nullable
              as List<UpgradeSubscriptionPlan>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AccountPageStateImpl implements _AccountPageState {
  const _$AccountPageStateImpl(
      {this.oneShotEvent = AccountOneShotEvent.none,
      this.selectedPlan = UpgradeSubscriptionPlan.none,
      this.currentPlan = UpgradeSubscriptionPlan.none,
      this.showSubscriptionOptions = false,
      final Map<UpgradeSubscriptionPlan, double> subscriptionPrices = const {},
      final Map<String, DateTime>? subscriptionInfo,
      final List<UpgradeSubscriptionPlan> availableUpdatePlans = const [],
      this.isLoading = false})
      : _subscriptionPrices = subscriptionPrices,
        _subscriptionInfo = subscriptionInfo,
        _availableUpdatePlans = availableUpdatePlans;

  @override
  @JsonKey()
  final AccountOneShotEvent oneShotEvent;
  @override
  @JsonKey()
  final UpgradeSubscriptionPlan selectedPlan;
  @override
  @JsonKey()
  final UpgradeSubscriptionPlan currentPlan;
  @override
  @JsonKey()
  final bool showSubscriptionOptions;
  final Map<UpgradeSubscriptionPlan, double> _subscriptionPrices;
  @override
  @JsonKey()
  Map<UpgradeSubscriptionPlan, double> get subscriptionPrices {
    if (_subscriptionPrices is EqualUnmodifiableMapView)
      return _subscriptionPrices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_subscriptionPrices);
  }

  final Map<String, DateTime>? _subscriptionInfo;
  @override
  Map<String, DateTime>? get subscriptionInfo {
    final value = _subscriptionInfo;
    if (value == null) return null;
    if (_subscriptionInfo is EqualUnmodifiableMapView) return _subscriptionInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<UpgradeSubscriptionPlan> _availableUpdatePlans;
  @override
  @JsonKey()
  List<UpgradeSubscriptionPlan> get availableUpdatePlans {
    if (_availableUpdatePlans is EqualUnmodifiableListView)
      return _availableUpdatePlans;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableUpdatePlans);
  }

  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'AccountPageState(oneShotEvent: $oneShotEvent, selectedPlan: $selectedPlan, currentPlan: $currentPlan, showSubscriptionOptions: $showSubscriptionOptions, subscriptionPrices: $subscriptionPrices, subscriptionInfo: $subscriptionInfo, availableUpdatePlans: $availableUpdatePlans, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountPageStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            (identical(other.selectedPlan, selectedPlan) ||
                other.selectedPlan == selectedPlan) &&
            (identical(other.currentPlan, currentPlan) ||
                other.currentPlan == currentPlan) &&
            (identical(
                    other.showSubscriptionOptions, showSubscriptionOptions) ||
                other.showSubscriptionOptions == showSubscriptionOptions) &&
            const DeepCollectionEquality()
                .equals(other._subscriptionPrices, _subscriptionPrices) &&
            const DeepCollectionEquality()
                .equals(other._subscriptionInfo, _subscriptionInfo) &&
            const DeepCollectionEquality()
                .equals(other._availableUpdatePlans, _availableUpdatePlans) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      selectedPlan,
      currentPlan,
      showSubscriptionOptions,
      const DeepCollectionEquality().hash(_subscriptionPrices),
      const DeepCollectionEquality().hash(_subscriptionInfo),
      const DeepCollectionEquality().hash(_availableUpdatePlans),
      isLoading);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountPageStateImplCopyWith<_$AccountPageStateImpl> get copyWith =>
      __$$AccountPageStateImplCopyWithImpl<_$AccountPageStateImpl>(
          this, _$identity);
}

abstract class _AccountPageState implements AccountPageState {
  const factory _AccountPageState(
      {final AccountOneShotEvent oneShotEvent,
      final UpgradeSubscriptionPlan selectedPlan,
      final UpgradeSubscriptionPlan currentPlan,
      final bool showSubscriptionOptions,
      final Map<UpgradeSubscriptionPlan, double> subscriptionPrices,
      final Map<String, DateTime>? subscriptionInfo,
      final List<UpgradeSubscriptionPlan> availableUpdatePlans,
      final bool isLoading}) = _$AccountPageStateImpl;

  @override
  AccountOneShotEvent get oneShotEvent;
  @override
  UpgradeSubscriptionPlan get selectedPlan;
  @override
  UpgradeSubscriptionPlan get currentPlan;
  @override
  bool get showSubscriptionOptions;
  @override
  Map<UpgradeSubscriptionPlan, double> get subscriptionPrices;
  @override
  Map<String, DateTime>? get subscriptionInfo;
  @override
  List<UpgradeSubscriptionPlan> get availableUpdatePlans;
  @override
  bool get isLoading;
  @override
  @JsonKey(ignore: true)
  _$$AccountPageStateImplCopyWith<_$AccountPageStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
