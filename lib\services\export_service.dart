import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:note_x/lib.dart';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// A service class for handling export operations
///
/// This service provides methods for exporting notes, flashcard sets,
/// and quiz sets in various formats.
class ExportService {
  /// Singleton instance
  static final ExportService _instance = ExportService._internal();

  /// Factory constructor to return the singleton instance
  factory ExportService() => _instance;

  /// Private constructor
  ExportService._internal();

  /// The note API service for making export requests
  final _noteApiService = GetIt.instance.get<CreateNoteApiServiceImpl>();

  /// Exports a note (summary or transcript)
  ///
  /// [format] - The format to export in (PDF, DOCX, etc.)
  /// [type] - The type of export (summary, transcript, etc.)
  /// [sharePosition] - The position to show the share dialog
  /// [noteBackendId] - The backend ID of the note
  /// [noteTitle] - The title of the note
  Future<void> exportNote(
    ExportNoteFormat format,
    ExportType type,
    Rect? sharePosition,
    String noteBackendId,
    String noteTitle,
  ) async {
    try {
      // Show loading dialog
      CommonDialogs.showLoadingDialog();

      if (noteBackendId.isEmpty) {
        CommonDialogs.closeLoading();
        return;
      }

      NoteExportDto? noteDto;
      noteDto = await _noteApiService.exportNote(
        type.displayType,
        noteBackendId,
        format.displayFormat,
      );

      final noteUrl = noteDto.url ?? '';
      if (noteUrl.isEmpty) {
        CommonDialogs.closeLoading();
        CommonDialogs.showToast(S.current.no_url_provided,
            gravity: ToastGravity.BOTTOM, length: Toast.LENGTH_LONG);
        return;
      }

      await _downloadAndShareFile(
        noteUrl,
        noteBackendId,
        format.displayFormat,
        sharePosition,
      );

      CommonDialogs.closeLoading();
    } catch (e) {
      CommonDialogs.closeLoading();
      _handleExportError(e);
      if (e is AppErrorException) {
        GetIt.instance.get<AppCubit>().emitError(e.error_key);
      }
    }
  }

  /// Exports a flashcard set
  ///
  /// [format] - The format to export in (PDF, DOCX, etc.)
  /// [sharePosition] - The position to show the share dialog
  /// [titleName] - The title of the flashcard set
  /// [setId] - The ID of the flashcard set
  /// [noteId] - The ID of the note containing the flashcard set
  Future<void> exportFlashcardSet(
    ExportNoteFormat format,
    Rect? sharePosition,
    String titleName,
    String setId,
    String noteId,
  ) async {
    try {
      // Show loading dialog
      CommonDialogs.showLoadingDialog();

      // Get flashcard set info from local storage
      if (setId.isEmpty) {
        CommonDialogs.closeLoading();
        CommonDialogs.showToast(
          S.current.flashcard_set_not_found,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      ExportSetDto? exportSetDto;
      exportSetDto = await _noteApiService.exportFlashcardSet(
        setId,
        format.displayFormat,
        noteId,
      );

      final setUrl = exportSetDto.url ?? '';
      if (setUrl.isEmpty) {
        CommonDialogs.closeLoading();
        CommonDialogs.showToast(
          S.current.no_url_provided,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      await _downloadAndShareFile(
        setUrl,
        setId,
        format.displayFormat,
        sharePosition,
      );

      CommonDialogs.closeLoading();
    } catch (e) {
      CommonDialogs.closeLoading();
      _handleExportError(e);
      if (e is AppErrorException) {
        GetIt.instance.get<AppCubit>().emitError(e.error_key);
      }
    }
  }

  /// Exports a quiz set
  ///
  /// [format] - The format to export in (PDF, DOCX, etc.)
  /// [sharePosition] - The position to show the share dialog
  /// [titleName] - The title of the quiz set
  /// [setId] - The ID of the quiz set
  /// [noteId] - The ID of the note containing the quiz set
  Future<void> exportQuizSet(
    ExportNoteFormat format,
    Rect? sharePosition,
    String titleName,
    String setId,
    String noteId,
  ) async {
    try {
      // Show loading dialog
      CommonDialogs.showLoadingDialog();

      // Get quiz set info from local storage
      if (setId.isEmpty) {
        CommonDialogs.closeLoading();
        CommonDialogs.showToast(
          S.current.quiz_set_not_found,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      ExportSetDto? exportSetDto;
      exportSetDto = await _noteApiService.exportQuizSet(
        setId,
        format.displayFormat,
        noteId,
      );

      final setUrl = exportSetDto.url ?? '';
      if (setUrl.isEmpty) {
        CommonDialogs.closeLoading();
        CommonDialogs.showToast(
          S.current.no_url_provided,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
        return;
      }

      await _downloadAndShareFile(
        setUrl,
        setId,
        format.displayFormat,
        sharePosition,
      );

      CommonDialogs.closeLoading();
    } catch (e) {
      CommonDialogs.closeLoading();
      _handleExportError(e);
      if (e is AppErrorException) {
        GetIt.instance.get<AppCubit>().emitError(e.error_key);
      }
    }
  }

  /// Downloads and shares a file
  ///
  /// [url] - The URL of the file to download
  /// [id] - The ID to use in the file name
  /// [format] - The format of the file
  /// [sharePosition] - The position to show the share dialog
  Future<void> _downloadAndShareFile(
    String url,
    String id,
    String format,
    Rect? sharePosition,
  ) async {
    try {
      // Download and share file
      final tempDir = await getTemporaryDirectory();
      final saveFileName = '$id.$format';
      final tempFilePath = path.join(tempDir.path, saveFileName);
      final file = File(tempFilePath);

      final response =
          await http.get(Uri.parse(url)).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        try {
          // Write file
          await file.writeAsBytes(response.bodyBytes);

          // Verify file exists and is readable
          if (!await file.exists()) {
            throw const FileSystemException('Failed to save file');
          }

          // Close loading before showing share dialog
          CommonDialogs.closeLoading();

          // Share file
          await ShareService().shareFile(
            filePath: file.path,
            sharePositionOrigin: sharePosition,
          );
        } catch (e) {
          CommonDialogs.showToast(
            S.current.export_failed,
            gravity: ToastGravity.BOTTOM,
            length: Toast.LENGTH_LONG,
          );
        } finally {
          // Clean up temp file if it exists
          try {
            if (await file.exists()) {
              await file.delete();
            }
          } catch (e) {
            debugPrint('Failed to delete temporary file: $e');
          }
        }
      } else {
        CommonDialogs.showToast(
          S.current.export_failed,
          gravity: ToastGravity.BOTTOM,
          length: Toast.LENGTH_LONG,
        );
      }
    } catch (e) {
      _handleExportError(e);
    }
  }

  /// Handles export errors
  ///
  /// [error] - The error that occurred
  void _handleExportError(dynamic error) {
    if (error is AppErrorException) {
      CommonDialogs.showToast(
        error.message ?? S.current.no_generated,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
      GetIt.instance.get<AppCubit>().emitError(error.error_key);
    } else if (error is HttpException) {
      CommonDialogs.showToast(
        S.current.http_failed,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    } else {
      debugPrint('Failed export file: $error');
      CommonDialogs.showToast(
        S.current.export_failed,
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_LONG,
      );
    }
  }
}
