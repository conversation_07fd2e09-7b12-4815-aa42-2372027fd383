// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'background_video_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

BackgroundVideoDto _$BackgroundVideoDtoFromJson(Map<String, dynamic> json) {
  return _BackgroundVideoDto.fromJson(json);
}

/// @nodoc
mixin _$BackgroundVideoDto {
  @JsonKey(name: 'bg_id')
  String get backgroundId => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  int get width => throw _privateConstructorUsedError;
  int get height => throw _privateConstructorUsedError;
  @JsonKey(name: 'thumbnail_url')
  String get thumbnailUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'video_url')
  String get videoUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BackgroundVideoDtoCopyWith<BackgroundVideoDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackgroundVideoDtoCopyWith<$Res> {
  factory $BackgroundVideoDtoCopyWith(
          BackgroundVideoDto value, $Res Function(BackgroundVideoDto) then) =
      _$BackgroundVideoDtoCopyWithImpl<$Res, BackgroundVideoDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'bg_id') String backgroundId,
      String title,
      int width,
      int height,
      @JsonKey(name: 'thumbnail_url') String thumbnailUrl,
      @JsonKey(name: 'video_url') String videoUrl});
}

/// @nodoc
class _$BackgroundVideoDtoCopyWithImpl<$Res, $Val extends BackgroundVideoDto>
    implements $BackgroundVideoDtoCopyWith<$Res> {
  _$BackgroundVideoDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backgroundId = null,
    Object? title = null,
    Object? width = null,
    Object? height = null,
    Object? thumbnailUrl = null,
    Object? videoUrl = null,
  }) {
    return _then(_value.copyWith(
      backgroundId: null == backgroundId
          ? _value.backgroundId
          : backgroundId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      videoUrl: null == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BackgroundVideoDtoImplCopyWith<$Res>
    implements $BackgroundVideoDtoCopyWith<$Res> {
  factory _$$BackgroundVideoDtoImplCopyWith(_$BackgroundVideoDtoImpl value,
          $Res Function(_$BackgroundVideoDtoImpl) then) =
      __$$BackgroundVideoDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'bg_id') String backgroundId,
      String title,
      int width,
      int height,
      @JsonKey(name: 'thumbnail_url') String thumbnailUrl,
      @JsonKey(name: 'video_url') String videoUrl});
}

/// @nodoc
class __$$BackgroundVideoDtoImplCopyWithImpl<$Res>
    extends _$BackgroundVideoDtoCopyWithImpl<$Res, _$BackgroundVideoDtoImpl>
    implements _$$BackgroundVideoDtoImplCopyWith<$Res> {
  __$$BackgroundVideoDtoImplCopyWithImpl(_$BackgroundVideoDtoImpl _value,
      $Res Function(_$BackgroundVideoDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backgroundId = null,
    Object? title = null,
    Object? width = null,
    Object? height = null,
    Object? thumbnailUrl = null,
    Object? videoUrl = null,
  }) {
    return _then(_$BackgroundVideoDtoImpl(
      backgroundId: null == backgroundId
          ? _value.backgroundId
          : backgroundId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      width: null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      videoUrl: null == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BackgroundVideoDtoImpl implements _BackgroundVideoDto {
  const _$BackgroundVideoDtoImpl(
      {@JsonKey(name: 'bg_id') this.backgroundId = '',
      this.title = '',
      this.width = 0,
      this.height = 0,
      @JsonKey(name: 'thumbnail_url') this.thumbnailUrl = '',
      @JsonKey(name: 'video_url') this.videoUrl = ''});

  factory _$BackgroundVideoDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$BackgroundVideoDtoImplFromJson(json);

  @override
  @JsonKey(name: 'bg_id')
  final String backgroundId;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final int width;
  @override
  @JsonKey()
  final int height;
  @override
  @JsonKey(name: 'thumbnail_url')
  final String thumbnailUrl;
  @override
  @JsonKey(name: 'video_url')
  final String videoUrl;

  @override
  String toString() {
    return 'BackgroundVideoDto(backgroundId: $backgroundId, title: $title, width: $width, height: $height, thumbnailUrl: $thumbnailUrl, videoUrl: $videoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackgroundVideoDtoImpl &&
            (identical(other.backgroundId, backgroundId) ||
                other.backgroundId == backgroundId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, backgroundId, title, width, height, thumbnailUrl, videoUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BackgroundVideoDtoImplCopyWith<_$BackgroundVideoDtoImpl> get copyWith =>
      __$$BackgroundVideoDtoImplCopyWithImpl<_$BackgroundVideoDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BackgroundVideoDtoImplToJson(
      this,
    );
  }
}

abstract class _BackgroundVideoDto implements BackgroundVideoDto {
  const factory _BackgroundVideoDto(
          {@JsonKey(name: 'bg_id') final String backgroundId,
          final String title,
          final int width,
          final int height,
          @JsonKey(name: 'thumbnail_url') final String thumbnailUrl,
          @JsonKey(name: 'video_url') final String videoUrl}) =
      _$BackgroundVideoDtoImpl;

  factory _BackgroundVideoDto.fromJson(Map<String, dynamic> json) =
      _$BackgroundVideoDtoImpl.fromJson;

  @override
  @JsonKey(name: 'bg_id')
  String get backgroundId;
  @override
  String get title;
  @override
  int get width;
  @override
  int get height;
  @override
  @JsonKey(name: 'thumbnail_url')
  String get thumbnailUrl;
  @override
  @JsonKey(name: 'video_url')
  String get videoUrl;
  @override
  @JsonKey(ignore: true)
  _$$BackgroundVideoDtoImplCopyWith<_$BackgroundVideoDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
