// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VoiceModelAdapter extends TypeAdapter<VoiceModel> {
  @override
  final int typeId = 13;

  @override
  VoiceModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VoiceModel(
      voiceId: fields[0] == null ? '' : fields[0] as String,
      voiceName: fields[1] == null ? '' : fields[1] as String,
      voiceGender: fields[2] == null ? '' : fields[2] as String,
      previewUrl: fields[3] == null ? '' : fields[3] as String,
      isCaptionSupport: fields[4] == null ? true : fields[4] as bool,
      voiceLanguage: fields[5] == null ? '' : fields[5] as String,
      displayLanguage: fields[6] == null ? '' : fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, VoiceModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.voiceId)
      ..writeByte(1)
      ..write(obj.voiceName)
      ..writeByte(2)
      ..write(obj.voiceGender)
      ..writeByte(3)
      ..write(obj.previewUrl)
      ..writeByte(4)
      ..write(obj.isCaptionSupport)
      ..writeByte(5)
      ..write(obj.voiceLanguage)
      ..writeByte(6)
      ..write(obj.displayLanguage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VoiceModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
