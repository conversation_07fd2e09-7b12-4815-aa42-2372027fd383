// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_sets_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

QuizSetsDto _$QuizSetsDtoFromJson(Map<String, dynamic> json) {
  return _QuizSetsDto.fromJson(json);
}

/// @nodoc
mixin _$QuizSetsDto {
  @JsonKey(name: 'set_id')
  String get setId => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'description')
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'difficulty')
  String get difficulty => throw _privateConstructorUsedError;
  @JsonKey(name: 'language')
  String get language => throw _privateConstructorUsedError;
  @JsonKey(name: 'questions_count')
  int get questionsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'questions')
  List<QuizDetailDto> get questions => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  String get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  String get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizSetsDtoCopyWith<QuizSetsDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizSetsDtoCopyWith<$Res> {
  factory $QuizSetsDtoCopyWith(
          QuizSetsDto value, $Res Function(QuizSetsDto) then) =
      _$QuizSetsDtoCopyWithImpl<$Res, QuizSetsDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'difficulty') String difficulty,
      @JsonKey(name: 'language') String language,
      @JsonKey(name: 'questions_count') int questionsCount,
      @JsonKey(name: 'questions') List<QuizDetailDto> questions,
      @JsonKey(name: 'created_at') String createdAt,
      @JsonKey(name: 'updated_at') String updatedAt});
}

/// @nodoc
class _$QuizSetsDtoCopyWithImpl<$Res, $Val extends QuizSetsDto>
    implements $QuizSetsDtoCopyWith<$Res> {
  _$QuizSetsDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? name = null,
    Object? description = null,
    Object? difficulty = null,
    Object? language = null,
    Object? questionsCount = null,
    Object? questions = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      questionsCount: null == questionsCount
          ? _value.questionsCount
          : questionsCount // ignore: cast_nullable_to_non_nullable
              as int,
      questions: null == questions
          ? _value.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<QuizDetailDto>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizSetsDtoImplCopyWith<$Res>
    implements $QuizSetsDtoCopyWith<$Res> {
  factory _$$QuizSetsDtoImplCopyWith(
          _$QuizSetsDtoImpl value, $Res Function(_$QuizSetsDtoImpl) then) =
      __$$QuizSetsDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'difficulty') String difficulty,
      @JsonKey(name: 'language') String language,
      @JsonKey(name: 'questions_count') int questionsCount,
      @JsonKey(name: 'questions') List<QuizDetailDto> questions,
      @JsonKey(name: 'created_at') String createdAt,
      @JsonKey(name: 'updated_at') String updatedAt});
}

/// @nodoc
class __$$QuizSetsDtoImplCopyWithImpl<$Res>
    extends _$QuizSetsDtoCopyWithImpl<$Res, _$QuizSetsDtoImpl>
    implements _$$QuizSetsDtoImplCopyWith<$Res> {
  __$$QuizSetsDtoImplCopyWithImpl(
      _$QuizSetsDtoImpl _value, $Res Function(_$QuizSetsDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? name = null,
    Object? description = null,
    Object? difficulty = null,
    Object? language = null,
    Object? questionsCount = null,
    Object? questions = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$QuizSetsDtoImpl(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      questionsCount: null == questionsCount
          ? _value.questionsCount
          : questionsCount // ignore: cast_nullable_to_non_nullable
              as int,
      questions: null == questions
          ? _value._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<QuizDetailDto>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizSetsDtoImpl implements _QuizSetsDto {
  const _$QuizSetsDtoImpl(
      {@JsonKey(name: 'set_id') this.setId = '',
      @JsonKey(name: 'name') this.name = '',
      @JsonKey(name: 'description') this.description = '',
      @JsonKey(name: 'difficulty') this.difficulty = '',
      @JsonKey(name: 'language') this.language = '',
      @JsonKey(name: 'questions_count') this.questionsCount = 0,
      @JsonKey(name: 'questions')
      final List<QuizDetailDto> questions = const [],
      @JsonKey(name: 'created_at') this.createdAt = '',
      @JsonKey(name: 'updated_at') this.updatedAt = ''})
      : _questions = questions;

  factory _$QuizSetsDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizSetsDtoImplFromJson(json);

  @override
  @JsonKey(name: 'set_id')
  final String setId;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'difficulty')
  final String difficulty;
  @override
  @JsonKey(name: 'language')
  final String language;
  @override
  @JsonKey(name: 'questions_count')
  final int questionsCount;
  final List<QuizDetailDto> _questions;
  @override
  @JsonKey(name: 'questions')
  List<QuizDetailDto> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  @override
  @JsonKey(name: 'created_at')
  final String createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final String updatedAt;

  @override
  String toString() {
    return 'QuizSetsDto(setId: $setId, name: $name, description: $description, difficulty: $difficulty, language: $language, questionsCount: $questionsCount, questions: $questions, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizSetsDtoImpl &&
            (identical(other.setId, setId) || other.setId == setId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.questionsCount, questionsCount) ||
                other.questionsCount == questionsCount) &&
            const DeepCollectionEquality()
                .equals(other._questions, _questions) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      setId,
      name,
      description,
      difficulty,
      language,
      questionsCount,
      const DeepCollectionEquality().hash(_questions),
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizSetsDtoImplCopyWith<_$QuizSetsDtoImpl> get copyWith =>
      __$$QuizSetsDtoImplCopyWithImpl<_$QuizSetsDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizSetsDtoImplToJson(
      this,
    );
  }
}

abstract class _QuizSetsDto implements QuizSetsDto {
  const factory _QuizSetsDto(
      {@JsonKey(name: 'set_id') final String setId,
      @JsonKey(name: 'name') final String name,
      @JsonKey(name: 'description') final String description,
      @JsonKey(name: 'difficulty') final String difficulty,
      @JsonKey(name: 'language') final String language,
      @JsonKey(name: 'questions_count') final int questionsCount,
      @JsonKey(name: 'questions') final List<QuizDetailDto> questions,
      @JsonKey(name: 'created_at') final String createdAt,
      @JsonKey(name: 'updated_at') final String updatedAt}) = _$QuizSetsDtoImpl;

  factory _QuizSetsDto.fromJson(Map<String, dynamic> json) =
      _$QuizSetsDtoImpl.fromJson;

  @override
  @JsonKey(name: 'set_id')
  String get setId;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'description')
  String get description;
  @override
  @JsonKey(name: 'difficulty')
  String get difficulty;
  @override
  @JsonKey(name: 'language')
  String get language;
  @override
  @JsonKey(name: 'questions_count')
  int get questionsCount;
  @override
  @JsonKey(name: 'questions')
  List<QuizDetailDto> get questions;
  @override
  @JsonKey(name: 'created_at')
  String get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  String get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$QuizSetsDtoImplCopyWith<_$QuizSetsDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
