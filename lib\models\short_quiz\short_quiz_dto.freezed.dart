// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'short_quiz_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ShortQuizDto _$ShortQuizDtoFromJson(Map<String, dynamic> json) {
  return _ShortQuizDto.fromJson(json);
}

/// @nodoc
mixin _$ShortQuizDto {
  @JsonKey(name: 'note_id')
  String get noteId => throw _privateConstructorUsedError;
  @JsonKey(name: 'quiz_video_url')
  String get quizVideoUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'task_id')
  String get taskId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'redis_data')
  String get redisData => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShortQuizDtoCopyWith<ShortQuizDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShortQuizDtoCopyWith<$Res> {
  factory $ShortQuizDtoCopyWith(
          ShortQuizDto value, $Res Function(ShortQuizDto) then) =
      _$ShortQuizDtoCopyWithImpl<$Res, ShortQuizDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'note_id') String noteId,
      @JsonKey(name: 'quiz_video_url') String quizVideoUrl,
      @JsonKey(name: 'task_id') String taskId,
      @JsonKey(name: 'user_id') String userId,
      @JsonKey(name: 'redis_data') String redisData,
      int duration});
}

/// @nodoc
class _$ShortQuizDtoCopyWithImpl<$Res, $Val extends ShortQuizDto>
    implements $ShortQuizDtoCopyWith<$Res> {
  _$ShortQuizDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noteId = null,
    Object? quizVideoUrl = null,
    Object? taskId = null,
    Object? userId = null,
    Object? redisData = null,
    Object? duration = null,
  }) {
    return _then(_value.copyWith(
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      quizVideoUrl: null == quizVideoUrl
          ? _value.quizVideoUrl
          : quizVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      redisData: null == redisData
          ? _value.redisData
          : redisData // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShortQuizDtoImplCopyWith<$Res>
    implements $ShortQuizDtoCopyWith<$Res> {
  factory _$$ShortQuizDtoImplCopyWith(
          _$ShortQuizDtoImpl value, $Res Function(_$ShortQuizDtoImpl) then) =
      __$$ShortQuizDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'note_id') String noteId,
      @JsonKey(name: 'quiz_video_url') String quizVideoUrl,
      @JsonKey(name: 'task_id') String taskId,
      @JsonKey(name: 'user_id') String userId,
      @JsonKey(name: 'redis_data') String redisData,
      int duration});
}

/// @nodoc
class __$$ShortQuizDtoImplCopyWithImpl<$Res>
    extends _$ShortQuizDtoCopyWithImpl<$Res, _$ShortQuizDtoImpl>
    implements _$$ShortQuizDtoImplCopyWith<$Res> {
  __$$ShortQuizDtoImplCopyWithImpl(
      _$ShortQuizDtoImpl _value, $Res Function(_$ShortQuizDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? noteId = null,
    Object? quizVideoUrl = null,
    Object? taskId = null,
    Object? userId = null,
    Object? redisData = null,
    Object? duration = null,
  }) {
    return _then(_$ShortQuizDtoImpl(
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      quizVideoUrl: null == quizVideoUrl
          ? _value.quizVideoUrl
          : quizVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      redisData: null == redisData
          ? _value.redisData
          : redisData // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShortQuizDtoImpl implements _ShortQuizDto {
  const _$ShortQuizDtoImpl(
      {@JsonKey(name: 'note_id') this.noteId = '',
      @JsonKey(name: 'quiz_video_url') this.quizVideoUrl = '',
      @JsonKey(name: 'task_id') this.taskId = '',
      @JsonKey(name: 'user_id') this.userId = '',
      @JsonKey(name: 'redis_data') this.redisData = '',
      this.duration = 0});

  factory _$ShortQuizDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShortQuizDtoImplFromJson(json);

  @override
  @JsonKey(name: 'note_id')
  final String noteId;
  @override
  @JsonKey(name: 'quiz_video_url')
  final String quizVideoUrl;
  @override
  @JsonKey(name: 'task_id')
  final String taskId;
  @override
  @JsonKey(name: 'user_id')
  final String userId;
  @override
  @JsonKey(name: 'redis_data')
  final String redisData;
  @override
  @JsonKey()
  final int duration;

  @override
  String toString() {
    return 'ShortQuizDto(noteId: $noteId, quizVideoUrl: $quizVideoUrl, taskId: $taskId, userId: $userId, redisData: $redisData, duration: $duration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortQuizDtoImpl &&
            (identical(other.noteId, noteId) || other.noteId == noteId) &&
            (identical(other.quizVideoUrl, quizVideoUrl) ||
                other.quizVideoUrl == quizVideoUrl) &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.redisData, redisData) ||
                other.redisData == redisData) &&
            (identical(other.duration, duration) ||
                other.duration == duration));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, noteId, quizVideoUrl, taskId, userId, redisData, duration);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortQuizDtoImplCopyWith<_$ShortQuizDtoImpl> get copyWith =>
      __$$ShortQuizDtoImplCopyWithImpl<_$ShortQuizDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShortQuizDtoImplToJson(
      this,
    );
  }
}

abstract class _ShortQuizDto implements ShortQuizDto {
  const factory _ShortQuizDto(
      {@JsonKey(name: 'note_id') final String noteId,
      @JsonKey(name: 'quiz_video_url') final String quizVideoUrl,
      @JsonKey(name: 'task_id') final String taskId,
      @JsonKey(name: 'user_id') final String userId,
      @JsonKey(name: 'redis_data') final String redisData,
      final int duration}) = _$ShortQuizDtoImpl;

  factory _ShortQuizDto.fromJson(Map<String, dynamic> json) =
      _$ShortQuizDtoImpl.fromJson;

  @override
  @JsonKey(name: 'note_id')
  String get noteId;
  @override
  @JsonKey(name: 'quiz_video_url')
  String get quizVideoUrl;
  @override
  @JsonKey(name: 'task_id')
  String get taskId;
  @override
  @JsonKey(name: 'user_id')
  String get userId;
  @override
  @JsonKey(name: 'redis_data')
  String get redisData;
  @override
  int get duration;
  @override
  @JsonKey(ignore: true)
  _$$ShortQuizDtoImplCopyWith<_$ShortQuizDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
