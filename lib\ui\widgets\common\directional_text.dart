import 'package:flutter/material.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/utils/rtl_utils.dart';

/// A widget that automatically handles text direction based on language or content
class DirectionalText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final bool? softWrap;
  final TextOverflow? overflow;
  final String? languageCode;
  final bool detectFromContent;

  const DirectionalText({
    Key? key,
    required this.text,
    this.style,
    this.textAlign,
    this.maxLines,
    this.softWrap,
    this.overflow,
    this.languageCode,
    this.detectFromContent = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine text direction
    TextDirection textDirection;
    
    if (languageCode != null) {
      // If language code is provided, use it to determine direction
      textDirection = RtlUtils.getTextDirectionForLanguage(languageCode!);
    } else if (detectFromContent) {
      // If detectFromContent is true, analyze the text content
      textDirection = RtlUtils.getTextDirectionForContent(text);
    } else {
      // Default to the ambient directionality
      textDirection = Directionality.of(context);
    }

    // Calculate the appropriate text alignment if not explicitly provided
    TextAlign effectiveTextAlign = textAlign ?? 
        (textDirection == TextDirection.rtl ? TextAlign.right : TextAlign.left);

    return Directionality(
      textDirection: textDirection,
      child: Text(
        text,
        style: style,
        textAlign: effectiveTextAlign,
        maxLines: maxLines,
        softWrap: softWrap,
        overflow: overflow,
      ),
    );
  }
}
