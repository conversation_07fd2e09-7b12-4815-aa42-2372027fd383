import 'package:get_it/get_it.dart';
import 'package:flutter/foundation.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/slide_show_create/cubit/slide_show_create_cubit_state.dart';

class SlideShowCreateCubit extends BaseCubit<SlideShowCreateCubitState> {
  SlideShowCreateCubit(super.initialState);
  final _slideShowService = GetIt.instance.get<SlideShowApiServiceImpl>();
  final _taskResultApiService = GetIt.instance.get<ResultTaskApiServiceImpl>();
  final _creditApiServiceImpl = GetIt.instance.get<CreditApiServiceImpl>();
  final _localService = GetIt.instance.get<LocalService>();
  void updateCardCount({
    String? cardCount,
  }) {
    emit(state.copyWith(cardCount: cardCount ?? state.cardCount));
  }

  void updateAdvancedMode(bool value) {
    emit(state.copyWith(isAdvancedMode: value));
  }

  void selectTemplate(int index) {
    emit(state.copyWith(selectedTemplateIndex: index));
  }

  void createSlideShow({
    required String noteId,
    required String cardCount,
    required String templateId,
  }) async {
    final note = HiveService().noteBox.get(noteId);
    if (note == null) return;
    final noteUpdate = note.copyWith(isGeneratingSlide: true);
    await HiveService().createOrUpdateNote(noteId, noteUpdate);
    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final taskId = await _slideShowService.getTaskIdForSlideShow(
          noteId: note.backendNoteId.isNotEmpty ? note.backendNoteId : noteId,
          templateId: templateId,
          slideCountRange: cardCount,
        );
        await _taskResultApiService.getTaskResultV2(
          taskId: taskId,
          localNoteId: noteId,
          isCommunityNote: false,
          isSlideShow: true,
        );
        _refreshUserCredits();
        _logAnalyticsEvents(
          cardCount: cardCount,
          templateId: templateId,
        );
      },
      doOnError: (exception) => Future(() async {
        AnalyticsService.logAnalyticsEventNoParam(
          eventName: EventName.slide_show_create_advanced_fail,
        );
        final failedNote = note.copyWith(
          isGeneratingSlide: false,
        );
        await HiveService().createOrUpdateNote(noteId, failedNote);
      }),
    );
  }

  void getSlideTemplates() async {
    return runCubitCatching(
      handleLoading: false,
      action: () async {
        final slideTemplates =
            await _slideShowService.getThumbnailForSlideShow();
        emit(state.copyWith(slideTemplates: slideTemplates));
      },
      doOnError: (exception) => Future(() async {
        emit(state.copyWith(slideTemplates: []));
      }),
    );
  }

  void _refreshUserCredits() async {
    final creditInfo = await _creditApiServiceImpl.fetchDataCredit();
    final user = appCubit.getAppUser().copyWith(
        youtubeCredit: creditInfo.youtubeCredit,
        webCredit: creditInfo.webCredit,
        audioCredit: creditInfo.audioCredit,
        rewardCredits: creditInfo.rewardCredits,
        shortsCredit: creditInfo.shortsCredit,
        documentCredit: creditInfo.documentCredit,
        slideCredit: creditInfo.slideCredit,
        userType: mapUserTypeStringToEnum(creditInfo.userType));
    appCubit.updateAppUser(user);
    _localService.saveAppUser(user);
  }

  void _logAnalyticsEvents({
    required String cardCount,
    required String templateId,
  }) {
    try {
      final analyticsEvents = [
        "${EventName.slide_show_choose_range_slide_click}_${cardCount.replaceAll(" ", "").replaceAll("-", "_")}",
        "${EventName.slide_show_Slide_template_click}_$templateId",
        EventName.slide_show_create_advanced_success,
      ];

      for (final event in analyticsEvents) {
        AnalyticsService.logAnalyticsEventNoParam(eventName: event);
      }
    } catch (e) {
      debugPrint('Error logging analytics events: $e');
    }
  }
}
