// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_export_pdf_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NoteExportPdfModelAdapter extends TypeAdapter<NoteExportPdfModel> {
  @override
  final int typeId = 14;

  @override
  NoteExportPdfModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NoteExportPdfModel(
      noteExportId: fields[0] == null ? '' : fields[0] as String,
      notePdfUrl: fields[1] == null ? '' : fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, NoteExportPdfModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.noteExportId)
      ..writeByte(1)
      ..write(obj.notePdfUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteExportPdfModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
