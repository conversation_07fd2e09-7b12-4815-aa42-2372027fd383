import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

/// Service for handling note sharing operations
class NoteSharingService {
  final CreateNoteApiService _noteApiService;
  final AppCubit _appCubit;
  
  NoteSharingService({
    CreateNoteApiService? noteApiService,
  }) : _noteApiService = noteApiService ?? GetIt.instance.get<CreateNoteApiServiceImpl>(),
       _appCubit = GetIt.instance.get<AppCubit>();
  
  /// Updates the sharing settings for a note
  /// 
  /// [localNoteId] - The local ID of the note
  /// [backendNoteId] - The backend ID of the note
  /// [currentNote] - The current note model
  /// [isPublic] - Whether to make the note public
  /// [isPasswordProtected] - Whether to password protect the note
  Future<void> updateNoteSharingSettings({
    required String localNoteId,
    required String backendNoteId,
    required NoteModel currentNote,
    bool? isPublic,
    bool? isPasswordProtected,
  }) async {
    try {
      // Show loading
      CommonDialogs.showLoadingDialog();
      
      // Make API call - BaseApiService will handle internet connection check
      final noteShareDto = await _noteApiService.updateNoteSharing(
        backendNoteId: backendNoteId,
        isUpdatePublicShare: isPublic != null,
        udpatedValue: isPublic ?? isPasswordProtected ?? false,
      );
      
      // Update note model
      final updatedNote = currentNote.copyWith(
        isPublic: noteShareDto.isPublic,
        isPasswordProtected: noteShareDto.isPasswordProtected,
        shareLink: noteShareDto.shareLink,
        sharePassword: noteShareDto.sharePassword,
      );
      
      // Save to local storage
      await HiveService().createOrUpdateNote(localNoteId, updatedNote);
    } catch (e) {
      debugPrint('Error updating note sharing settings: $e');
      if (e is AppErrorException) {
        _appCubit.emitError(e.error_key);
      }
    } finally {
      // Always hide loading
      CommonDialogs.closeLoading();
    }
  }
}