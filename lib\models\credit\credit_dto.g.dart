// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreditDtoImpl _$$CreditDtoImplFromJson(Map<String, dynamic> json) =>
    _$CreditDtoImpl(
      youtubeCredit: (json['youtubeCredit'] as num?)?.toInt() ?? 0,
      webCredit: (json['webCredit'] as num?)?.toInt() ?? 0,
      audioCredit: (json['audioCredit'] as num?)?.toInt() ?? 0,
      userType: json['user_type'] as String? ?? 'free',
      rewardCredits: (json['reward_credits'] as num?)?.toInt() ?? 0,
      shortsCredit: (json['shortsCredit'] as num?)?.toInt() ?? 0,
      slideCredit: (json['slideCredit'] as num?)?.toInt() ?? 0,
      documentCredit: (json['documentCredit'] as num?)?.toInt() ?? 0,
      activePurchasePlan: json['activePurchasePlan'] == null
          ? null
          : ActivePurchasePlan.fromJson(
              json['activePurchasePlan'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CreditDtoImplToJson(_$CreditDtoImpl instance) =>
    <String, dynamic>{
      'youtubeCredit': instance.youtubeCredit,
      'webCredit': instance.webCredit,
      'audioCredit': instance.audioCredit,
      'user_type': instance.userType,
      'reward_credits': instance.rewardCredits,
      'shortsCredit': instance.shortsCredit,
      'slideCredit': instance.slideCredit,
      'documentCredit': instance.documentCredit,
      'activePurchasePlan': instance.activePurchasePlan,
    };
