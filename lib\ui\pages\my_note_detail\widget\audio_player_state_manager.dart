import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

class AudioPlayerStateManager {
  static AudioPlayerStateManager? _instance;
  static AudioPlayerStateManager get instance => _instance ??= AudioPlayerStateManager._();

  AudioPlayerStateManager._();

  // Audio player instance
  AudioPlayer? _audioPlayer;

  // State notifiers
  final ValueNotifier<Duration> _audioDuration = ValueNotifier(Duration.zero);
  final ValueNotifier<Duration> _audioPosition = ValueNotifier(Duration.zero);
  final ValueNotifier<double> _playbackSpeed = ValueNotifier(1.0);
  final ValueNotifier<bool> _isDragging = ValueNotifier(false);
  final ValueNotifier<bool> _isPlaying = ValueNotifier(false);
  final ValueNotifier<bool> _isEditMode = ValueNotifier(false);

  // Stream subscriptions
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;

  // Current audio source
  String? _currentAudioSource;

  // Getters for state
  AudioPlayer? get audioPlayer => _audioPlayer;
  ValueNotifier<Duration> get audioDuration => _audioDuration;
  ValueNotifier<Duration> get audioPosition => _audioPosition;
  ValueNotifier<double> get playbackSpeed => _playbackSpeed;
  ValueNotifier<bool> get isDragging => _isDragging;
  ValueNotifier<bool> get isPlaying => _isPlaying;
  ValueNotifier<bool> get isEditMode => _isEditMode;

  /// Initialize audio player với audio source
  Future<void> initialize({
    required String audioFilePath,
    String? audioUrl,
  }) async {
    final audioSource = audioUrl ?? audioFilePath;

    if (_audioPlayer != null && _currentAudioSource == audioSource) {
      debugPrint('AudioPlayerStateManager: Already initialized with same source');
      return;
    }

    // Dispose existing player nếu có
    await dispose();

    _currentAudioSource = audioSource;
    _audioPlayer = AudioPlayer();

    try {
      await (audioUrl != null && audioUrl.isNotEmpty
          ? _audioPlayer!.setUrl(audioUrl)
          : _audioPlayer!.setFilePath(audioFilePath));

      final duration = _audioPlayer!.duration;
      _audioDuration.value = duration ?? Duration.zero;

      // Setup listeners
      _setupListeners();

      debugPrint('AudioPlayerStateManager: Initialized successfully');
    } catch (e) {
      debugPrint('AudioPlayerStateManager: Error initializing: $e');
      rethrow;
    }
  }

  void _setupListeners() {
    if (_audioPlayer == null) return;

    // Position stream
    _positionSubscription = _audioPlayer!.positionStream.listen((position) {
      if (!_isDragging.value) {
        _audioPosition.value = position;
      }
    });

    // Player state stream
    _playerStateSubscription = _audioPlayer!.playerStateStream.listen((state) {
      _isPlaying.value = state.playing;
    });
  }

  /// Play audio
  Future<void> play() async {
    await _audioPlayer?.play();
  }

  /// Pause audio
  Future<void> pause() async {
    await _audioPlayer?.pause();
  }

  /// Seek to position
  Future<void> seek(Duration position) async {
    await _audioPlayer?.seek(position);
  }

  /// Set playback speed
  Future<void> setSpeed(double speed) async {
    await _audioPlayer?.setSpeed(speed);
    _playbackSpeed.value = speed;
  }

  /// Set dragging state
  void setDragging(bool isDragging) {
    _isDragging.value = isDragging;
  }

  /// Update position manually (khi dragging)
  void updatePosition(Duration position) {
    _audioPosition.value = position;
  }

  /// Get current position
  Duration get currentPosition => _audioPosition.value;

  /// Get current duration
  Duration get currentDuration => _audioDuration.value;

  /// Check if same audio source
  bool isSameSource({required String audioFilePath, String? audioUrl}) {
    final audioSource = audioUrl ?? audioFilePath;
    return _currentAudioSource == audioSource;
  }

  /// Enter edit mode - pause audio and disable controls
  Future<void> enterEditMode() async {
    debugPrint('AudioPlayerStateManager: Entering edit mode');

    // Pause audio if playing
    if (_isPlaying.value && _audioPlayer != null) {
      await _audioPlayer!.pause();
      debugPrint('AudioPlayerStateManager: Audio paused for edit mode');
    }

    // Set edit mode flag
    _isEditMode.value = true;
  }

  /// Exit edit mode - re-enable controls
  void exitEditMode() {
    debugPrint('AudioPlayerStateManager: Exiting edit mode');
    _isEditMode.value = false;
  }

  /// Check if currently in edit mode
  bool get isInEditMode => _isEditMode.value;

  /// Dispose resources
  Future<void> dispose() async {
    await _positionSubscription?.cancel();
    await _playerStateSubscription?.cancel();
    await _audioPlayer?.dispose();

    _positionSubscription = null;
    _playerStateSubscription = null;
    _audioPlayer = null;
    _currentAudioSource = null;

    debugPrint('AudioPlayerStateManager: Disposed');
  }

  /// Reset to clean state
  void reset() {
    _audioDuration.value = Duration.zero;
    _audioPosition.value = Duration.zero;
    _playbackSpeed.value = 1.0;
    _isDragging.value = false;
    _isPlaying.value = false;
    _isEditMode.value = false;
  }
}
