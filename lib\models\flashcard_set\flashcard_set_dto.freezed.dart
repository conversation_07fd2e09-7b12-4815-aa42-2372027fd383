// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flashcard_set_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FlashcardSetDto _$FlashcardSetDtoFromJson(Map<String, dynamic> json) {
  return _FlashcardSetDto.fromJson(json);
}

/// @nodoc
mixin _$FlashcardSetDto {
  @JsonKey(name: 'set_id')
  String get setId => throw _privateConstructorUsedError;
  @JsonKey(name: 'note_id')
  String get noteId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'description')
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'difficulty')
  String get difficulty => throw _privateConstructorUsedError;
  @JsonKey(name: 'language')
  String get language => throw _privateConstructorUsedError;
  @JsonKey(name: 'cards_count')
  int get cardsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  String get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  String get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FlashcardSetDtoCopyWith<FlashcardSetDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlashcardSetDtoCopyWith<$Res> {
  factory $FlashcardSetDtoCopyWith(
          FlashcardSetDto value, $Res Function(FlashcardSetDto) then) =
      _$FlashcardSetDtoCopyWithImpl<$Res, FlashcardSetDto>;
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'note_id') String noteId,
      @JsonKey(name: 'user_id') String userId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'difficulty') String difficulty,
      @JsonKey(name: 'language') String language,
      @JsonKey(name: 'cards_count') int cardsCount,
      @JsonKey(name: 'created_at') String createdAt,
      @JsonKey(name: 'updated_at') String updatedAt});
}

/// @nodoc
class _$FlashcardSetDtoCopyWithImpl<$Res, $Val extends FlashcardSetDto>
    implements $FlashcardSetDtoCopyWith<$Res> {
  _$FlashcardSetDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? noteId = null,
    Object? userId = null,
    Object? name = null,
    Object? description = null,
    Object? difficulty = null,
    Object? language = null,
    Object? cardsCount = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      cardsCount: null == cardsCount
          ? _value.cardsCount
          : cardsCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlashcardSetDtoImplCopyWith<$Res>
    implements $FlashcardSetDtoCopyWith<$Res> {
  factory _$$FlashcardSetDtoImplCopyWith(_$FlashcardSetDtoImpl value,
          $Res Function(_$FlashcardSetDtoImpl) then) =
      __$$FlashcardSetDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'set_id') String setId,
      @JsonKey(name: 'note_id') String noteId,
      @JsonKey(name: 'user_id') String userId,
      @JsonKey(name: 'name') String name,
      @JsonKey(name: 'description') String description,
      @JsonKey(name: 'difficulty') String difficulty,
      @JsonKey(name: 'language') String language,
      @JsonKey(name: 'cards_count') int cardsCount,
      @JsonKey(name: 'created_at') String createdAt,
      @JsonKey(name: 'updated_at') String updatedAt});
}

/// @nodoc
class __$$FlashcardSetDtoImplCopyWithImpl<$Res>
    extends _$FlashcardSetDtoCopyWithImpl<$Res, _$FlashcardSetDtoImpl>
    implements _$$FlashcardSetDtoImplCopyWith<$Res> {
  __$$FlashcardSetDtoImplCopyWithImpl(
      _$FlashcardSetDtoImpl _value, $Res Function(_$FlashcardSetDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setId = null,
    Object? noteId = null,
    Object? userId = null,
    Object? name = null,
    Object? description = null,
    Object? difficulty = null,
    Object? language = null,
    Object? cardsCount = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$FlashcardSetDtoImpl(
      setId: null == setId
          ? _value.setId
          : setId // ignore: cast_nullable_to_non_nullable
              as String,
      noteId: null == noteId
          ? _value.noteId
          : noteId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      cardsCount: null == cardsCount
          ? _value.cardsCount
          : cardsCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FlashcardSetDtoImpl implements _FlashcardSetDto {
  const _$FlashcardSetDtoImpl(
      {@JsonKey(name: 'set_id') this.setId = '',
      @JsonKey(name: 'note_id') this.noteId = '',
      @JsonKey(name: 'user_id') this.userId = '',
      @JsonKey(name: 'name') this.name = '',
      @JsonKey(name: 'description') this.description = '',
      @JsonKey(name: 'difficulty') this.difficulty = '',
      @JsonKey(name: 'language') this.language = '',
      @JsonKey(name: 'cards_count') this.cardsCount = 0,
      @JsonKey(name: 'created_at') this.createdAt = '',
      @JsonKey(name: 'updated_at') this.updatedAt = ''});

  factory _$FlashcardSetDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FlashcardSetDtoImplFromJson(json);

  @override
  @JsonKey(name: 'set_id')
  final String setId;
  @override
  @JsonKey(name: 'note_id')
  final String noteId;
  @override
  @JsonKey(name: 'user_id')
  final String userId;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'difficulty')
  final String difficulty;
  @override
  @JsonKey(name: 'language')
  final String language;
  @override
  @JsonKey(name: 'cards_count')
  final int cardsCount;
  @override
  @JsonKey(name: 'created_at')
  final String createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final String updatedAt;

  @override
  String toString() {
    return 'FlashcardSetDto(setId: $setId, noteId: $noteId, userId: $userId, name: $name, description: $description, difficulty: $difficulty, language: $language, cardsCount: $cardsCount, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlashcardSetDtoImpl &&
            (identical(other.setId, setId) || other.setId == setId) &&
            (identical(other.noteId, noteId) || other.noteId == noteId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.cardsCount, cardsCount) ||
                other.cardsCount == cardsCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, setId, noteId, userId, name,
      description, difficulty, language, cardsCount, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FlashcardSetDtoImplCopyWith<_$FlashcardSetDtoImpl> get copyWith =>
      __$$FlashcardSetDtoImplCopyWithImpl<_$FlashcardSetDtoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FlashcardSetDtoImplToJson(
      this,
    );
  }
}

abstract class _FlashcardSetDto implements FlashcardSetDto {
  const factory _FlashcardSetDto(
          {@JsonKey(name: 'set_id') final String setId,
          @JsonKey(name: 'note_id') final String noteId,
          @JsonKey(name: 'user_id') final String userId,
          @JsonKey(name: 'name') final String name,
          @JsonKey(name: 'description') final String description,
          @JsonKey(name: 'difficulty') final String difficulty,
          @JsonKey(name: 'language') final String language,
          @JsonKey(name: 'cards_count') final int cardsCount,
          @JsonKey(name: 'created_at') final String createdAt,
          @JsonKey(name: 'updated_at') final String updatedAt}) =
      _$FlashcardSetDtoImpl;

  factory _FlashcardSetDto.fromJson(Map<String, dynamic> json) =
      _$FlashcardSetDtoImpl.fromJson;

  @override
  @JsonKey(name: 'set_id')
  String get setId;
  @override
  @JsonKey(name: 'note_id')
  String get noteId;
  @override
  @JsonKey(name: 'user_id')
  String get userId;
  @override
  @JsonKey(name: 'name')
  String get name;
  @override
  @JsonKey(name: 'description')
  String get description;
  @override
  @JsonKey(name: 'difficulty')
  String get difficulty;
  @override
  @JsonKey(name: 'language')
  String get language;
  @override
  @JsonKey(name: 'cards_count')
  int get cardsCount;
  @override
  @JsonKey(name: 'created_at')
  String get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  String get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$FlashcardSetDtoImplCopyWith<_$FlashcardSetDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
