// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_purchase_plan_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ActivePurchasePlanImpl _$$ActivePurchasePlanImplFromJson(
        Map<String, dynamic> json) =>
    _$ActivePurchasePlanImpl(
      productId: json['product_id'] as String?,
      priceInPurchaseCurrency:
          (json['price_in_purchased_currency'] as num?)?.toDouble() ?? -1,
      expirationAtMs: (json['expiration_at_ms'] as num?)?.toDouble() ?? 0.0,
      isTrial: json['is_trial'] as bool?,
    );

Map<String, dynamic> _$$ActivePurchasePlanImplToJson(
        _$ActivePurchasePlanImpl instance) =>
    <String, dynamic>{
      'product_id': instance.productId,
      'price_in_purchased_currency': instance.priceInPurchaseCurrency,
      'expiration_at_ms': instance.expirationAtMs,
      'is_trial': instance.isTrial,
    };
