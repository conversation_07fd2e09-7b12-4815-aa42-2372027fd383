import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:note_x/lib.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:get_it/get_it.dart';
import 'widgets/empty_state.dart';

class AudioLocalPage extends StatefulWidget {
  const AudioLocalPage({Key? key}) : super(key: key);

  @override
  State<AudioLocalPage> createState() => _AudioLocalPageState();
}

class _AudioLocalPageState extends State<AudioLocalPage> {
  List<FileSystemEntity> _recordings = [];
  bool _isLoadingRecordings = true;
  late AudioController _audioController;
  final AppCubit appCubit = GetIt.instance.get<AppCubit>();

  @override
  void initState() {
    super.initState();
    _audioController = AudioController();
    _loadRecordings();
  }

  Future<void> _loadRecordings() async {
    setState(() => _isLoadingRecordings = true);

    try {
      final directory = await getApplicationDocumentsDirectory();
      final recordDirectory = Directory(directory.path);

      if (await recordDirectory.exists()) {
        final files = await recordDirectory.list().toList();
        final audioFiles = files
            .whereType<File>()
            .where((file) => file.path.endsWith('.m4a'))
            .toList();

        audioFiles.sort((a, b) => File(b.path)
            .lastModifiedSync()
            .compareTo(File(a.path).lastModifiedSync()));

        setState(() => _recordings = audioFiles);
      } else {
        setState(() => _recordings = []);
      }
    } catch (e) {
      debugPrint(e.toString());
    } finally {
      setState(() => _isLoadingRecordings = false);
    }
  }

  @override
  void dispose() {
    _audioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorScheme.mainBackground,
      appBar: AppBarWidget(
        title: S.current.manage_recordings,
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    if (_isLoadingRecordings) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoActivityIndicator(color: context.colorScheme.mainBlue),
          ],
        ),
      );
    }

    return SafeArea(
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          CupertinoSliverRefreshControl(
            onRefresh: _loadRecordings,
          ),
          if (_recordings.isEmpty)
            const EmptyState()
          else
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final file = File(_recordings[index].path);
                    final fileName = file.path.split('/').last;
                    final bytes = file.lengthSync();
                    final kb = (bytes / 1000).round();
                    final fileSize = '$kb KB';
                    final lastModified =
                        DateFormat('E, d MMM').format(file.lastModifiedSync());
                    return Padding(
                      padding: EdgeInsets.only(bottom: 12.w),
                      child: ListenableBuilder(
                        listenable: _audioController,
                        builder: (context, _) {
                          return AudioItem(
                            file: file,
                            fileName: fileName,
                            lastModified: lastModified,
                            fileSize: fileSize,
                            controller: _audioController,
                            onCreateNote: _createNoteFromRecording,
                            onDelete: _promptDelete,
                            isInFailedBox: _isInFailedBox(file.path),
                            noteFail: _getNoteForFilePath(file.path),
                          );
                        },
                      ),
                    );
                  },
                  childCount: _recordings.length,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<bool> _confirmDelete(File file) async {
    final completer = Completer<bool>();
    showNewCupertinoDialog(
      context: context,
      title: S.current.delete_recording,
      image: Assets.icons.icMascottDelete,
      message: S.current.delete_recording_setting_confirmation,
      confirmButton: S.current.delete,
      cancelButton: S.current.cancel,
      onConfirm: () {
        completer.complete(true);
      },
      onCancel: () {
        completer.complete(false);
      },
    );

    return completer.future;
  }

  void _promptDelete(File file) async {
    final shouldDelete = await _confirmDelete(file);
    if (shouldDelete) {
      _deleteFile(file);
    }
  }

  void _deleteFile(File file) {
    try {
      if (_audioController.currentPlayingFile?.path == file.path) {
        _audioController.stop();
      }

      file.deleteSync();
      _loadRecordings();
    } catch (e) {
      CommonDialogs.showToast(S.current.failed_to_delete_recording);
    }
  }

  void _createNoteFromRecording(File file) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => UploadAudioPage(
          sharedMediaFile: SharedMediaFile(
            path: file.path,
            type: SharedMediaType.file,
          ),
        ),
      ),
    );
  }

  bool _isInFailedBox(String filePath) {
    final failedNotes = HiveService().noteFailedBox.values;
    return failedNotes.any((note) => note.audioFilePath == filePath);
  }

  NoteModel? _getNoteForFilePath(String filePath) {
    final failedNotes = HiveService().noteFailedBox.values;
    try {
      return failedNotes.firstWhere((note) => note.audioFilePath == filePath);
    } catch (e) {
      return null;
    }
  }
}
