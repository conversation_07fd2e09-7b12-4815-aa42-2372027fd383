// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_purchase_plan_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ActivePurchasePlan _$ActivePurchasePlanFromJson(Map<String, dynamic> json) {
  return _ActivePurchasePlan.fromJson(json);
}

/// @nodoc
mixin _$ActivePurchasePlan {
  @JsonKey(name: 'product_id')
  String? get productId => throw _privateConstructorUsedError;
  @JsonKey(name: 'price_in_purchased_currency')
  double get priceInPurchaseCurrency => throw _privateConstructorUsedError;
  @JsonKey(name: 'expiration_at_ms')
  double get expirationAtMs => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_trial')
  bool? get isTrial => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ActivePurchasePlanCopyWith<ActivePurchasePlan> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActivePurchasePlanCopyWith<$Res> {
  factory $ActivePurchasePlanCopyWith(
          ActivePurchasePlan value, $Res Function(ActivePurchasePlan) then) =
      _$ActivePurchasePlanCopyWithImpl<$Res, ActivePurchasePlan>;
  @useResult
  $Res call(
      {@JsonKey(name: 'product_id') String? productId,
      @JsonKey(name: 'price_in_purchased_currency')
      double priceInPurchaseCurrency,
      @JsonKey(name: 'expiration_at_ms') double expirationAtMs,
      @JsonKey(name: 'is_trial') bool? isTrial});
}

/// @nodoc
class _$ActivePurchasePlanCopyWithImpl<$Res, $Val extends ActivePurchasePlan>
    implements $ActivePurchasePlanCopyWith<$Res> {
  _$ActivePurchasePlanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? priceInPurchaseCurrency = null,
    Object? expirationAtMs = null,
    Object? isTrial = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      priceInPurchaseCurrency: null == priceInPurchaseCurrency
          ? _value.priceInPurchaseCurrency
          : priceInPurchaseCurrency // ignore: cast_nullable_to_non_nullable
              as double,
      expirationAtMs: null == expirationAtMs
          ? _value.expirationAtMs
          : expirationAtMs // ignore: cast_nullable_to_non_nullable
              as double,
      isTrial: freezed == isTrial
          ? _value.isTrial
          : isTrial // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActivePurchasePlanImplCopyWith<$Res>
    implements $ActivePurchasePlanCopyWith<$Res> {
  factory _$$ActivePurchasePlanImplCopyWith(_$ActivePurchasePlanImpl value,
          $Res Function(_$ActivePurchasePlanImpl) then) =
      __$$ActivePurchasePlanImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'product_id') String? productId,
      @JsonKey(name: 'price_in_purchased_currency')
      double priceInPurchaseCurrency,
      @JsonKey(name: 'expiration_at_ms') double expirationAtMs,
      @JsonKey(name: 'is_trial') bool? isTrial});
}

/// @nodoc
class __$$ActivePurchasePlanImplCopyWithImpl<$Res>
    extends _$ActivePurchasePlanCopyWithImpl<$Res, _$ActivePurchasePlanImpl>
    implements _$$ActivePurchasePlanImplCopyWith<$Res> {
  __$$ActivePurchasePlanImplCopyWithImpl(_$ActivePurchasePlanImpl _value,
      $Res Function(_$ActivePurchasePlanImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? priceInPurchaseCurrency = null,
    Object? expirationAtMs = null,
    Object? isTrial = freezed,
  }) {
    return _then(_$ActivePurchasePlanImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      priceInPurchaseCurrency: null == priceInPurchaseCurrency
          ? _value.priceInPurchaseCurrency
          : priceInPurchaseCurrency // ignore: cast_nullable_to_non_nullable
              as double,
      expirationAtMs: null == expirationAtMs
          ? _value.expirationAtMs
          : expirationAtMs // ignore: cast_nullable_to_non_nullable
              as double,
      isTrial: freezed == isTrial
          ? _value.isTrial
          : isTrial // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ActivePurchasePlanImpl implements _ActivePurchasePlan {
  const _$ActivePurchasePlanImpl(
      {@JsonKey(name: 'product_id') this.productId,
      @JsonKey(name: 'price_in_purchased_currency')
      this.priceInPurchaseCurrency = -1,
      @JsonKey(name: 'expiration_at_ms') this.expirationAtMs = 0.0,
      @JsonKey(name: 'is_trial') this.isTrial});

  factory _$ActivePurchasePlanImpl.fromJson(Map<String, dynamic> json) =>
      _$$ActivePurchasePlanImplFromJson(json);

  @override
  @JsonKey(name: 'product_id')
  final String? productId;
  @override
  @JsonKey(name: 'price_in_purchased_currency')
  final double priceInPurchaseCurrency;
  @override
  @JsonKey(name: 'expiration_at_ms')
  final double expirationAtMs;
  @override
  @JsonKey(name: 'is_trial')
  final bool? isTrial;

  @override
  String toString() {
    return 'ActivePurchasePlan(productId: $productId, priceInPurchaseCurrency: $priceInPurchaseCurrency, expirationAtMs: $expirationAtMs, isTrial: $isTrial)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActivePurchasePlanImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(
                    other.priceInPurchaseCurrency, priceInPurchaseCurrency) ||
                other.priceInPurchaseCurrency == priceInPurchaseCurrency) &&
            (identical(other.expirationAtMs, expirationAtMs) ||
                other.expirationAtMs == expirationAtMs) &&
            (identical(other.isTrial, isTrial) || other.isTrial == isTrial));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, productId, priceInPurchaseCurrency, expirationAtMs, isTrial);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ActivePurchasePlanImplCopyWith<_$ActivePurchasePlanImpl> get copyWith =>
      __$$ActivePurchasePlanImplCopyWithImpl<_$ActivePurchasePlanImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ActivePurchasePlanImplToJson(
      this,
    );
  }
}

abstract class _ActivePurchasePlan implements ActivePurchasePlan {
  const factory _ActivePurchasePlan(
          {@JsonKey(name: 'product_id') final String? productId,
          @JsonKey(name: 'price_in_purchased_currency')
          final double priceInPurchaseCurrency,
          @JsonKey(name: 'expiration_at_ms') final double expirationAtMs,
          @JsonKey(name: 'is_trial') final bool? isTrial}) =
      _$ActivePurchasePlanImpl;

  factory _ActivePurchasePlan.fromJson(Map<String, dynamic> json) =
      _$ActivePurchasePlanImpl.fromJson;

  @override
  @JsonKey(name: 'product_id')
  String? get productId;
  @override
  @JsonKey(name: 'price_in_purchased_currency')
  double get priceInPurchaseCurrency;
  @override
  @JsonKey(name: 'expiration_at_ms')
  double get expirationAtMs;
  @override
  @JsonKey(name: 'is_trial')
  bool? get isTrial;
  @override
  @JsonKey(ignore: true)
  _$$ActivePurchasePlanImplCopyWith<_$ActivePurchasePlanImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
