import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:note_x/lib.dart';
class NoteSharingHelper {
  static void showBottomSheet(BuildContext context, NoteModel note) {
    // Create a key for the sharing view to update it when the note changes
    final GlobalKey<UpdatableNoteSharingViewState> sharingViewKey =
        GlobalKey<UpdatableNoteSharingViewState>();

    // Create an instance of NoteSharingService
    final noteSharingService = NoteSharingService();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.colorScheme.mainNeutral,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.85,
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonText(
                      S.current.share_note_link,
                      style: TextStyle(
                        color: context.colorScheme.mainPrimary,
                        fontWeight: FontWeight.w600,
                        fontSize: context.isTablet ? 22 : 20.sp,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(
                        Assets.icons.icCloseWhite,
                        width: 24.w,
                        height: 24.h,
                        colorFilter: ColorFilter.mode(
                          context.colorScheme.mainGray,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                ValueListenableBuilder<Box<NoteModel>>(
                  valueListenable: HiveService().noteBox.listenable(),
                  builder: (context, box, _) {
                    final currentNote = box.get(note.id) ?? note;

                    if (sharingViewKey.currentState != null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        sharingViewKey.currentState?.updateNote(currentNote);
                      });
                    }

                    return UpdatableNoteSharingView(
                      key: sharingViewKey,
                      note: currentNote,
                      onPublicToggled: (isPublicToggled) async {
                        await noteSharingService.updateNoteSharingSettings(
                          localNoteId: note.id,
                          backendNoteId: note.backendNoteId,
                          currentNote: currentNote,
                          isPublic: isPublicToggled,
                        );
                      },
                      onPasswordToggled: (isPasswordToggled) async {
                        await noteSharingService.updateNoteSharingSettings(
                          localNoteId: note.id,
                          backendNoteId: note.backendNoteId,
                          currentNote: currentNote,
                          isPasswordProtected: isPasswordToggled,
                        );
                      },
                      onLinkCopied: () {
                        CommonDialogs.showToast(S.current.copied_to_clipboard);
                      },
                      onPasswordCopied: () {
                        CommonDialogs.showToast(S.current.copied_to_clipboard);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
