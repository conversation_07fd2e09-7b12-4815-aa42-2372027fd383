// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'suggestion_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SuggestionDto _$SuggestionDtoFromJson(Map<String, dynamic> json) {
  return _SuggestionDto.fromJson(json);
}

/// @nodoc
mixin _$SuggestionDto {
  String get question => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SuggestionDtoCopyWith<SuggestionDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SuggestionDtoCopyWith<$Res> {
  factory $SuggestionDtoCopyWith(
          SuggestionDto value, $Res Function(SuggestionDto) then) =
      _$SuggestionDtoCopyWithImpl<$Res, SuggestionDto>;
  @useResult
  $Res call({String question, String type, String difficulty});
}

/// @nodoc
class _$SuggestionDtoCopyWithImpl<$Res, $Val extends SuggestionDto>
    implements $SuggestionDtoCopyWith<$Res> {
  _$SuggestionDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? type = null,
    Object? difficulty = null,
  }) {
    return _then(_value.copyWith(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SuggestionDtoImplCopyWith<$Res>
    implements $SuggestionDtoCopyWith<$Res> {
  factory _$$SuggestionDtoImplCopyWith(
          _$SuggestionDtoImpl value, $Res Function(_$SuggestionDtoImpl) then) =
      __$$SuggestionDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String question, String type, String difficulty});
}

/// @nodoc
class __$$SuggestionDtoImplCopyWithImpl<$Res>
    extends _$SuggestionDtoCopyWithImpl<$Res, _$SuggestionDtoImpl>
    implements _$$SuggestionDtoImplCopyWith<$Res> {
  __$$SuggestionDtoImplCopyWithImpl(
      _$SuggestionDtoImpl _value, $Res Function(_$SuggestionDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? type = null,
    Object? difficulty = null,
  }) {
    return _then(_$SuggestionDtoImpl(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SuggestionDtoImpl implements _SuggestionDto {
  const _$SuggestionDtoImpl(
      {this.question = '', this.type = '', this.difficulty = ''});

  factory _$SuggestionDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$SuggestionDtoImplFromJson(json);

  @override
  @JsonKey()
  final String question;
  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final String difficulty;

  @override
  String toString() {
    return 'SuggestionDto(question: $question, type: $type, difficulty: $difficulty)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuggestionDtoImpl &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, question, type, difficulty);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SuggestionDtoImplCopyWith<_$SuggestionDtoImpl> get copyWith =>
      __$$SuggestionDtoImplCopyWithImpl<_$SuggestionDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SuggestionDtoImplToJson(
      this,
    );
  }
}

abstract class _SuggestionDto implements SuggestionDto {
  const factory _SuggestionDto(
      {final String question,
      final String type,
      final String difficulty}) = _$SuggestionDtoImpl;

  factory _SuggestionDto.fromJson(Map<String, dynamic> json) =
      _$SuggestionDtoImpl.fromJson;

  @override
  String get question;
  @override
  String get type;
  @override
  String get difficulty;
  @override
  @JsonKey(ignore: true)
  _$$SuggestionDtoImplCopyWith<_$SuggestionDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
